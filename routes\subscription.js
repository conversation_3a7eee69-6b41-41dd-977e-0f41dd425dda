const express = require('express');
const router = express.Router();
const { createClient } = require('@supabase/supabase-js');
const { isSuperAdmin } = require('../middleware/auth');

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// Get all subscription tiers (public)
router.get('/tiers', async (req, res) => {
    try {
        const { data: tiers, error } = await supabase
            .from('subscription_tiers')
            .select(`
                *,
                features:tier_features(
                    feature:subscription_features(
                        key,
                        name,
                        description
                    ),
                    value
                )
            `)
            .eq('deleted', false)
            .order('created_at');

        if (error) throw error;

        // Transform the data to a more friendly format
        const formattedTiers = tiers.map(tier => ({
            id: tier.id,
            name: tier.name,
            description: tier.description,
            chargebee_plan_id: tier.chargebee_plan_id,
            features: tier.features.reduce((acc, f) => ({
                ...acc,
                [f.feature.key]: {
                    name: f.feature.name,
                    description: f.feature.description,
                    ...f.value
                }
            }), {})
        }));

        res.json(formattedTiers);
    } catch (error) {
        console.error('Error fetching tiers:', error);
        res.status(500).json({ error: 'Failed to fetch subscription tiers' });
    }
});

// Get all features (superadmin only)
router.get('/features', isSuperAdmin, async (req, res) => {
    try {
        const { data: features, error } = await supabase
            .from('subscription_features')
            .select('*')
            .eq('deleted', false)
            .order('created_at');

        if (error) throw error;
        res.json(features);
    } catch (error) {
        console.error('Error fetching features:', error);
        res.status(500).json({ error: 'Failed to fetch features' });
    }
});

// Create a new feature (superadmin only)
router.post('/features', isSuperAdmin, async (req, res) => {
    const { name, description, key } = req.body;

    try {
        const { data, error } = await supabase
            .from('subscription_features')
            .insert([{ name, description, key }])
            .select()
            .single();

        if (error) throw error;
        res.status(201).json(data);
    } catch (error) {
        console.error('Error creating feature:', error);
        res.status(500).json({ error: 'Failed to create feature' });
    }
});

// Update a feature (superadmin only)
router.put('/features/:id', isSuperAdmin, async (req, res) => {
    const { id } = req.params;
    const { name, description } = req.body;

    try {
        const { data, error } = await supabase
            .from('subscription_features')
            .update({ name, description, updated_at: new Date() })
            .eq('id', id)
            .select()
            .single();

        if (error) throw error;
        res.json(data);
    } catch (error) {
        console.error('Error updating feature:', error);
        res.status(500).json({ error: 'Failed to update feature' });
    }
});

// Delete a feature (superadmin only)
router.delete('/features/:id', isSuperAdmin, async (req, res) => {
    const { id } = req.params;
    const { user } = req;

    try {
        const { error } = await supabase
            .from('subscription_features')
            .update({
                deleted: true,
                deleted_at: new Date(),
                deleted_by: user.id
            })
            .eq('id', id);

        if (error) throw error;
        res.status(204).send();
    } catch (error) {
        console.error('Error deleting feature:', error);
        res.status(500).json({ error: 'Failed to delete feature' });
    }
});

// Update tier features (superadmin only)
router.put('/tiers/:id/features', isSuperAdmin, async (req, res) => {
    const { id } = req.params;
    const { features } = req.body;

    try {
        // Start a transaction
        const { error: trxError } = await supabase.rpc('begin_transaction');
        if (trxError) throw trxError;

        // Delete existing features for this tier
        const { error: deleteError } = await supabase
            .from('tier_features')
            .delete()
            .eq('tier_id', id);

        if (deleteError) throw deleteError;

        // Insert new features
        const featureRows = Object.entries(features).map(([feature_id, value]) => ({
            tier_id: id,
            feature_id,
            value
        }));

        const { error: insertError } = await supabase
            .from('tier_features')
            .insert(featureRows);

        if (insertError) throw insertError;

        // Commit transaction
        const { error: commitError } = await supabase.rpc('commit_transaction');
        if (commitError) throw commitError;

        res.json({ message: 'Tier features updated successfully' });
    } catch (error) {
        // Rollback transaction
        await supabase.rpc('rollback_transaction');
        console.error('Error updating tier features:', error);
        res.status(500).json({ error: 'Failed to update tier features' });
    }
});

// Get company subscription
router.get('/companies/:id/subscription', async (req, res) => {
    const { id } = req.params;
    const { user } = req;

    try {
        // Check if user has access to this company
        const { data: userCompany, error: userError } = await supabase
            .from('user_companies')
            .select('role')
            .eq('user_id', user.id)
            .eq('company_id', id)
            .single();

        if (userError || !userCompany) {
            return res.status(403).json({ error: 'Not authorized to access this company' });
        }

        const { data: subscription, error } = await supabase
            .from('company_subscriptions')
            .select(`
                *,
                tier:subscription_tiers(
                    id,
                    name,
                    description,
                    features:tier_features(
                        feature:subscription_features(
                            key,
                            name,
                            description
                        ),
                        value
                    )
                )
            `)
            .eq('company_id', id)
            .single();

        if (error) throw error;

        // Transform the tier features into a more friendly format
        if (subscription?.tier) {
            subscription.tier.features = subscription.tier.features.reduce((acc, f) => ({
                ...acc,
                [f.feature.key]: {
                    name: f.feature.name,
                    description: f.feature.description,
                    ...f.value
                }
            }), {});
        }

        res.json(subscription);
    } catch (error) {
        console.error('Error fetching company subscription:', error);
        res.status(500).json({ error: 'Failed to fetch company subscription' });
    }
});

module.exports = router;
