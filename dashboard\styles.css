/*
 * VUM - Vurbis Uptime Monitor
 * Dashboard Styles
 */

/* Base Styles */
:root {
    /* Primary Colors - Based on Vurbis branding */
    --primary-color: #0056b3;
    --primary-dark: #004494;
    --primary-light: #3378c5;

    /* Secondary Colors */
    --secondary-color: #ff6b00;
    --secondary-dark: #e05e00;
    --secondary-light: #ff8c3a;

    /* Status Colors */
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;

    /* Neutral Colors */
    --dark: #343a40;
    --light: #f8f9fa;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;

    /* Layout */
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    --footer-height: 50px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);

    /* Transitions */
    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: var(--gray-800);
    background-color: var(--gray-100);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: color var(--transition-speed);
}

a:hover {
    color: var(--primary-dark);
}

button {
    cursor: pointer;
    border: none;
    background: none;
    font-family: inherit;
}

ul {
    list-style: none;
}

/* Layout */
.app-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--dark);
    color: white;
    display: flex;
    flex-direction: column;
    transition: width var(--transition-speed);
    position: fixed;
    height: 100vh;
    z-index: 100;
}

.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-icon {
    width: 30px;
    height: 30px;
    margin-right: 10px;
}

.sidebar-title {
    font-size: 20px;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
}

.sidebar-collapsed .sidebar-title {
    display: none;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul li {
    margin-bottom: 5px;
}

.sidebar-nav ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--gray-400);
    transition: all var(--transition-speed);
    border-left: 3px solid transparent;
}

.sidebar-nav ul li a i {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.sidebar-collapsed .sidebar-nav ul li a span {
    display: none;
}

.sidebar-nav ul li a:hover,
.sidebar-nav ul li.active a {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: var(--primary-color);
}

.sidebar-footer {
    padding: 15px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: space-between;
}

.sidebar-footer a {
    color: var(--gray-400);
    font-size: 12px;
    display: flex;
    align-items: center;
}

.sidebar-footer a i {
    margin-right: 5px;
}

.sidebar-collapsed .sidebar-footer a span {
    display: none;
}

.sidebar-footer a:hover {
    color: white;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-speed);
    display: flex;
    flex-direction: column;
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Header */
.header {
    height: var(--header-height);
    background-color: white;
    box-shadow: var(--box-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 99;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    margin-right: 20px;
    font-size: 20px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    transition: all var(--transition-speed);
}

.menu-toggle:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.header-logo {
    display: flex;
    align-items: center;
}

.header-logo img {
    height: 40px;
    margin-right: 10px;
}

.header-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
}

.header-title p {
    font-size: 12px;
    color: var(--gray-600);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-search {
    position: relative;
    margin-right: 20px;
}

.header-search input {
    width: 250px;
    height: 40px;
    padding: 0 40px 0 15px;
    border: 1px solid var(--gray-300);
    border-radius: 20px;
    background-color: var(--gray-100);
    transition: all var(--transition-speed);
}

.header-search input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
    width: 300px;
}

.header-search button {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
}

.header-notifications {
    margin-right: 20px;
    position: relative;
}

.notification-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--gray-600);
    transition: all var(--transition-speed);
}

.notification-btn:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background-color: var(--danger-color);
    color: white;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-user {
    display: flex;
    align-items: center;
}

.header-user .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 18px;
}

.header-user span {
    font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.dashboard-header h2 {
    font-size: 24px;
    font-weight: 600;
    color: var(--gray-800);
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-speed);
}

.btn i {
    margin-right: 5px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

.btn-secondary:hover {
    background-color: var(--gray-300);
}

.btn-text {
    color: var(--primary-color);
    padding: 0;
}

.btn-text:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all var(--transition-speed);
}

.btn-icon:hover {
    background-color: var(--gray-200);
    color: var(--gray-800);
}

/* Status Cards */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.status-card {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
}

.status-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    margin-right: 15px;
}

.status-card-icon.up {
    background-color: var(--success-color);
}

.status-card-icon.down {
    background-color: var(--danger-color);
}

.status-card-content h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 5px;
}

.status-card-content p {
    color: var(--gray-600);
    font-size: 14px;
}

/* Dashboard Sections */
.dashboard-section {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    border-bottom: 1px solid var(--gray-200);
}

.section-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Monitors Table */
.monitors-table {
    overflow-x: auto;
}

.monitors-table table {
    width: 100%;
    border-collapse: collapse;
}

.monitors-table th,
.monitors-table td {
    padding: 12px 20px;
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.monitors-table th {
    font-weight: 600;
    color: var(--gray-700);
    background-color: var(--gray-100);
}

.monitors-table tr:last-child td {
    border-bottom: none;
}

.monitors-table tr:hover td {
    background-color: var(--gray-50);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge i {
    margin-right: 4px;
}

.status-badge.up {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-badge.down {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.table-actions {
    display: flex;
    gap: 5px;
}

/* Charts Section */
.charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.chart-container {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.chart {
    height: 300px;
    padding: 20px;
}

.select-time-range {
    padding: 6px 12px;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: white;
    color: var(--gray-700);
}

/* Incidents List */
.incidents-list {
    padding: 20px;
}

.incident-card {
    display: flex;
    align-items: flex-start;
    padding: 15px;
    border-bottom: 1px solid var(--gray-200);
}

.incident-card:last-child {
    border-bottom: none;
}

.incident-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--success-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-right: 15px;
    flex-shrink: 0;
}

.incident-icon.down {
    background-color: var(--danger-color);
}

.incident-content {
    flex: 1;
}

.incident-content h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.incident-content p {
    color: var(--gray-600);
    font-size: 14px;
    margin-bottom: 10px;
}

.incident-meta {
    display: flex;
    gap: 15px;
}

.incident-meta span {
    font-size: 12px;
    color: var(--gray-600);
    display: flex;
    align-items: center;
}

.incident-meta span i {
    margin-right: 5px;
}

.incident-status {
    margin-left: 15px;
    flex-shrink: 0;
}

/* Footer */
.footer {
    height: var(--footer-height);
    background-color: white;
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    font-size: 12px;
    color: var(--gray-600);
}

.footer-right {
    display: flex;
    gap: 15px;
}

/* Responsive */
@media (max-width: 992px) {
    .sidebar {
        width: var(--sidebar-collapsed-width);
    }

    .sidebar-title {
        display: none;
    }

    .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }

    .header-search input {
        width: 200px;
    }

    .header-search input:focus {
        width: 250px;
    }

    .charts-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }

    .sidebar-title {
        display: block;
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .header-search {
        display: none;
    }

    .status-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .header-title p {
        display: none;
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .dashboard-actions {
        width: 100%;
        justify-content: space-between;
    }
}
