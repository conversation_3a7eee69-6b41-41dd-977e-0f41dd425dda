# Test Email Notifications

This document describes the implementation of the test email notification feature for superadmins in the Vurbis Uptime Monitor (VUM) application.

## Overview

The test email notification feature allows superadmins to send test emails for any monitor in the system. This is useful for verifying that email notifications are working correctly and for testing the appearance of different types of notifications (up, down, degraded).

## Implementation Details

### Database Components

1. **monitor_email_notifications Table**:
   - Stores email notification requests
   - Includes fields for monitor_id, company_id, status, and processing status
   - Has RLS policies to restrict access to superadmins and company members

2. **notify_email_service Trigger Function**:
   - Triggered when a new row is inserted into the monitor_email_notifications table
   - Sends a PostgreSQL notification to the email notification listener service

3. **superadmin_send_test_email Function**:
   - Allows superadmins to send test emails for any monitor
   - Creates a notification in the database
   - Inserts a row into the monitor_email_notifications table to trigger the email sending process
   - Has a p_force_email parameter to ensure emails are sent even if global email settings are disabled

### Backend Components

1. **email-notification-listener.js**:
   - Listens for PostgreSQL notifications
   - Processes email notification requests
   - Marks notifications as processed after sending emails
   - <PERSON>les errors and retries

2. **direct-email-sender.js**:
   - Sends emails using the Resend API
   - Formats email content based on monitor status
   - Sends emails to company admins and superadmins

### Frontend Components

1. **SuperadminTestNotification.tsx**:
   - Provides a user interface for superadmins to send test emails
   - Allows selection of any monitor and status (up, down, degraded)
   - Calls the superadmin_send_test_email function with p_force_email=true

## Usage

1. Log in as a superadmin
2. Navigate to Global Settings
3. Select the "Notifications" tab
4. Use the "Test Notification" card to:
   - Select a monitor from the dropdown
   - Choose the status (Up, Down, or Degraded)
   - Click "Send Test Notification"
5. Check that emails are received by all appropriate recipients

## Troubleshooting

If test emails are not being received:

1. Check the email-notification-listener.js logs for errors
2. Verify that the RESEND_API_KEY in the .env file is a valid production key (not a test key)
3. Check that the email-notification-listener service is running
4. Verify that the recipient email addresses are correct
5. Check if emails are being caught by spam filters

## Technical Notes

- The system uses a separate table and trigger for email notifications to ensure that emails are sent even if global email settings are disabled
- The p_force_email parameter in the superadmin_send_test_email function ensures that test emails are always sent
- The system marks notifications as processed after sending emails to avoid duplicate emails
