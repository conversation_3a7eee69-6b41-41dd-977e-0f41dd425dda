import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { useSubscription } from '@/hooks/use-subscription';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import DocumentTitle from '@/components/DocumentTitle';
import { SubscriptionTierComparison } from '@/components/SubscriptionTierComparison';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SubscriptionTierSettings } from '@/components/SubscriptionTierSettings';
import { CompanySubscriptionSettings } from '@/components/CompanySubscriptionSettings';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ArrowUpRight, Loader2, Layers } from 'lucide-react';

const SubscriptionPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const { useCompanySubscription } = useSubscription();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin } = useGlobalSuperadminQuery();

  const { data: subscription, isLoading } = useCompanySubscription();

  useEffect(() => {
    if (!user) {
      navigate('/login');
    }
  }, [user, navigate]);

  const header = (
    <UnifiedHeader
      title="Subscription"
      showCompanySelector={true}
      actions={
        isSuperadmin ? (
          <Button variant="outline" onClick={() => navigate('/subscription-tiers')}>
            <Layers className="h-4 w-4 mr-2" />
            Manage Tiers
          </Button>
        ) : undefined
      }
    />
  );

  if (!user || !currentCompany) {
    return (
      <AppLayout header={header}>
        <div className="flex justify-center items-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Subscription" />
      <div className="container mx-auto py-6 space-y-8">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Subscription</h1>
        </div>

        {isSuperadmin ? (
          <div className="space-y-8">
            <CompanySubscriptionSettings
              companyId={currentCompany.id}
              companyName={currentCompany.name}
            />

            <Separator />

            <SubscriptionTierComparison />

            <div className="flex justify-end">
              <Button onClick={() => navigate('/subscription-tiers')}>
                <Layers className="h-4 w-4 mr-2" />
                Manage All Subscription Tiers
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Current Subscription</CardTitle>
                    <CardDescription>Your company's current subscription plan</CardDescription>
                  </div>
                  {subscription && (
                    <Badge variant="outline" className="text-lg px-3 py-1">
                      {subscription.tier_name}
                    </Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : subscription ? (
                  <div className="space-y-6">
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium mb-1">Monitor Usage</h3>
                        <p className="text-2xl font-bold">
                          {subscription.current_monitors} / {subscription.max_monitors}
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {subscription.current_monitors >= subscription.max_monitors
                            ? 'Limit reached'
                            : `${subscription.max_monitors - subscription.current_monitors} monitors available`}
                        </p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium mb-1">History Retention</h3>
                        <p className="text-2xl font-bold">
                          {subscription.history_retention_days} days
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Monitoring data is stored for this period
                        </p>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h3 className="text-sm font-medium mb-2">Need more features?</h3>
                      <p className="text-muted-foreground">
                        Contact your administrator to upgrade your subscription plan for additional monitors,
                        longer data retention, and more features.
                      </p>
                    </div>
                  </div>
                ) : (
                  <p>No subscription information available.</p>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="ml-auto" onClick={() => navigate('/contact')}>
                  Contact Support
                  <ArrowUpRight className="ml-2 h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>

            <SubscriptionTierComparison />
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default SubscriptionPage;
