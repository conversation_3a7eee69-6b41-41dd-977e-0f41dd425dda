import { useToast } from "@/hooks/use-toast"
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { Button } from "@/components/ui/button"
import { Copy } from "lucide-react"
import { toast as showToast } from "@/hooks/use-toast"

export function Toaster() {
  const { toasts } = useToast()

  const handleCopy = (content: string) => {
    navigator.clipboard.writeText(content)
      .then(() => {
        showToast({
          title: "Copied to clipboard",
          description: "The error message has been copied to your clipboard.",
        })
      })
      .catch((err) => {
        console.error("Failed to copy text: ", err)
      })
  }

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant, ...props }) {
        // Prepare content to copy (title + description)
        const contentToCopy = `${title || ''} ${description || ''}`

        return (
          <Toast key={id} {...props} variant={variant}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            <div className="flex items-center gap-2">
              {action}
              {variant === 'destructive' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleCopy(contentToCopy)}
                  className="h-8 px-2 text-xs"
                >
                  <Copy className="h-3.5 w-3.5 mr-1" />
                  Copy
                </Button>
              )}
            </div>
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
