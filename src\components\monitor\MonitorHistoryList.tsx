import React, { Fragment } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Check<PERSON>ircle, XCircle, Clock, AlertTriangle, RefreshCw } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { formatTooltipDateTime, formatDate, formatTime } from "@/utils/dateFormat";

interface MonitorHistoryItem {
  id: string;
  status: boolean | string;
  response_time: number | null;
  error_message: string | null;
  timestamp: string;
}

interface MonitorHistoryListProps {
  history: MonitorHistoryItem[];
  isLoading: boolean;
  retentionDays: number;
  onRefresh: () => void;
}

const MonitorHistoryList: React.FC<MonitorHistoryListProps> = ({
  history,
  isLoading,
  retentionDays,
  onRefresh
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Check History</CardTitle>
        <CardDescription>
          Check history for the last {retentionDays} days
          {!isLoading && (
            <span className="block text-xs mt-1">
              Showing {history.length} checks
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-1/3" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : history.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No History Yet</h3>
            <p className="text-muted-foreground">
              This monitor doesn't have any check history yet. Checks will appear here as they are performed.
            </p>
          </div>
        ) : (
          <div className="space-y-4 max-h-[400px] overflow-y-auto pr-2">
            {history.map((item, index) => {
              // Check if the date has changed from the previous item
              const currentDate = new Date(item.timestamp).toDateString();
              const prevDate = index > 0 ? new Date(history[index - 1].timestamp).toDateString() : null;
              const showDateHeader = index === 0 || currentDate !== prevDate;

              return (
                <React.Fragment key={item.id}>
                  {showDateHeader && (
                    <div className="sticky top-0 bg-background py-2 border-b border-muted z-10 -mx-2 px-2 mb-3">
                      <h3 className="text-sm font-medium text-muted-foreground flex items-center">
                        <span className="inline-block w-2 h-2 bg-primary rounded-full mr-2"></span>
                        {formatDate(new Date(item.timestamp))}
                      </h3>
                    </div>
                  )}
                  <div className="flex items-start border-b pb-4 last:border-0 last:pb-0">
                    <div className="mr-4 mt-1">
                      {typeof item.status === 'string' ? (
                        item.status === 'up' ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : item.status === 'degraded' ? (
                          <AlertTriangle className="h-5 w-5 text-amber-500" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )
                      ) : item.status ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">
                          {typeof item.status === 'string' ? (
                            item.status === 'up' ? 'UP' :
                            item.status === 'degraded' ? 'DEGRADED' :
                            'DOWN'
                          ) : item.status ? 'UP' : 'DOWN'}
                        </h4>
                        <span className="text-sm text-muted-foreground">
                          {formatTime(new Date(item.timestamp))}
                        </span>
                      </div>
                      {item.response_time && (
                        <p className="text-sm">
                          Response time: {item.response_time} ms
                        </p>
                      )}
                      {item.error_message && (
                        <p className="text-sm text-red-500 mt-1">
                          {item.error_message}
                        </p>
                      )}
                    </div>
                  </div>
                </React.Fragment>
              );
            })}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={isLoading}
          className="w-full"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'Loading...' : 'Refresh History'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default MonitorHistoryList;
