-- This script checks the current RLS policies
-- Run this in the Supabase SQL Editor

-- Check RLS status for companies table
SELECT relname, relrowsecurity
FROM pg_class
WHERE relname = 'companies';

-- Check policies for companies table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'companies';

-- Check RLS status for company_members table
SELECT relname, relrowsecurity
FROM pg_class
WHERE relname = 'company_members';

-- Check policies for company_members table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'company_members';

-- Check RLS status for monitors table
SELECT relname, relrowsecurity
FROM pg_class
WHERE relname = 'monitors';

-- Check policies for monitors table
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies
WHERE tablename = 'monitors';
