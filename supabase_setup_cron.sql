-- This script sets up a cron job to trigger the monitor-checker function every minute
-- Run this in the Supabase SQL Editor

-- First, make sure the http extension is enabled
CREATE EXTENSION IF NOT EXISTS http;

-- Create a function to call the edge function
CREATE OR REPLACE FUNCTION trigger_monitor_checker()
RETURNS json AS $$
DECLARE
  supabase_url text := 'https://axcfqilzeombkbzebeym.supabase.co';
  function_path text := '/functions/v1/monitor-checker';
  anon_key text := 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4Y2ZxaWx6ZW9tYmtiemViZXltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDEzMTYsImV4cCI6MjA1OTE3NzMxNn0.27w2cbxT9xQ2vd-aQmser6ozRUy9ibhn7mo2-VzN4w8';
  response json;
BEGIN
  SELECT content::json INTO response
  FROM http((  -- Note the double parentheses for the new http extension format
    'POST',
    supabase_url || function_path,
    ARRAY[
      ('Authorization', 'Bearer ' || anon_key),
      ('Content-Type', 'application/json')
    ],
    '{}',
    60  -- Timeout in seconds
  ));

  -- Log the response with timestamp for debugging
  RAISE NOTICE 'Monitor checker run at % with response: %', NOW(), response;

  -- Insert a log entry for debugging
  INSERT INTO monitor_checker_logs (run_time, response)
  VALUES (NOW(), response);

  RETURN response;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER;  -- Run with definer's privileges

-- Schedule the function to run every minute using pg_cron
-- Note: This requires the pg_cron extension to be enabled
SELECT cron.schedule(
  'check-monitors-every-minute',  -- Unique job name
  '* * * * *',                   -- Cron expression: every minute
  $$SELECT trigger_monitor_checker()$$
);

-- To view scheduled jobs:
-- SELECT * FROM cron.job;

-- To manually test the function:
-- SELECT trigger_monitor_checker();

/*
If the above doesn't work, you can set up the scheduled function in Supabase manually:
1. Go to the Supabase dashboard
2. Navigate to Database > SQL Editor
3. Create a new query with this content:
   SELECT cron.schedule(
     'check-monitors-every-minute',
     '* * * * *',
     $$SELECT trigger_monitor_checker()$$
   );
4. Run the query

Or through the UI:
1. Go to the Supabase dashboard
2. Navigate to Database > Functions > Scheduled Functions
3. Create a new scheduled function:
   - Name: trigger_monitor_checker
   - Schedule: * * * * * (every minute)
   - Function: SELECT trigger_monitor_checker();
*/
