import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import SuperadminAccessTest from '@/components/SuperadminAccessTest';
import UserRolesDisplay from '@/components/UserRolesDisplay';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import DocumentTitle from '@/components/DocumentTitle';
import { Shield } from 'lucide-react';

const SuperadminTest = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuperadmin, setIsSuperadmin] = useState(false);
  const [userRoles, setUserRoles] = useState<any[]>([]);
  const [testResults, setTestResults] = useState<{[key: string]: any}>({});

  // Test the is_global_superadmin function
  const testSuperadminFunction = async () => {
    setIsLoading(true);
    const results: {[key: string]: any} = {};

    try {
      // Test 1: Check if the function exists
      results.functionExists = await checkFunctionExists();

      // Test 2: Direct query of user_roles table
      const { data: roleData, error: roleError } = await supabase
        .from('user_roles')
        .select('*')
        .eq('user_id', user?.id)
        .eq('role_type', 'superadmin');

      results.directQuery = { data: roleData, error: roleError?.message };

      // Test 3: Call the is_global_superadmin function
      const { data: isSuperadminData, error: isSuperadminError } = await supabase
        .rpc('is_global_superadmin');

      results.functionCall = { data: isSuperadminData, error: isSuperadminError?.message };

      // Test 4: Call the is_global_superadmin_bypass function
      const { data: bypassData, error: bypassError } = await supabase
        .rpc('is_global_superadmin_bypass');

      results.bypassCall = { data: bypassData, error: bypassError?.message };

      // Update state with results
      setTestResults(results);
      setIsSuperadmin(isSuperadminData || false);

      // Fetch all user roles
      const { data: allRoles, error: allRolesError } = await supabase
        .from('user_roles')
        .select('*');

      if (allRolesError) throw allRolesError;
      setUserRoles(allRoles || []);

    } catch (err) {
      console.error('Error testing superadmin functions:', err);
      toast({
        title: 'Error',
        description: `Error testing superadmin functions: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Check if the function exists
  const checkFunctionExists = async () => {
    try {
      const { data, error } = await supabase.rpc('check_function_exists', {
        function_name: 'is_global_superadmin'
      });

      return { exists: data, error: error?.message };
    } catch (err) {
      // Function to check if another function exists might not exist
      return { exists: false, error: err instanceof Error ? err.message : 'Unknown error' };
    }
  };

  // Make the current user a superadmin
  const makeSuperadmin = async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      // Insert directly into user_roles table
      const { data, error } = await supabase
        .from('user_roles')
        .upsert({
          user_id: user.id,
          role_type: 'superadmin'
        }, { onConflict: 'user_id,role_type' });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'You are now a superadmin',
      });

      // Refresh the tests
      await testSuperadminFunction();
    } catch (err) {
      console.error('Error making user superadmin:', err);
      toast({
        title: 'Error',
        description: `Error making user superadmin: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const header = (
    <UnifiedHeader
      title="Superadmin Test"
      icon={Shield}
      description="Test and manage superadmin permissions"
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Superadmin Test" />
      <div className="container mx-auto py-8 px-4">

        <div className="grid gap-6">
          {/* New SuperadminAccessTest component */}
          <SuperadminAccessTest />

          <Card>
            <CardHeader>
              <CardTitle>User Information</CardTitle>
            </CardHeader>
            <CardContent>
              <p><strong>User ID:</strong> {user?.id || 'Not logged in'}</p>
              <p><strong>Email:</strong> {user?.email || 'Not logged in'}</p>
              <p><strong>Is Superadmin:</strong> {isSuperadmin ? 'Yes' : 'No'}</p>

              <div className="mt-4 flex gap-2">
                <Button onClick={testSuperadminFunction} disabled={isLoading}>
                  {isLoading ? 'Testing...' : 'Run Tests'}
                </Button>
                <Button onClick={makeSuperadmin} disabled={isLoading || isSuperadmin}>
                  Make Superadmin
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-slate-100 dark:bg-slate-800 p-4 rounded overflow-auto max-h-96">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </CardContent>
          </Card>

          {/* New component to display all users and their roles */}
          <UserRolesDisplay />
        </div>
      </div>
    </AppLayout>
  );
};

export default SuperadminTest;
