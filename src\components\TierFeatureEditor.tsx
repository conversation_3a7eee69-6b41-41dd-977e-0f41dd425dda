import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Loader2, RefreshCw, Check, X } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { SubscriptionTier, SubscriptionFeature } from '@/types/subscription';

interface TierFeatureEditorProps {
  tier: SubscriptionTier;
}

export function TierFeatureEditor({ tier }: TierFeatureEditorProps) {
  const {
    useSubscriptionFeatures,
    useTierFeatures,
    useUpdateTierFeature,
    isSuperadmin
  } = useSubscription();

  const { data: features, isLoading: featuresLoading } = useSubscriptionFeatures();
  const { data: tierFeatures, isLoading: tierFeaturesLoading, refetch } = useTierFeatures(tier.id);
  const updateTierFeatureMutation = useUpdateTierFeature();

  const [editingFeature, setEditingFeature] = useState<string | null>(null);
  const [featureValues, setFeatureValues] = useState<Record<string, { value: string; isEnabled: boolean }>>({});
  const [isSaving, setIsSaving] = useState(false);

  // Initialize feature values when data is loaded
  useEffect(() => {
    if (tierFeatures && features) {
      const initialValues: Record<string, { value: string; isEnabled: boolean }> = {};
      
      // Initialize all features with default values
      features.forEach(feature => {
        initialValues[feature.id] = { value: '', isEnabled: false };
      });
      
      // Update with actual values from tierFeatures
      tierFeatures.forEach(tf => {
        initialValues[tf.feature_id] = { 
          value: tf.value || '', 
          isEnabled: tf.is_enabled 
        };
      });
      
      setFeatureValues(initialValues);
    }
  }, [tierFeatures, features]);

  // If not a superadmin, don't show this component
  if (!isSuperadmin) {
    return null;
  }

  const handleEdit = (featureId: string) => {
    setEditingFeature(featureId);
  };

  const handleSave = async (featureId: string) => {
    const featureValue = featureValues[featureId];
    if (!featureValue) return;

    setIsSaving(true);
    try {
      await updateTierFeatureMutation.mutateAsync({
        tierId: tier.id,
        featureId,
        value: featureValue.value,
        isEnabled: featureValue.isEnabled
      });

      setEditingFeature(null);
      refetch();
    } catch (error) {
      console.error('Error updating tier feature:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingFeature(null);
  };

  const handleValueChange = (featureId: string, value: string) => {
    setFeatureValues(prev => ({
      ...prev,
      [featureId]: { ...prev[featureId], value }
    }));
  };

  const handleEnabledChange = (featureId: string, isEnabled: boolean) => {
    setFeatureValues(prev => ({
      ...prev,
      [featureId]: { ...prev[featureId], isEnabled }
    }));
    
    // If we're not in edit mode, save immediately
    if (editingFeature !== featureId) {
      updateTierFeatureMutation.mutate({
        tierId: tier.id,
        featureId,
        value: featureValues[featureId]?.value || '',
        isEnabled
      });
    }
  };

  if (featuresLoading || tierFeaturesLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Features for {tier.name} Tier</CardTitle>
        <CardDescription>
          Configure which features are enabled for this tier and their values
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-end mb-4">
          <Button variant="outline" onClick={() => refetch()} disabled={featuresLoading || tierFeaturesLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Feature</TableHead>
                <TableHead className="w-[200px]">Value</TableHead>
                <TableHead className="w-[100px] text-center">Enabled</TableHead>
                <TableHead className="w-[150px] text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {features?.map((feature) => {
                const featureValue = featureValues[feature.id] || { value: '', isEnabled: false };
                const isEditing = editingFeature === feature.id;
                
                return (
                  <TableRow key={feature.id}>
                    <TableCell className="font-medium">
                      <div>
                        {feature.name}
                        <p className="text-xs text-muted-foreground mt-1">{feature.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {isEditing ? (
                        <Input
                          value={featureValue.value}
                          onChange={(e) => handleValueChange(feature.id, e.target.value)}
                          placeholder="e.g., 5, Unlimited, etc."
                        />
                      ) : (
                        <span>{featureValue.value || '-'}</span>
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex justify-center">
                        <Switch
                          checked={featureValue.isEnabled}
                          onCheckedChange={(checked) => handleEnabledChange(feature.id, checked)}
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {isEditing ? (
                        <div className="space-x-2">
                          <Button variant="outline" size="sm" onClick={handleCancel} disabled={isSaving}>
                            Cancel
                          </Button>
                          <Button size="sm" onClick={() => handleSave(feature.id)} disabled={isSaving}>
                            {isSaving ? (
                              <>
                                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              'Save'
                            )}
                          </Button>
                        </div>
                      ) : (
                        <Button variant="outline" size="sm" onClick={() => handleEdit(feature.id)}>
                          Edit Value
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
