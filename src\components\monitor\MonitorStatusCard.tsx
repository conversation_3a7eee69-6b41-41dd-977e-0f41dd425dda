import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { CheckCircle, XCircle, Clock, AlertTriangle } from "lucide-react";

interface MonitorStatusCardProps {
  status: boolean | string;
  isActive: boolean;
  hasHistory: boolean;
  errorMessage?: string;
}

const MonitorStatusCard: React.FC<MonitorStatusCardProps> = ({
  status,
  isActive,
  hasHistory,
  errorMessage
}) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm text-slate-500 dark:text-slate-400">Status</CardTitle>
      </CardHeader>
      <CardContent>
        {hasHistory ? (
          <div className="flex items-center">
            {/* Determine status based on active flag and latest history */}
            {!isActive ? (
              <>
                <Clock className="h-5 w-5 text-slate-500 mr-2" />
                <span className="font-medium">Paused</span>
              </>
            ) : typeof status === 'string' ? (
              status === 'up' ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                  <span className="font-medium">Up</span>
                </>
              ) : status === 'degraded' ? (
                <div className="flex flex-col">
                  <div className="flex items-center">
                    <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                    <span className="font-medium">Degraded</span>
                  </div>
                  {errorMessage && (
                    <span className="text-xs text-amber-600 dark:text-amber-400 mt-1 ml-7">
                      {errorMessage.replace('HTTP status indicates degraded service: ', 'Error ').replace('Response time ', 'Slow: ')}
                    </span>
                  )}
                </div>
              ) : (
                <>
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                  <span className="font-medium">Down</span>
                </>
              )
            ) : status ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                <span className="font-medium">Up</span>
              </>
            ) : (
              <>
                <XCircle className="h-5 w-5 text-red-500 mr-2" />
                <span className="font-medium">Down</span>
              </>
            )}
          </div>
        ) : (
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-500 mr-2" />
            <span className="font-medium">Unknown</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MonitorStatusCard;
