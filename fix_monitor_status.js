// This patch fixes the status field format in the monitor service
// Apply these changes to your monitor service code

// Find the function that saves check results (likely in database.js or similar)
// Look for code similar to this:

// In database.js or equivalent file:
async function saveCheckResult(monitor, status, responseTime, errorMessage) {
  try {
    // Make sure status is a boolean
    const booleanStatus = status === true || status === false ? status : !!status;
    
    const checkResult = {
      monitor_id: monitor.id,
      status: booleanStatus, // Ensure this is a boolean
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    logger.debug(`Saving check result: ${JSON.stringify(checkResult)}`);
    
    const { error } = await supabase
      .from('monitor_history')
      .insert(checkResult);
      
    if (error) {
      throw error;
    }
    
    logger.info(`Saved check result for ${monitor.name}: ${booleanStatus ? 'UP' : 'DOWN'} (${responseTime}ms)`);
    return true;
  } catch (error) {
    logger.error(`Error saving check result for monitor ${monitor.id}: ${error.message}`);
    return false;
  }
}

// Also check the performCheck function to ensure it's returning a boolean status
// It should look something like this:

async function performCheck(monitor) {
  try {
    logger.info(`Checking monitor: ${monitor.name} (${monitor.id})`);
    logger.info(`Target: ${monitor.target}, Type: ${monitor.type}, Timeout: ${monitor.timeout}s`);
    
    const startTime = Date.now();
    let status = false; // Initialize as boolean
    let responseTime = null;
    let errorMessage = null;
    
    // For HTTP checks
    if (monitor.type === 'http') {
      try {
        logger.info(`Sending HTTP request to ${monitor.target}...`);
        
        const options = {
          timeout: monitor.timeout * 1000,
          validateStatus: null, // Don't throw on any status code
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
          },
          maxRedirects: 5
        };
        
        logger.info(`Request options: ${JSON.stringify(options)}`);
        
        const response = await axios.get(monitor.target, options);
        
        responseTime = Date.now() - startTime;
        
        // Make sure this is a boolean
        status = response.status >= 200 && response.status < 300;
        
        logger.info(`Response received: Status ${response.status}, Time ${responseTime}ms`);
        
        if (!status) {
          errorMessage = `HTTP status: ${response.status}`;
        }
      } catch (error) {
        responseTime = Date.now() - startTime;
        errorMessage = error.message;
        logger.error(`Error checking ${monitor.target}: ${error.message}`);
      }
    }
    
    // Save the check result
    await saveCheckResult(monitor, status, responseTime, errorMessage);
    
    // Check if status changed and send notification if needed
    const statusChanged = await checkStatusChange(monitor, status);
    
    return { 
      status: !!status, // Ensure boolean
      responseTime, 
      errorMessage,
      statusChanged
    };
  } catch (error) {
    logger.error(`Error performing check for ${monitor.name}: ${error.message}`);
    return { status: false, responseTime: null, errorMessage: error.message };
  }
}
