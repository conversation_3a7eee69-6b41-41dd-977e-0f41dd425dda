# Notification System Updates

This document describes the changes made to the notification system in the Vurbis Uptime Monitor (VUM) application.

## Overview of Changes

1. **Removed Dashboard Notifications UI**:
   - Removed the NotificationCenter component from the UI
   - Removed the use-notifications hook
   - Notifications are now only sent via email, not displayed in the dashboard

2. **Added Superadmin Test Notification Feature**:
   - Created a new SQL function `superadmin_send_test_notification` that allows superadmins to send test notifications for any monitor
   - Added a new UI component `SuperadminTestNotification` that provides a user-friendly interface for sending test notifications
   - This feature is only available to superadmins

3. **Updated Email Notification System**:
   - Email notifications are now sent to:
     - All company admins for the affected company
     - All superadmins in the system
   - Removed the global_notification_email setting

## Technical Implementation

### Database Changes

1. **Removed `user_id` from Notifications Table**:
   - Dropped the foreign key constraint on `user_id`
   - Removed the `user_id` column from the table
   - Updated RLS policies to use company membership instead of user ID

2. **Added Test Notification Function**:
   - Created a new SQL function `superadmin_send_test_notification` that:
     - Verifies the user is a superadmin
     - Creates a test notification for the selected monitor
     - Sends email notifications to all appropriate recipients

### Frontend Changes

1. **Removed Notification UI Components**:
   - Removed `NotificationCenter.tsx` component
   - Removed `use-notifications.ts` hook
   - Removed notification UI references from Dashboard and MonitorDetail pages

2. **Added Test Notification UI**:
   - Created `SuperadminTestNotification.tsx` component
   - Added it to the GlobalSettings page in the Notifications tab
   - Provides a dropdown to select any monitor and the desired test status (up/down/degraded)

## Benefits of the Changes

1. **Simplified System**: Removed the complexity of maintaining both in-app and email notifications
2. **Improved Email Testing**: Added a dedicated feature for testing email notifications
3. **Reduced Database Size**: Removed unnecessary data from the notifications table
4. **Better Security**: Access control is now based solely on company membership

## Usage Instructions

### Testing Email Notifications

1. Log in as a superadmin
2. Navigate to Global Settings
3. Select the "Notifications" tab
4. Use the "Test Notification" card to:
   - Select a monitor from the dropdown
   - Choose the status (Up, Down, or Degraded)
   - Click "Send Test Notification"
5. Check that emails are received by all appropriate recipients
