import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { UserWithRoles } from '@/hooks/use-users';
import UserTableRow from './UserTableRow';

interface UserTableProps {
  users: UserWithRoles[];
  searchQuery: string;
  currentUserId: string | undefined;
  setEditUserFormValues: (user: UserWithRoles) => void;
  handleToggleSuperadminStatus: (userId: string, isSuperadmin: boolean) => Promise<void>;
  handleRemoveUserFromCompany: (userId: string, companyId: string) => Promise<void>;
  setSelectedUser: (user: UserWithRoles) => void;
  setDeleteUserDialogOpen: (open: boolean) => void;
  setAssignCompanyDialogOpen: (open: boolean) => void;
}

const UserTable: React.FC<UserTableProps> = ({
  users,
  searchQuery,
  currentUserId,
  setEditUserFormValues,
  handleToggleSuperadminStatus,
  handleRemoveUserFromCompany,
  setSelectedUser,
  setDeleteUserDialogOpen,
  setAssignCompanyDialogOpen,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Users</CardTitle>
        <CardDescription>
          Manage users, their company memberships, and permissions
        </CardDescription>
      </CardHeader>
      <CardContent>
        {users.length === 0 ? (
          <div className="text-center py-8 text-slate-500">
            {searchQuery ? 'No users match your search' : 'No users found'}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Companies</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.map((user) => (
                  <UserTableRow
                    key={user.id}
                    user={user}
                    currentUserId={currentUserId}
                    setEditUserFormValues={setEditUserFormValues}
                    handleToggleSuperadminStatus={handleToggleSuperadminStatus}
                    handleRemoveUserFromCompany={handleRemoveUserFromCompany}
                    setSelectedUser={setSelectedUser}
                    setDeleteUserDialogOpen={setDeleteUserDialogOpen}
                    setAssignCompanyDialogOpen={setAssignCompanyDialogOpen}
                  />
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserTable;
