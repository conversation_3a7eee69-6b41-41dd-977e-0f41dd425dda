import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, LayoutGrid } from 'lucide-react';

interface FixedHeaderProps {
  title: string;
  showBackButton?: boolean;
  showAllMonitorsButton?: boolean;
}

const FixedHeader: React.FC<FixedHeaderProps> = ({
  title,
  showBackButton = true,
  showAllMonitorsButton = true,
}) => {
  const navigate = useNavigate();

  return (
    <div className="fixed top-0 left-0 right-0 bg-white dark:bg-slate-900 border-b border-slate-200 dark:border-slate-800 z-10 shadow-sm">
      <div className="container mx-auto py-3 px-4 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {showBackButton && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate(-1)}
              className="h-8 px-2"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          <h1 className="text-lg font-semibold">{title}</h1>
        </div>
        
        {showAllMonitorsButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard')}
            className="h-8"
          >
            <LayoutGrid className="h-4 w-4 mr-2" />
            All Monitors
          </Button>
        )}
      </div>
    </div>
  );
};

export default FixedHeader;
