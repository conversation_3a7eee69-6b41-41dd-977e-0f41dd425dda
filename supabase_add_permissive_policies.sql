-- This script adds permissive policies for all tables
-- Run this in the Supabase SQL Editor

-- Add permissive policy for companies table
CREATE POLICY "Temp: Allow all operations for authenticated users"
ON public.companies
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Add permissive policy for company_members table
CREATE POLICY "Temp: Allow all operations for authenticated users"
ON public.company_members
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Grant necessary permissions
GRANT ALL ON public.companies TO authenticated;
GRANT ALL ON public.company_members TO authenticated;
