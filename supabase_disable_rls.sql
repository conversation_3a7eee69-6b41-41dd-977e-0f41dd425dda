-- This script temporarily disables <PERSON><PERSON> for testing
-- Run this in the Supabase SQL Editor
-- WARNING: This should only be used for testing and should be re-enabled afterward

-- Disable RLS for companies table
ALTER TABLE public.companies DISABLE ROW LEVEL SECURITY;

-- Disable RLS for company_members table
ALTER TABLE public.company_members DISABLE ROW LEVEL SECURITY;

-- Disable RLS for monitors table
ALTER TABLE public.monitors DISABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT ALL ON public.companies TO authenticated;
GRANT ALL ON public.company_members TO authenticated;
GRANT ALL ON public.monitors TO authenticated;
