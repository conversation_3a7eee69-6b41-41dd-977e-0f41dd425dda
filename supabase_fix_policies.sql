-- This script fixes the policies for the company-related tables
-- Run this in the Supabase SQL Editor

-- Enable Row Level Security if not already enabled
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.monitors ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view companies they are members of" ON public.companies;
DROP POLICY IF EXISTS "Company admins can update their companies" ON public.companies;
DROP POLICY IF EXISTS "Company admins can delete their companies" ON public.companies;
DROP POLICY IF EXISTS "Authenticated users can create companies" ON public.companies;
DROP POLICY IF EXISTS "Users can view company members for their companies" ON public.company_members;
DROP POLICY IF EXISTS "Company admins can manage company members" ON public.company_members;
DROP POLICY IF EXISTS "Users can view monitors in their companies" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can create monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can update monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can delete monitors" ON public.monitors;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.companies;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.company_members;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.monitors;
DROP POLICY IF EXISTS "Users can view their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can insert their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can update their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can delete their own monitors" ON public.monitors;

-- Create temporary permissive policies to allow all operations
-- This will make it easier to set up the initial data
CREATE POLICY "Temp: Allow all operations for authenticated users"
ON public.companies
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

CREATE POLICY "Temp: Allow all operations for authenticated users"
ON public.company_members
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

CREATE POLICY "Temp: Allow all operations for authenticated users"
ON public.monitors
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Grant necessary permissions
GRANT ALL ON public.companies TO authenticated;
GRANT ALL ON public.company_members TO authenticated;
GRANT ALL ON public.monitors TO authenticated;
