-- This script fixes the notifications table schema cache issue
-- Run this in the Supabase SQL Editor

-- First, let's check the current schema of the notifications table
DO $$
DECLARE
  column_exists BOOLEAN;
BEGIN
  -- Check if the notifications table exists
  IF EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'notifications') THEN
    -- Check if the created_at column exists
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'notifications'
      AND column_name = 'created_at'
    ) INTO column_exists;

    IF column_exists THEN
      RAISE NOTICE 'created_at column exists in notifications table';
    ELSE
      -- Add the created_at column if it doesn't exist
      ALTER TABLE public.notifications ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT now();
      RAISE NOTICE 'Added created_at column to notifications table';
    END IF;

    -- Check if the timestamp column exists (which is causing the error)
    SELECT EXISTS (
      SELECT FROM information_schema.columns
      WHERE table_schema = 'public'
      AND table_name = 'notifications'
      AND column_name = 'timestamp'
    ) INTO column_exists;

    IF column_exists THEN
      RAISE NOTICE 'timestamp column exists in notifications table';
    ELSE
      -- Add the timestamp column as an alias to created_at
      ALTER TABLE public.notifications ADD COLUMN timestamp TIMESTAMP WITH TIME ZONE GENERATED ALWAYS AS (created_at) STORED;
      RAISE NOTICE 'Added timestamp column as an alias to created_at';
    END IF;
  ELSE
    RAISE NOTICE 'notifications table does not exist';
  END IF;
END $$;

-- Refresh the schema cache to make sure the new column is recognized
NOTIFY pgrst, 'reload schema';

-- Update any code that might be using the timestamp column to use created_at instead
COMMENT ON COLUMN public.notifications.timestamp IS 'DEPRECATED: Use created_at instead. This column is an alias to created_at for backward compatibility.';
