// This script fixes the issue with duplicate monitor checks
// It implements a worker pool with concurrency control

// Import required modules
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const CHECK_INTERVAL = 60 * 1000; // Check every minute
const MAX_CONCURRENT_CHECKS = 5; // Maximum number of concurrent checks
const LOG_DIR = './logs';
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');

// Create log directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Worker pool for concurrent checks
class WorkerPool {
  constructor(maxConcurrent = 5) {
    this.maxConcurrent = maxConcurrent;
    this.queue = [];
    this.activeWorkers = 0;
    this.inProgressMonitors = new Set(); // Track monitors currently being checked
  }

  async addTask(monitor, taskFn) {
    // If this monitor is already being checked, skip it
    if (this.inProgressMonitors.has(monitor.id)) {
      log(`Monitor ${monitor.name} is already being checked, skipping duplicate check`, 'WARN');
      return;
    }

    // Add to queue
    return new Promise((resolve, reject) => {
      const task = async () => {
        try {
          this.inProgressMonitors.add(monitor.id);
          const result = await taskFn(monitor);
          resolve(result);
          return result;
        } catch (error) {
          reject(error);
          throw error;
        } finally {
          this.inProgressMonitors.delete(monitor.id);
          this.activeWorkers--;
          this.processQueue();
        }
      };

      this.queue.push(task);
      this.processQueue();
    });
  }

  processQueue() {
    if (this.queue.length === 0) return;
    if (this.activeWorkers >= this.maxConcurrent) return;

    const task = this.queue.shift();
    this.activeWorkers++;

    // Execute the task (don't await it here)
    task().catch(error => {
      log(`Task error: ${error.message}`, 'ERROR');
    });

    // If we can process more, continue
    if (this.activeWorkers < this.maxConcurrent && this.queue.length > 0) {
      this.processQueue();
    }
  }

  get queueLength() {
    return this.queue.length;
  }

  get activeCount() {
    return this.activeWorkers;
  }
}

// Create worker pool
const workerPool = new WorkerPool(MAX_CONCURRENT_CHECKS);

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  console.log(logMessage);
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Function to perform a check on a monitor
async function performCheck(monitor) {
  log(`Checking monitor: ${monitor.name} (${monitor.type})`);

  const startTime = Date.now();
  let status = false;
  let responseTime = null;
  let errorMessage = null;

  try {
    // Perform the check based on monitor type
    switch (monitor.type) {
      case 'http':
        // HTTP check
        try {
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);

          const response = await fetch(monitor.target, {
            method: 'GET',
            signal: controller.signal
          });

          clearTimeout(timeoutId);
          status = response.ok;
          responseTime = Date.now() - startTime;

          if (!status) {
            errorMessage = `HTTP status: ${response.status}`;
          }
        } catch (error) {
          errorMessage = error.message;
        }
        break;

      // Add other monitor types here (ping, port, etc.)
      default:
        errorMessage = `Unsupported monitor type: ${monitor.type}`;
        break;
    }
  } catch (error) {
    errorMessage = error.message;
  }

  // Save the check result
  try {
    const checkResult = {
      monitor_id: monitor.id,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };

    const { error } = await supabase
      .from('monitor_history')
      .insert(checkResult);

    if (error) {
      throw error;
    }

    log(`Saved check result for ${monitor.name}: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);

    // Check if status changed and send notification if needed
    await checkStatusChange(monitor, status);

    return checkResult;
  } catch (error) {
    log(`Failed to save check result: ${error.message}`, 'ERROR');
    throw error;
  }
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(monitor, currentStatus) {
  try {
    // Get the previous check
    const { data: previousChecks, error } = await supabase
      .from('monitor_history')
      .select('status')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .limit(2);

    if (error) {
      throw error;
    }

    // If this is the first check or status changed, send notification
    if (previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
      const statusText = currentStatus ? 'up' : 'down';
      const previousStatus = previousChecks.length < 2 ? null : (previousChecks[1].status ? 'up' : 'down');

      if (previousStatus) {
        log(`Status change detected for ${monitor.name}: ${previousStatus} -> ${statusText}`);
      }

      await createNotificationsForMonitor(monitor, currentStatus);
      return true;
    }

    return false;
  } catch (error) {
    log(`Failed to check status change: ${error.message}`, 'ERROR');
    return false;
  }
}

// Function to create notifications for all companies associated with a monitor
async function createNotificationsForMonitor(monitor, status) {
  try {
    // Make sure we have the monitor's user_id
    if (!monitor.user_id) {
      // Get the monitor details including user_id
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('user_id, name')
        .eq('id', monitor.id)
        .single();

      if (monitorError) {
        log(`Error getting monitor details for ${monitor.id}: ${monitorError.message}`, 'ERROR');
        return false;
      }

      if (!monitorData) {
        log(`Monitor not found: ${monitor.id}`, 'ERROR');
        return false;
      }

      // Update the monitor object with the user_id
      monitor.user_id = monitorData.user_id;
      if (!monitor.name) {
        monitor.name = monitorData.name;
      }
    }

    // Get all companies associated with this monitor
    const { data: companies, error: companiesError } = await supabase
      .from('monitor_companies')
      .select('company_id')
      .eq('monitor_id', monitor.id);

    if (companiesError) {
      log(`Error getting companies for monitor ${monitor.id}: ${companiesError.message}`, 'ERROR');
      return false;
    }

    if (!companies || companies.length === 0) {
      log(`No companies found for monitor ${monitor.id}`, 'WARN');
      return false;
    }

    // Create a notification for each company
    let successCount = 0;
    for (const company of companies) {
      try {
        const statusMessage = status ? 'UP' : 'DOWN';

        // Create notification object with both timestamp and created_at for compatibility
        const notification = {
          monitor_id: monitor.id,
          user_id: monitor.user_id, // Ensure this is set
          company_id: company.company_id,
          message: `Monitor ${monitor.name} is now ${statusMessage}`,
          type: status ? 'up' : 'down',
          read: false,
          created_at: new Date().toISOString()
        };

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(notification);

        if (notificationError) {
          throw notificationError;
        }

        successCount++;
      } catch (error) {
        log(`Error creating notification for company ${company.company_id}: ${error.message}`, 'ERROR');
      }
    }

    log(`Created notifications for ${monitor.name} across ${successCount} companies`);
    return successCount > 0;
  } catch (error) {
    log(`Error creating notifications for monitor ${monitor.id}: ${error.message}`, 'ERROR');
    return false;
  }
}

// Check monitors
async function checkMonitors() {
  try {
    // Get all active monitors
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    log(`Found ${monitors.length} active monitors`);

    // Check each monitor
    for (const monitor of monitors) {
      try {
        // Get the last check time
        const { data: lastCheck, error: lastCheckError } = await supabase
          .from('monitor_history')
          .select('timestamp')
          .eq('monitor_id', monitor.id)
          .order('timestamp', { ascending: false })
          .limit(1);

        if (lastCheckError) {
          throw lastCheckError;
        }

        const now = Date.now();
        const lastCheckTime = lastCheck && lastCheck.length > 0
          ? new Date(lastCheck[0].timestamp).getTime()
          : 0;
        const intervalMs = monitor.interval * 60 * 1000;

        // Check if it's time to check this monitor
        if (now - lastCheckTime >= intervalMs) {
          log(`Time to check monitor ${monitor.name} (interval: ${monitor.interval} minutes)`);

          // Add to worker pool instead of checking immediately
          workerPool.addTask(monitor, performCheck).catch(error => {
            log(`Error checking monitor ${monitor.name}: ${error.message}`, 'ERROR');
          });
        } else {
          const nextCheckIn = Math.round((intervalMs - (now - lastCheckTime)) / 1000);
          log(`Monitor ${monitor.name} will be checked in ${nextCheckIn} seconds`, 'DEBUG');
        }
      } catch (error) {
        log(`Error processing monitor ${monitor.name}: ${error.message}`, 'ERROR');
      }
    }

    // Log worker pool status
    log(`Worker pool status: ${workerPool.activeCount} active, ${workerPool.queueLength} queued`);
  } catch (error) {
    log(`Error fetching monitors: ${error.message}`, 'ERROR');
  }

  // Schedule the next check
  setTimeout(checkMonitors, CHECK_INTERVAL);
}

// Start the service
async function startService() {
  log('Starting Monitor Service with Worker Pool...');

  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('monitors')
      .select('id, name');

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${data.length} monitors.`);

    // Start checking monitors
    checkMonitors();

    log('Monitor Service started successfully');
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startService, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down Monitor Service...');
  log('Monitor Service stopped');
  process.exit(0);
});

// Start the service
startService();
