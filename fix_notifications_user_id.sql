-- This script fixes notifications with null user_id values
-- Run this in the Supabase SQL Editor

-- First, let's check if there are any notifications with null user_id
SELECT COUNT(*) AS null_user_id_count
FROM public.notifications
WHERE user_id IS NULL;

-- Create a temporary function to fix notifications with null user_id
CREATE OR REPLACE FUNCTION fix_notifications_with_null_user_id()
RETURNS INTEGER AS $$
DECLARE
    fixed_count INTEGER := 0;
    notification_rec RECORD;
    monitor_owner_id UUID;
BEGIN
    -- Loop through all notifications with null user_id
    FOR notification_rec IN 
        SELECT id, monitor_id
        FROM public.notifications
        WHERE user_id IS NULL
    LOOP
        -- Get the monitor owner
        SELECT user_id INTO monitor_owner_id
        FROM public.monitors
        WHERE id = notification_rec.monitor_id;
        
        -- If we found an owner, update the notification
        IF monitor_owner_id IS NOT NULL THEN
            UPDATE public.notifications
            SET user_id = monitor_owner_id
            WHERE id = notification_rec.id;
            
            fixed_count := fixed_count + 1;
        END IF;
    END LOOP;
    
    RETURN fixed_count;
END;
$$ LANGUAGE plpgsql;

-- Run the function to fix notifications
SELECT fix_notifications_with_null_user_id() AS fixed_notifications_count;

-- Drop the temporary function
DROP FUNCTION fix_notifications_with_null_user_id();

-- Check if there are still any notifications with null user_id
SELECT COUNT(*) AS remaining_null_user_id_count
FROM public.notifications
WHERE user_id IS NULL;

-- If there are still notifications with null user_id, we need to decide what to do with them
-- Option 1: Delete them
-- DELETE FROM public.notifications WHERE user_id IS NULL;

-- Option 2: Assign them to a default user (e.g., the first superadmin)
-- UPDATE public.notifications
-- SET user_id = (
--     SELECT user_id 
--     FROM public.user_roles 
--     WHERE role_type = 'superadmin' 
--     LIMIT 1
-- )
-- WHERE user_id IS NULL;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
