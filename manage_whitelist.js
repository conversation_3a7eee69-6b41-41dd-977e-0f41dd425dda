import { addToWhitelist, removeFrom<PERSON>hitelist } from './security_middleware.js';

const command = process.argv[2];
const ip = process.argv[3];

if (!command || !ip) {
  console.log('Usage:');
  console.log('  Add IP:    node manage_whitelist.js add <ip>');
  console.log('  Remove IP: node manage_whitelist.js remove <ip>');
  process.exit(1);
}

async function main() {
  try {
    switch (command.toLowerCase()) {
      case 'add':
        await addToWhitelist(ip);
        console.log(`Added ${ip} to whitelist`);
        break;
      case 'remove':
        await removeFromWhitelist(ip);
        console.log(`Removed ${ip} from whitelist`);
        break;
      default:
        console.error('Invalid command. Use "add" or "remove"');
        process.exit(1);
    }
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}

main();
