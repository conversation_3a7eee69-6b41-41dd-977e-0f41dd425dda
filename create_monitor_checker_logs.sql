-- Create a table to log monitor checker runs
CREATE TABLE IF NOT EXISTS monitor_checker_logs (
  id SERIAL PRIMARY KEY,
  run_time TIMESTAMP WITH TIME ZONE NOT NULL,
  response JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on run_time for faster queries
CREATE INDEX IF NOT EXISTS monitor_checker_logs_run_time_idx ON monitor_checker_logs (run_time);

-- Set up RLS policies
ALTER TABLE monitor_checker_logs ENABLE ROW LEVEL SECURITY;

-- Allow service role to insert
CREATE POLICY "Service role can insert logs" 
ON monitor_checker_logs FOR INSERT 
TO service_role 
USING (true);

-- Allow authenticated users to view logs
CREATE POLICY "Authenticated users can view logs" 
ON monitor_checker_logs FOR SELECT 
TO authenticated 
USING (true);
