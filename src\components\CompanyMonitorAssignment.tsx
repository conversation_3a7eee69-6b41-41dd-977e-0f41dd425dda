import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, Plus, Trash2, Search, Check, X } from 'lucide-react';
import { useMonitors } from '@/hooks/use-monitors';
import { supabase } from '@/integrations/supabase/client';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function CompanyMonitorAssignment() {
  const { id } = useParams<{ id: string }>();

  const [companyMonitors, setCompanyMonitors] = useState<any[]>([]);
  const [availableMonitors, setAvailableMonitors] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [selectedMonitor, setSelectedMonitor] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Load company monitors
  const loadCompanyMonitors = async () => {
    setIsLoading(true);
    try {
      // Get monitors assigned to this company
      const { data: companyData, error: companyError } = await supabase
        .from('monitor_companies')
        .select(`
          id,
          monitor_id,
          monitor:monitor_id (
            id,
            name,
            target,
            type,
            active,
            created_at
          )
        `)
        .eq('company_id', id);

      if (companyError) throw companyError;

      // Format the data
      const formattedCompanyMonitors = companyData.map(item => ({
        relationshipId: item.id,
        id: item.monitor.id,
        name: item.monitor.name,
        target: item.monitor.target,
        type: item.monitor.type,
        active: item.monitor.active,
        created_at: item.monitor.created_at
      }));

      setCompanyMonitors(formattedCompanyMonitors);

      // Get all monitors not assigned to this company
      const { data: allMonitors, error: allError } = await supabase
        .from('monitors')
        .select('*')
        .eq('deleted', false);

      if (allError) throw allError;

      // Filter out monitors already assigned to this company
      const assignedIds = new Set(formattedCompanyMonitors.map(m => m.id));
      const unassignedMonitors = allMonitors.filter(m => !assignedIds.has(m.id));

      setAvailableMonitors(unassignedMonitors);
    } catch (error) {
      console.error('Error loading company monitors:', error);
      toast({
        title: 'Error',
        description: 'Failed to load monitors. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      loadCompanyMonitors();
    }
  }, [id]);

  // Filter available monitors based on search term
  const filteredAvailableMonitors = availableMonitors.filter(monitor =>
    monitor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    monitor.target.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle adding a monitor to the company
  const handleAddMonitor = async (monitor: any) => {
    setIsProcessing(true);
    try {

      // Create the relationship
      const { error } = await supabase
        .from('monitor_companies')
        .insert({
          monitor_id: monitor.id,
          company_id: id
        });

      if (error) {
        // If there's a monitor limit error, show a specific message
        if (error.message.includes('Monitor limit reached')) {
          toast({
            title: 'Monitor Limit Reached',
            description: error.message,
            variant: 'destructive',
          });
        } else {
          throw error;
        }
      } else {
        toast({
          title: 'Monitor Added',
          description: `"${monitor.name}" has been added to this company.`,
        });

        // Refresh the lists
        await loadCompanyMonitors();
      }
    } catch (error) {
      console.error('Error adding monitor to company:', error);
      toast({
        title: 'Error',
        description: 'Failed to add monitor to company. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setShowAddDialog(false);
      setIsProcessing(false);
    }
  };

  // Handle removing a monitor from the company
  const handleRemoveMonitor = async () => {
    if (!selectedMonitor) return;

    setIsProcessing(true);
    try {
      const { error } = await supabase
        .from('monitor_companies')
        .delete()
        .eq('id', selectedMonitor.relationshipId);

      if (error) throw error;

      toast({
        title: 'Monitor Removed',
        description: `"${selectedMonitor.name}" has been removed from this company.`,
      });

      // Refresh the lists
      await loadCompanyMonitors();
    } catch (error) {
      console.error('Error removing monitor from company:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove monitor from company. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setShowRemoveDialog(false);
      setSelectedMonitor(null);
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Manage Company Monitors</CardTitle>
        <CardDescription>
          Assign or remove monitors for this company
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">

          {/* Current company monitors */}
          <div>
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium">Current Monitors</h3>
              <div className="flex space-x-2">
                <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
                  <DialogTrigger asChild>
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Monitor
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-3xl">
                    <DialogHeader>
                      <DialogTitle>Add Monitor to Company</DialogTitle>
                      <DialogDescription>
                        Select a monitor to add to this company
                      </DialogDescription>
                    </DialogHeader>

                    <div className="py-4">
                      <div className="flex items-center space-x-2 mb-4">
                        <Search className="h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search monitors..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="flex-1"
                        />
                      </div>

                      <div className="max-h-[400px] overflow-y-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Name</TableHead>
                              <TableHead>Target</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Action</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredAvailableMonitors.length === 0 ? (
                              <TableRow>
                                <TableCell colSpan={5} className="text-center py-4">
                                  No monitors available to add
                                </TableCell>
                              </TableRow>
                            ) : (
                              filteredAvailableMonitors.map((monitor) => (
                                <TableRow key={monitor.id}>
                                  <TableCell className="font-medium">{monitor.name}</TableCell>
                                  <TableCell>{monitor.target}</TableCell>
                                  <TableCell>{monitor.type}</TableCell>
                                  <TableCell>
                                    {monitor.active ? (
                                      <span className="flex items-center">
                                        <Check className="h-4 w-4 text-green-500 mr-1" />
                                        Active
                                      </span>
                                    ) : (
                                      <span className="flex items-center">
                                        <X className="h-4 w-4 text-red-500 mr-1" />
                                        Paused
                                      </span>
                                    )}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <Button
                                      size="sm"
                                      onClick={() => handleAddMonitor(monitor)}
                                      disabled={isProcessing}
                                    >
                                      {isProcessing ? (
                                        <>
                                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                          Adding...
                                        </>
                                      ) : (
                                        <>
                                          <Plus className="h-3 w-3 mr-1" />
                                          Add
                                        </>
                                      )}
                                    </Button>
                                  </TableCell>
                                </TableRow>
                              ))
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </div>

                    <DialogFooter>
                      <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                        Close
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                <Button variant="outline" onClick={loadCompanyMonitors}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Target</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {companyMonitors.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-4">
                      No monitors assigned to this company
                    </TableCell>
                  </TableRow>
                ) : (
                  companyMonitors.map((monitor) => (
                    <TableRow key={monitor.id}>
                      <TableCell className="font-medium">{monitor.name}</TableCell>
                      <TableCell>{monitor.target}</TableCell>
                      <TableCell>{monitor.type}</TableCell>
                      <TableCell>
                        {monitor.active ? (
                          <span className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-1" />
                            Active
                          </span>
                        ) : (
                          <span className="flex items-center">
                            <X className="h-4 w-4 text-red-500 mr-1" />
                            Paused
                          </span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => {
                            setSelectedMonitor(monitor);
                            setShowRemoveDialog(true);
                          }}
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          Remove
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Remove Monitor Confirmation Dialog */}
        <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Monitor</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove "{selectedMonitor?.name}" from this company?
                This will not delete the monitor, only remove the association.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setShowRemoveDialog(false)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleRemoveMonitor}
                className="bg-red-600 hover:bg-red-700"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Removing...
                  </>
                ) : (
                  'Remove'
                )}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </CardContent>
    </Card>
  );
}
