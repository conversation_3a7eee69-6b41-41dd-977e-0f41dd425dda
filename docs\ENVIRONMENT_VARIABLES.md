# Environment Variables

This document describes the environment variables used in the Vurbis Uptime Monitor (VUM) application.

## IMPORTANT SECURITY NOTICE

**NEVER hardcode sensitive information like API keys, passwords, or credentials in your code or commit them to the repository.**

Always use environment variables for sensitive information and ensure that files containing actual values (like `.env.local`) are listed in `.gitignore` to prevent them from being committed to the repository.

The example files (`.env.example` and `.env.local.example`) contain placeholders that should be replaced with actual values in your local environment.

### Development vs. Production

For development, the application will use fallback values if certain environment variables aren't set. This makes it easier for developers to get started without having to set up all environment variables immediately.

However, for production, all environment variables **MUST** be set properly. The fallback values should never be used in production.

## Frontend Environment Variables

These variables are used by the frontend application and should be prefixed with `VITE_` to make them accessible to the frontend code.

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `VITE_SUPABASE_URL` | The URL of your Supabase project | No* | Fallback value for development |
| `VITE_SUPABASE_ANON_KEY` | The anonymous key for your Supabase project | No* | Fallback value for development |
| `VITE_APP_VERSION` | The version of the application | No | "1.0.0" |
| `VITE_ENABLE_ANALYTICS` | Whether to enable analytics | No | false |

*While these variables are technically not required for development (fallbacks are provided), they are **required for production**. The fallback values should never be used in production.

## Backend Environment Variables

These variables are used by the monitor service and other backend processes.

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `SUPABASE_URL` | The URL of your Supabase project | Yes | - |
| `SUPABASE_ANON_KEY` | The anonymous key for your Supabase project | No | - |
| `SUPABASE_SERVICE_KEY` | The service role key for your Supabase project | Yes | - |
| `SUPABASE_PROJECT_REF` | The reference ID of your Supabase project | Yes | - |
| `LOG_FILE_PATH` | The path to the log file | No | "C:\\VUM\\monitor-checker-service.log" |
| `CHECK_INTERVAL` | The interval in milliseconds between monitor checks | No | 60000 |
| `LOG_LEVEL` | The log level (debug, info, warn, error) | No | "info" |
| `MONITOR_TABLE` | The name of the monitors table | No | "monitors" |
| `HISTORY_TABLE` | The name of the monitor history table | No | "monitor_history" |
| `NOTIFICATION_TABLE` | The name of the notifications table | No | "notifications" |
| `MAX_CONCURRENT_CHECKS` | The maximum number of concurrent checks | No | 10 |
| `RESEND_API_KEY` | The API key for Resend email service | No | - |
| `ENABLE_EMAIL_ALERTS` | Whether to enable email alerts | No | false |

## Database Connection Variables

These variables are used by the notification listener service to connect directly to the PostgreSQL database.

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `DB_HOST` | The hostname of the database | Yes | - |
| `DB_PORT` | The port of the database | No | 5432 |
| `DB_NAME` | The name of the database | No | "postgres" |
| `DB_USER` | The username for the database | No | "postgres" |
| `DB_PASSWORD` | The password for the database | Yes | - |

## Environment Files

The application uses the following environment files:

- `.env`: Default environment variables (committed to the repository, should not contain sensitive information)
- `.env.local`: Local development environment variables (not committed to the repository)
- `.env.example`: Example environment variables (committed to the repository)
- `.env.local.example`: Example local development environment variables (committed to the repository)

## Security Considerations

- Never commit sensitive information like API keys to the repository
- Use environment variables for all sensitive information
- Use different keys for development and production environments
- Rotate keys regularly using the key rotation service
