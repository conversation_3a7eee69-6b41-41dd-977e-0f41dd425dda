import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompany } from '@/contexts/CompanyContext';
import { useCompanies } from '@/hooks/use-companies';
import { useUsers, UserWithRoles, CreateUserData, UpdateUserData } from '@/hooks/use-users';
import { toast } from '@/components/ui/use-toast';

export const useUserManagement = () => {
  const { user } = useAuth();
  const { useGetCompaniesQuery } = useCompanies();
  const { data: companies = [] } = useGetCompaniesQuery();
  const {
    useGetUsersQuery,
    useCreateUserMutation,
    useUpdateUserMutation,
    useDeleteUserMutation,
    useAssignUserToCompanyMutation,
    useRemoveUserFromCompanyMutation,
    useSetSuperadminStatusMutation
  } = useUsers();

  // State for user management
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlySuperadmins, setShowOnlySuperadmins] = useState(false);
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [deleteUserDialogOpen, setDeleteUserDialogOpen] = useState(false);
  const [assignCompanyDialogOpen, setAssignCompanyDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithRoles | null>(null);

  // Form state
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPassword, setNewUserPassword] = useState('');
  const [newUserFullName, setNewUserFullName] = useState('');
  const [editUserEmail, setEditUserEmail] = useState('');
  const [editUserFullName, setEditUserFullName] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [selectedCompanyRole, setSelectedCompanyRole] = useState('member');

  // Fetch users
  const { data: users = [], isLoading, error, refetch } = useGetUsersQuery();

  // Mutations
  const createUserMutation = useCreateUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const deleteUserMutation = useDeleteUserMutation();
  const assignUserToCompanyMutation = useAssignUserToCompanyMutation();
  const removeUserFromCompanyMutation = useRemoveUserFromCompanyMutation();
  const setSuperadminStatusMutation = useSetSuperadminStatusMutation();

  // Filter users based on search query and superadmin filter
  const filteredUsers = users.filter(user => {
    // Apply search filter
    const matchesSearch =
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchQuery.toLowerCase()));

    // Apply superadmin filter if enabled
    const matchesSuperadminFilter = !showOnlySuperadmins || user.is_superadmin;

    return matchesSearch && matchesSuperadminFilter;
  });

  // Handle create user
  const handleCreateUser = async () => {
    if (!newUserEmail || !newUserPassword) {
      toast({
        title: 'Missing fields',
        description: 'Email and password are required.',
        variant: 'destructive',
      });
      return;
    }

    const userData: CreateUserData = {
      email: newUserEmail,
      password: newUserPassword,
      full_name: newUserFullName || undefined,
    };

    try {
      await createUserMutation.mutateAsync(userData);
      setCreateUserDialogOpen(false);
      resetCreateUserForm();
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  // Handle edit user
  const handleEditUser = async () => {
    if (!selectedUser) return;

    const userData: UpdateUserData = {};
    if (editUserEmail && editUserEmail !== selectedUser.email) {
      userData.email = editUserEmail;
    }
    if (editUserFullName !== selectedUser.full_name) {
      userData.full_name = editUserFullName;
    }

    if (Object.keys(userData).length === 0) {
      setEditUserDialogOpen(false);
      return;
    }

    try {
      await updateUserMutation.mutateAsync({
        userId: selectedUser.id,
        data: userData,
      });
      setEditUserDialogOpen(false);
    } catch (error) {
      console.error('Error updating user:', error);
    }
  };

  // Handle delete user
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      await deleteUserMutation.mutateAsync(selectedUser.id);
      setDeleteUserDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  // Handle assign user to company
  const handleAssignUserToCompany = async () => {
    if (!selectedUser || !selectedCompanyId || !selectedCompanyRole) return;

    try {
      await assignUserToCompanyMutation.mutateAsync({
        userId: selectedUser.id,
        companyId: selectedCompanyId,
        role: selectedCompanyRole,
      });
      setAssignCompanyDialogOpen(false);
      setSelectedCompanyId('');
      setSelectedCompanyRole('member');
    } catch (error) {
      console.error('Error assigning user to company:', error);
    }
  };

  // Handle remove user from company
  const handleRemoveUserFromCompany = async (userId: string, companyId: string) => {
    try {
      await removeUserFromCompanyMutation.mutateAsync({
        userId,
        companyId,
      });
    } catch (error) {
      console.error('Error removing user from company:', error);
    }
  };

  // Handle toggle superadmin status
  const handleToggleSuperadminStatus = async (userId: string, isSuperadmin: boolean) => {
    try {
      await setSuperadminStatusMutation.mutateAsync({
        userId,
        isSuperadmin: !isSuperadmin,
      });
    } catch (error) {
      console.error('Error toggling superadmin status:', error);
    }
  };

  // Reset create user form
  const resetCreateUserForm = () => {
    setNewUserEmail('');
    setNewUserPassword('');
    setNewUserFullName('');
  };

  // Set edit user form values
  const setEditUserFormValues = (user: UserWithRoles) => {
    setSelectedUser(user);
    setEditUserEmail(user.email);
    setEditUserFullName(user.full_name || '');
    setEditUserDialogOpen(true);
  };

  // Get available companies for assignment (companies the user is not already a member of)
  const getAvailableCompanies = (user: UserWithRoles) => {
    const userCompanyIds = user.companies.map(c => c.company_id);
    return companies.filter(company => !userCompanyIds.includes(company.id));
  };

  return {
    // State
    user,
    users,
    filteredUsers,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
    showOnlySuperadmins,
    setShowOnlySuperadmins,
    createUserDialogOpen,
    setCreateUserDialogOpen,
    editUserDialogOpen,
    setEditUserDialogOpen,
    deleteUserDialogOpen,
    setDeleteUserDialogOpen,
    assignCompanyDialogOpen,
    setAssignCompanyDialogOpen,
    selectedUser,
    setSelectedUser,
    newUserEmail,
    setNewUserEmail,
    newUserPassword,
    setNewUserPassword,
    newUserFullName,
    setNewUserFullName,
    editUserEmail,
    setEditUserEmail,
    editUserFullName,
    setEditUserFullName,
    selectedCompanyId,
    setSelectedCompanyId,
    selectedCompanyRole,
    setSelectedCompanyRole,
    
    // Mutations
    createUserMutation,
    updateUserMutation,
    deleteUserMutation,
    assignUserToCompanyMutation,
    removeUserFromCompanyMutation,
    setSuperadminStatusMutation,
    
    // Functions
    handleCreateUser,
    handleEditUser,
    handleDeleteUser,
    handleAssignUserToCompany,
    handleRemoveUserFromCompany,
    handleToggleSuperadminStatus,
    resetCreateUserForm,
    setEditUserFormValues,
    getAvailableCompanies,
    refetch
  };
};
