-- Comprehensive degraded status functions
-- This script creates enhanced functions for degraded status checking

-- Create a comprehensive function to check if a monitor is degraded
CREATE OR REPLACE FUNCTION is_monitor_degraded(
    p_monitor_id UUID,
    p_response_time INTEGER,
    p_status_code INTEGER DEFAULT NULL,
    p_consecutive_failures INTEGER DEFAULT 0,
    p_error_rate DECIMAL DEFAULT 0
) RETURNS JSONB AS $$
DECLARE
    settings JSONB;
    is_degraded BOOLEAN := FALSE;
    degraded_reasons TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Get the effective degraded settings for this monitor
    SELECT get_monitor_degraded_settings(p_monitor_id) INTO settings;
    
    -- Check response time threshold (TRIGGERS DEGRADED STATUS)
    IF p_response_time IS NOT NULL AND p_response_time > (settings->>'response_time')::INTEGER THEN
        is_degraded := TRUE;
        degraded_reasons := array_append(degraded_reasons,
            format('Response time %sms exceeds threshold %sms',
                p_response_time, settings->>'response_time'));
    END IF;

    -- Check status code threshold (TRIGGERS DEGRADED STATUS)
    IF p_status_code IS NOT NULL AND settings->'status_codes' IS NOT NULL THEN
        IF p_status_code = ANY(ARRAY(SELECT jsonb_array_elements_text(settings->'status_codes'))::INTEGER[]) THEN
            is_degraded := TRUE;
            degraded_reasons := array_append(degraded_reasons,
                format('Status code %s indicates degraded service', p_status_code));
        END IF;
    END IF;

    -- Track consecutive failures threshold (FOR FUTURE CUSTOMER ADVISEMENT - DOES NOT TRIGGER DEGRADED)
    -- Note: This is tracked but does not set is_degraded = TRUE

    -- Track error rate threshold (FOR FUTURE CUSTOMER ADVISEMENT - DOES NOT TRIGGER DEGRADED)
    -- Note: This is tracked but does not set is_degraded = TRUE
    
    -- Return comprehensive result
    RETURN jsonb_build_object(
        'is_degraded', is_degraded,
        'reasons', degraded_reasons,
        'settings_used', settings,
        'checked_values', jsonb_build_object(
            'response_time', p_response_time,
            'status_code', p_status_code,
            'consecutive_failures', p_consecutive_failures,
            'error_rate', p_error_rate
        ),
        'advisory_metrics', jsonb_build_object(
            'consecutive_failures_threshold_exceeded',
                CASE WHEN p_consecutive_failures >= (settings->>'consecutive_failures')::INTEGER
                     THEN TRUE ELSE FALSE END,
            'error_rate_threshold_exceeded',
                CASE WHEN p_error_rate >= (settings->>'error_rate')::DECIMAL
                     THEN TRUE ELSE FALSE END,
            'consecutive_failures_info',
                format('Consecutive failures: %s (threshold: %s)',
                    p_consecutive_failures, settings->>'consecutive_failures'),
            'error_rate_info',
                format('Error rate: %s%% (threshold: %s%%)',
                    p_error_rate, settings->>'error_rate')
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Create a function to get consecutive failures for a monitor
CREATE OR REPLACE FUNCTION get_monitor_consecutive_failures(p_monitor_id UUID)
RETURNS INTEGER AS $$
DECLARE
    consecutive_count INTEGER := 0;
    check_record RECORD;
BEGIN
    -- Count consecutive failures from the most recent checks
    FOR check_record IN 
        SELECT status 
        FROM monitor_history 
        WHERE monitor_id = p_monitor_id 
        ORDER BY checked_at DESC
        LIMIT 50  -- Limit to prevent excessive processing
    LOOP
        IF check_record.status = FALSE THEN
            consecutive_count := consecutive_count + 1;
        ELSE
            -- Stop counting when we hit a successful check
            EXIT;
        END IF;
    END LOOP;
    
    RETURN consecutive_count;
END;
$$ LANGUAGE plpgsql;

-- Create a function to calculate error rate for a monitor
CREATE OR REPLACE FUNCTION get_monitor_error_rate(
    p_monitor_id UUID,
    p_time_window_minutes INTEGER DEFAULT 60
) RETURNS DECIMAL AS $$
DECLARE
    total_checks INTEGER := 0;
    failed_checks INTEGER := 0;
    error_rate DECIMAL := 0;
BEGIN
    -- Count total and failed checks within the time window
    SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = FALSE THEN 1 END) as failed
    INTO total_checks, failed_checks
    FROM monitor_history 
    WHERE monitor_id = p_monitor_id 
    AND checked_at >= NOW() - INTERVAL '1 minute' * p_time_window_minutes;
    
    -- Calculate error rate as percentage
    IF total_checks > 0 THEN
        error_rate := (failed_checks::DECIMAL / total_checks::DECIMAL) * 100;
    END IF;
    
    RETURN error_rate;
END;
$$ LANGUAGE plpgsql;

-- Create a comprehensive monitor status evaluation function
CREATE OR REPLACE FUNCTION evaluate_monitor_status(
    p_monitor_id UUID,
    p_current_status BOOLEAN,
    p_response_time INTEGER,
    p_status_code INTEGER DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    consecutive_failures INTEGER;
    error_rate DECIMAL;
    degraded_check JSONB;
    final_status TEXT;
BEGIN
    -- Get additional metrics
    SELECT get_monitor_consecutive_failures(p_monitor_id) INTO consecutive_failures;
    SELECT get_monitor_error_rate(p_monitor_id, 60) INTO error_rate;
    
    -- Determine base status
    IF p_current_status THEN
        final_status := 'up';
    ELSE
        final_status := 'down';
    END IF;
    
    -- Check for degraded status only if monitor is currently up
    IF p_current_status THEN
        SELECT is_monitor_degraded(
            p_monitor_id,
            p_response_time,
            p_status_code,
            consecutive_failures,
            error_rate
        ) INTO degraded_check;
        
        -- If degraded conditions are met, change status to degraded
        IF (degraded_check->>'is_degraded')::BOOLEAN THEN
            final_status := 'degraded';
        END IF;
    END IF;
    
    -- Return comprehensive status information
    RETURN jsonb_build_object(
        'status', final_status,
        'response_time', p_response_time,
        'status_code', p_status_code,
        'consecutive_failures', consecutive_failures,
        'error_rate', error_rate,
        'degraded_check', degraded_check
    );
END;
$$ LANGUAGE plpgsql;

-- Add comments to the functions
COMMENT ON FUNCTION is_monitor_degraded(UUID, INTEGER, INTEGER, INTEGER, DECIMAL) IS 
'Comprehensive function to check if a monitor is in degraded state based on multiple criteria.';

COMMENT ON FUNCTION get_monitor_consecutive_failures(UUID) IS 
'Returns the number of consecutive failures for a monitor from recent history.';

COMMENT ON FUNCTION get_monitor_error_rate(UUID, INTEGER) IS 
'Calculates the error rate percentage for a monitor within a specified time window.';

COMMENT ON FUNCTION evaluate_monitor_status(UUID, BOOLEAN, INTEGER, INTEGER) IS 
'Comprehensive monitor status evaluation including degraded state detection.';
