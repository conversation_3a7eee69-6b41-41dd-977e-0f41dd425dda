// This script provides code snippets to fix the monitor service
// Apply these changes to your monitor service code

// 1. Find the function that saves check results
// It might look something like this:

async function saveCheckResult(monitor, status, responseTime, errorMessage) {
  try {
    // IMPORTANT: Ensure status is a boolean
    const booleanStatus = Boolean(status);
    
    const checkResult = {
      monitor_id: monitor.id,
      status: booleanStatus, // This must be a boolean
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    logger.info(`Saving check result: ${JSON.stringify(checkResult)}`);
    
    const { error } = await supabase
      .from('monitor_history')
      .insert(checkResult);
      
    if (error) {
      logger.error(`Error saving check result: ${error.message}`);
      throw error;
    }
    
    logger.info(`Saved check result for ${monitor.name}: ${booleanStatus ? 'UP' : 'DOWN'} (${responseTime}ms)`);
    return true;
  } catch (error) {
    logger.error(`Error saving check result for monitor ${monitor.id}: ${error.message}`);
    return false;
  }
}

// 2. Find the function that performs checks
// It might look something like this:

async function performCheck(monitor) {
  try {
    logger.info(`Checking monitor: ${monitor.name} (${monitor.id})`);
    logger.info(`Target: ${monitor.target}, Type: ${monitor.type}, Timeout: ${monitor.timeout}s`);
    
    const startTime = Date.now();
    let status = false; // Initialize as boolean
    let responseTime = null;
    let errorMessage = null;
    
    // For HTTP checks
    if (monitor.type === 'http') {
      try {
        logger.info(`Sending HTTP request to ${monitor.target}...`);
        
        const options = {
          timeout: monitor.timeout * 1000,
          validateStatus: null, // Don't throw on any status code
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)'
          },
          maxRedirects: 5
        };
        
        logger.info(`Request options: ${JSON.stringify(options)}`);
        
        const response = await axios.get(monitor.target, options);
        
        responseTime = Date.now() - startTime;
        
        // IMPORTANT: Make sure this is a boolean
        status = response.status >= 200 && response.status < 300;
        
        logger.info(`Response received: Status ${response.status}, Time ${responseTime}ms`);
        
        if (!status) {
          errorMessage = `HTTP status: ${response.status}`;
        }
      } catch (error) {
        responseTime = Date.now() - startTime;
        errorMessage = error.message;
        logger.error(`Error checking ${monitor.target}: ${error.message}`);
      }
    }
    
    // IMPORTANT: Save the check result with a boolean status
    await saveCheckResult(monitor, Boolean(status), responseTime, errorMessage);
    
    return { 
      status: Boolean(status),
      responseTime, 
      errorMessage
    };
  } catch (error) {
    logger.error(`Error performing check for ${monitor.name}: ${error.message}`);
    return { status: false, responseTime: null, errorMessage: error.message };
  }
}

// 3. Add a debug function to check the status type
// This can help diagnose issues:

function debugStatusType(status) {
  logger.info(`Status value: ${status}, Type: ${typeof status}`);
  
  // Convert to boolean if needed
  const booleanStatus = Boolean(status);
  logger.info(`Converted to boolean: ${booleanStatus}, Type: ${typeof booleanStatus}`);
  
  return booleanStatus;
}

// 4. Add this to your main monitor service file to test the database connection:

async function testDatabaseConnection() {
  try {
    logger.info('Testing database connection...');
    
    // Test basic connection
    const { data, error } = await supabase
      .from('monitors')
      .select('count(*)', { count: 'exact', head: true });
      
    if (error) {
      logger.error(`Database connection error: ${error.message}`);
      return false;
    }
    
    logger.info(`Database connection successful. Found ${data.count} monitors.`);
    
    // Test monitor_history table
    const { data: historyData, error: historyError } = await supabase
      .from('monitor_history')
      .select('count(*)', { count: 'exact', head: true });
      
    if (historyError) {
      logger.error(`Error accessing monitor_history table: ${historyError.message}`);
      return false;
    }
    
    logger.info(`Monitor history table accessible. Found ${historyData.count} records.`);
    
    // Test inserting a record
    const testMonitor = {
      id: '00000000-0000-0000-0000-000000000000', // Use a fake ID
      name: 'Test Monitor'
    };
    
    const testResult = {
      monitor_id: testMonitor.id,
      status: true, // Boolean
      response_time: 100,
      error_message: 'Test record',
      timestamp: new Date().toISOString()
    };
    
    logger.info(`Testing record insert with status type: ${typeof testResult.status}`);
    
    const { error: insertError } = await supabase
      .from('monitor_history')
      .insert(testResult);
      
    if (insertError) {
      logger.error(`Error inserting test record: ${insertError.message}`);
      return false;
    }
    
    logger.info('Test record inserted successfully');
    
    // Clean up test record
    await supabase
      .from('monitor_history')
      .delete()
      .eq('monitor_id', testMonitor.id);
      
    logger.info('Test record cleaned up');
    
    return true;
  } catch (error) {
    logger.error(`Database test error: ${error.message}`);
    return false;
  }
}

// Call this function when your service starts
testDatabaseConnection()
  .then(success => {
    if (success) {
      logger.info('Database tests passed. Service ready to run.');
    } else {
      logger.error('Database tests failed. Service may not work correctly.');
    }
  })
  .catch(error => {
    logger.error(`Database test error: ${error.message}`);
  });
