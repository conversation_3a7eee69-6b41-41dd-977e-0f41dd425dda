-- This script checks if a specific user exists in the users table
-- Run this in the Supabase SQL Editor

-- Check if the user exists in auth.users
SELECT 
    id, 
    email, 
    raw_user_meta_data
FROM 
    auth.users 
WHERE 
    id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';

-- Check if the user exists in public.users
SELECT 
    id, 
    email, 
    full_name
FROM 
    public.users 
WHERE 
    id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';
