-- Create or replace the is_global_superadmin_bypass function
-- This function checks if the current user is a superadmin without using RLS
-- This avoids circular dependencies in RLS policies
CREATE OR REPLACE FUNCTION public.is_global_superadmin_bypass()
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  is_superadmin boolean;
BEGIN
  -- Direct query to check if the current user is a superadmin
  -- This bypasses RLS by using SECURITY DEFINER
  SELECT EXISTS (
    SELECT 1 
    FROM public.user_roles 
    WHERE user_id = auth.uid() 
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_global_superadmin_bypass() TO authenticated;

-- Create or replace the is_global_superadmin function
-- This is the standard function that respects RLS
CREATE OR REPLACE FUNCTION public.is_global_superadmin()
RETURNS boolean
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
  is_superadmin boolean;
BEGIN
  -- Check if the current user is a superadmin
  SELECT EXISTS (
    SELECT 1 
    FROM public.user_roles 
    WHERE user_id = auth.uid() 
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.is_global_superadmin() TO authenticated;
