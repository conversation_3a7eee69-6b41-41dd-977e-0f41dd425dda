// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface Monitor {
  id: string
  name: string
  target: string
  type: string
  interval: number
  timeout: number
  active: boolean
  user_id: string
  company_id: string | null
  created_at: string
}

interface MonitorCompany {
  id: string
  monitor_id: string
  company_id: string
  created_at: string
}

interface RequestBody {
  action: 'get_monitor_companies' | 'assign_monitor_to_companies' | 'remove_monitor_from_company'
  monitor_id: string
  company_ids?: string[]
  company_id?: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Get the user from the request
    const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Parse the request body
    const requestData: RequestBody = await req.json()
    const { action, monitor_id } = requestData

    // Validate the monitor_id
    if (!monitor_id) {
      return new Response(JSON.stringify({ error: 'monitor_id is required' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      })
    }

    // Check if the user has access to the monitor
    const { data: monitor, error: monitorError } = await supabaseClient
      .from('monitors')
      .select('*')
      .eq('id', monitor_id)
      .eq('user_id', user.id)
      .single()

    if (monitorError || !monitor) {
      return new Response(JSON.stringify({ error: 'Monitor not found or access denied' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      })
    }

    // Handle different actions
    switch (action) {
      case 'get_monitor_companies':
        // Get all companies associated with the monitor
        const { data: companies, error: companiesError } = await supabaseClient
          .from('monitor_companies')
          .select('*, company:company_id(id, name)')
          .eq('monitor_id', monitor_id)

        if (companiesError) {
          return new Response(JSON.stringify({ error: companiesError.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        return new Response(JSON.stringify({ companies }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })

      case 'assign_monitor_to_companies':
        const { company_ids } = requestData
        
        if (!company_ids || !Array.isArray(company_ids) || company_ids.length === 0) {
          return new Response(JSON.stringify({ error: 'company_ids is required and must be a non-empty array' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          })
        }

        // First, check if the user has admin access to all the companies
        const { data: userCompanies, error: userCompaniesError } = await supabaseClient
          .from('company_members')
          .select('company_id, role')
          .eq('user_id', user.id)
          .in('company_id', company_ids)
          .eq('role', 'admin')

        if (userCompaniesError) {
          return new Response(JSON.stringify({ error: userCompaniesError.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        // Verify that the user is an admin in all the requested companies
        if (!userCompanies || userCompanies.length !== company_ids.length) {
          return new Response(JSON.stringify({ error: 'You must be an admin in all companies to assign monitors' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
          })
        }

        // Delete existing relationships
        const { error: deleteError } = await supabaseClient
          .from('monitor_companies')
          .delete()
          .eq('monitor_id', monitor_id)

        if (deleteError) {
          return new Response(JSON.stringify({ error: deleteError.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        // Create new relationships
        const monitorCompanies = company_ids.map(company_id => ({
          monitor_id,
          company_id
        }))

        const { data: insertedCompanies, error: insertError } = await supabaseClient
          .from('monitor_companies')
          .insert(monitorCompanies)
          .select()

        if (insertError) {
          return new Response(JSON.stringify({ error: insertError.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        return new Response(JSON.stringify({ success: true, companies: insertedCompanies }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })

      case 'remove_monitor_from_company':
        const { company_id } = requestData
        
        if (!company_id) {
          return new Response(JSON.stringify({ error: 'company_id is required' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 400,
          })
        }

        // Check if the user is an admin in the company
        const { data: userCompany, error: userCompanyError } = await supabaseClient
          .from('company_members')
          .select('role')
          .eq('user_id', user.id)
          .eq('company_id', company_id)
          .eq('role', 'admin')
          .single()

        if (userCompanyError || !userCompany) {
          return new Response(JSON.stringify({ error: 'You must be an admin in the company to remove monitors' }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 403,
          })
        }

        // Remove the relationship
        const { error: removeError } = await supabaseClient
          .from('monitor_companies')
          .delete()
          .eq('monitor_id', monitor_id)
          .eq('company_id', company_id)

        if (removeError) {
          return new Response(JSON.stringify({ error: removeError.message }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500,
          })
        }

        return new Response(JSON.stringify({ success: true }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        })
    }
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
