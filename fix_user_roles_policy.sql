-- This script fixes the infinite recursion in the user_roles policy
-- Run this in the Supabase SQL Editor

-- First, drop the problematic policy
DROP POLICY IF EXISTS "Only superadmins can manage global roles" ON public.user_roles;

-- Create a new policy with a service role bypass
CREATE POLICY "Service role can manage global roles"
ON public.user_roles
FOR ALL
USING (
    -- Allow the service role to manage all roles
    (SELECT current_setting('request.jwt.claims', true)::json->>'role' = 'service_role')
);

-- Create a policy for the first superadmin
CREATE POLICY "First superadmin can be created"
ON public.user_roles
FOR INSERT
WITH CHECK (
    -- Allow inserting the first superadmin when no superadmins exist
    NOT EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE role_type = 'superadmin'
    )
);

-- Create a policy for superadmins to manage other roles
CREATE POLICY "Superadmins can manage global roles"
ON public.user_roles
FOR ALL
USING (
    -- Allow superadmins to manage all roles except their own
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_id = auth.uid()
        AND role_type = 'superadmin'
    )
    AND
    -- Prevent superadmins from deleting their own role
    (current_setting('request.method') <> 'DELETE' OR user_id <> auth.uid())
);

-- Make <EMAIL> a superadmin
DO $$
DECLARE
    target_user_id UUID;
BEGIN
    -- Get the user ID from auth.users
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    -- Check if the user exists
    IF target_user_id IS NULL THEN
        RAISE NOTICE 'User <NAME_EMAIL> not found';
        RETURN;
    END IF;
    
    -- Insert the user as a global superadmin
    -- This uses the ON CONFLICT clause to handle the case where the user is already a superadmin
    INSERT INTO public.user_roles (user_id, role_type)
    VALUES (target_user_id, 'superadmin')
    ON CONFLICT (user_id, role_type) 
    DO UPDATE SET updated_at = NOW();
    
    RAISE NOTICE 'User <EMAIL> (ID: %) has been made a global superadmin', target_user_id;
END $$;

-- Verify the user is now a superadmin
SELECT 
    u.id,
    u.email,
    ur.role_type AS global_role,
    ur.created_at,
    ur.updated_at
FROM 
    auth.users u
JOIN 
    public.user_roles ur ON u.id = ur.user_id
WHERE 
    u.email = '<EMAIL>'
    AND ur.role_type = 'superadmin';
