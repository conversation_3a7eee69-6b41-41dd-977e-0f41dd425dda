import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YA<PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Skeleton } from "@/components/ui/skeleton";
import { formatTooltipDateTime } from "@/utils/dateFormat";

interface ChartDataPoint {
  time: string;
  value: number;
  date: Date;
}

interface MonitorResponseTimeChartProps {
  chartData: ChartDataPoint[];
  isLoading: boolean;
  retentionDays: number;
}

const MonitorResponseTimeChart: React.FC<MonitorResponseTimeChartProps> = ({
  chartData,
  isLoading,
  retentionDays
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Response Time</CardTitle>
        <CardDescription>
          Response time history (last {retentionDays} days)
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="h-[300px] w-full flex items-center justify-center">
            <Skeleton className="h-[250px] w-full" />
          </div>
        ) : chartData.length > 0 ? (
          <div className="h-[350px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={chartData}
                margin={{
                  top: 5,
                  right: 30,
                  left: 20,
                  bottom: 50, // Increased to accommodate tilted labels
                }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="time"
                  tick={{ fontSize: 10 }}
                  interval="preserveStartEnd"
                  minTickGap={30}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis unit="ms" />
                <Tooltip
                  formatter={(value) => [`${value} ms`, 'Response Time']}
                  labelFormatter={(label, payload) => {
                    if (payload && payload[0] && payload[0].payload.date) {
                      return formatTooltipDateTime(payload[0].payload.date);
                    }
                    return `Time: ${label}`;
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  dot={{ r: 4 }}
                  activeDot={{ r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="h-[300px] w-full flex items-center justify-center">
            <p className="text-slate-500 dark:text-slate-400">No data available yet</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MonitorResponseTimeChart;
