// <PERSON>ript to show the last N checks for each monitor
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to format date and time
function formatDateTime(dateString) {
  const date = new Date(dateString);
  
  // Format date as DD/MM/YYYY
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  // Format time as HH:MM:SS
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
}

// Function to get the last N checks for each monitor
async function getLastChecks(checksPerMonitor = 10) {
  try {
    // Get all monitors
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('id, name, type, target')
      .order('name');
    
    if (monitorsError) {
      throw new Error(`Failed to get monitors: ${monitorsError.message}`);
    }
    
    if (!monitors || monitors.length === 0) {
      console.log('No monitors found');
      return;
    }
    
    console.log(`Found ${monitors.length} monitors`);
    console.log('='.repeat(80));
    
    // For each monitor, get the last N checks
    for (const monitor of monitors) {
      const { data: checks, error: checksError } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(checksPerMonitor);
      
      if (checksError) {
        console.error(`Error getting checks for monitor ${monitor.name}: ${checksError.message}`);
        continue;
      }
      
      if (!checks || checks.length === 0) {
        console.log(`Monitor: ${monitor.name} (${monitor.type}: ${monitor.target})`);
        console.log('  No checks found');
        console.log('-'.repeat(80));
        continue;
      }
      
      console.log(`Monitor: ${monitor.name} (${monitor.type}: ${monitor.target})`);
      
      // Display each check
      checks.forEach((check, index) => {
        const status = typeof check.status === 'boolean' 
          ? (check.status ? 'UP' : 'DOWN')
          : check.status;
        
        const formattedTime = formatDateTime(check.timestamp);
        
        console.log(`  ${index + 1}. ${formattedTime} - Status: ${status}, Response time: ${check.response_time}ms`);
        if (check.error_message) {
          console.log(`     Error: ${check.error_message}`);
        }
      });
      
      console.log('-'.repeat(80));
    }
  } catch (error) {
    console.error(`Error getting checks: ${error.message}`);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const checksPerMonitor = args.find(arg => arg.startsWith('--count='))?.split('=')[1] || 10;

// Run the function
getLastChecks(parseInt(checksPerMonitor))
  .then(() => process.exit(0))
  .catch(error => {
    console.error(error);
    process.exit(1);
  });
