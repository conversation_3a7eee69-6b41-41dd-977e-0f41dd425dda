
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import DocumentTitle from "../components/DocumentTitle";
import AppLayout from "@/components/AppLayout";
import UnifiedHeader from "@/components/UnifiedHeader";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import MonitorList from "@/components/MonitorList";
import CondensedMonitorList from "@/components/CondensedMonitorList";
import LayoutToggle, { LayoutType } from "@/components/LayoutToggle";
import StatusOverview from "@/components/StatusOverview";

import { useCompany } from "@/contexts/CompanyContext";
import { useMonitors } from "@/hooks/use-monitors";
import { useMonitorHistory } from "@/hooks/use-monitor-history";
import { useCompanyRoles } from "@/hooks/use-company-roles";
import { useQueryClient } from "@tanstack/react-query";




const Dashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  const { useGetMonitorsQuery } = useMonitors();
  const { currentCompany, isAdmin } = useCompany();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();
  const [layout, setLayout] = useState<LayoutType>('standard');

  // Check for refresh parameter in URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const refreshParam = searchParams.get('refresh');

    if (refreshParam) {
      console.log('Refresh parameter detected, invalidating monitor queries');
      // Invalidate all monitor-related queries to force a refresh
      queryClient.invalidateQueries({ queryKey: ['monitors'] });

      // Remove the refresh parameter from the URL without navigating
      const newUrl = window.location.pathname;
      window.history.replaceState({}, '', newUrl);
    }
  }, [location.search, queryClient]);

  // Force a refresh when the company changes
  useEffect(() => {
    if (currentCompany) {
      console.log(`Dashboard detected company change to: ${currentCompany.name} (${currentCompany.id})`);
      // Refetch the monitors data when the company changes
      queryClient.invalidateQueries({ queryKey: ['monitors'] });
    }
  }, [currentCompany?.id, queryClient]);

  // Fetch monitors data
  const { data: monitors, isLoading: monitorsLoading } = useGetMonitorsQuery();

  // Get the latest history for all monitors
  const { useGetLatestHistoryForMonitors } = useMonitorHistory();
  const { data: monitorHistories = {}, isLoading: historiesLoading } = useGetLatestHistoryForMonitors(
    monitors?.map(m => m.id) || []
  );

  // Process monitors data for the condensed view
  const processedMonitors = monitors?.map(monitor => {
    const history = monitorHistories[monitor.id];

    // Determine status based on active flag and monitor status
    let status = 'unknown';
    if (!monitor.active) {
      status = 'paused';
    } else {
      // Use the status directly from the monitors table
      if (typeof monitor.status === 'string') {
        // Make sure the status is one of the allowed values
        const statusLower = monitor.status.toLowerCase();
        if (['up', 'down', 'degraded'].includes(statusLower)) {
          status = statusLower;
        } else {
          // Default to 'up' for any other string value
          status = 'up';
        }
      } else if (typeof monitor.status === 'boolean') {
        // Handle legacy boolean status
        status = monitor.status ? 'up' : 'down';
      }
    }

    // Ensure status is lowercase for consistency
    const normalizedStatus = status.toLowerCase();

    return {
      ...monitor,
      status: normalizedStatus,
      last_check_time: history?.timestamp || null,
      last_response_time: history?.response_time || null
    };
  }) || [];

  // Load saved layout preference from localStorage
  useEffect(() => {
    const savedLayout = localStorage.getItem('dashboard-layout');
    if (savedLayout === 'condensed' || savedLayout === 'standard') {
      setLayout(savedLayout as LayoutType);
    }
  }, []);

  // Save layout preference to localStorage
  const handleLayoutChange = (newLayout: LayoutType) => {
    setLayout(newLayout);
    localStorage.setItem('dashboard-layout', newLayout);
  };

  const header = (
    <UnifiedHeader
      title="Dashboard"
      showCompanySelector={true}
      actions={
        <>
          <div className="mr-2">
            <LayoutToggle currentLayout={layout} onLayoutChange={handleLayoutChange} />
          </div>
          {(isAdmin || !currentCompany || (isSuperadmin && currentCompany?.isAllCompanies)) && (
            <Button onClick={() => navigate("/add-monitor")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Monitor
            </Button>
          )}
        </>
      }
    />
  );

  return (
    <>
      <DocumentTitle title="Dashboard" />
      <AppLayout header={header}>
        {/* Main content */}
        <div className="flex-1 overflow-y-auto">
          <StatusOverview />



          <div className="mt-8">
            {monitorsLoading || historiesLoading ? (
              <div className="text-center py-8">Loading monitors...</div>
            ) : monitors && monitors.length > 0 ? (
              <>
                {layout === 'standard' ? (
                  <MonitorList monitors={monitors} />
                ) : (
                  <CondensedMonitorList monitors={processedMonitors} />
                )}
              </>
            ) : (
              <div className="text-center py-8 text-slate-500 dark:text-slate-400">
                No monitors found. Add your first monitor to get started.
                {!monitors && <p className="mt-2 text-sm">If you just registered, you may need to refresh the page.</p>}
              </div>
            )}
          </div>

          <Outlet />
        </div>
      </AppLayout>
    </>
  );
};

export default Dashboard;
