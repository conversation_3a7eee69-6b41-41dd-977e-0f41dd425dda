import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import axios from 'axios';

dotenv.config();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

// Email configuration
const ENABLE_EMAIL_ALERTS = process.env.ENABLE_EMAIL_ALERTS?.toLowerCase() === 'true';
const RESEND_API_KEY = process.env.RESEND_API_KEY;

if (ENABLE_EMAIL_ALERTS && !RESEND_API_KEY) {
    throw new Error('RESEND_API_KEY is not set in .env file and email alerts are enabled');
}

// Alert thresholds
const CONSECUTIVE_FAILURES_THRESHOLD = 3;
const RESPONSE_TIME_THRESHOLD = 5000; // 5 seconds

export async function checkAndSendAlerts() {
    try {
        // Get monitors with consecutive failures
        const { data: recentHistory, error: historyError } = await supabase
            .from('monitor_history')
            .select('monitor_id, status, timestamp, error_message, response_time, monitors(name, target)')
            .order('timestamp', { ascending: false });

        if (historyError) throw historyError;

        // Process monitor history to find issues
        const monitorStatus = new Map();
        const failedMonitors = [];
        const slowMonitors = [];

        for (const record of recentHistory) {
            if (!monitorStatus.has(record.monitor_id)) {
                let consecutiveFailures = 0;
                let highResponseTimes = 0;
                
                // Check last few entries for this monitor
                const monitorRecords = recentHistory
                    .filter(r => r.monitor_id === record.monitor_id)
                    .slice(0, 5); // Look at last 5 records

                for (const r of monitorRecords) {
                    if (!r.status) consecutiveFailures++;
                    if (r.response_time > RESPONSE_TIME_THRESHOLD) highResponseTimes++;
                }

                if (consecutiveFailures >= CONSECUTIVE_FAILURES_THRESHOLD) {
                    failedMonitors.push({
                        id: record.monitor_id,
                        name: record.monitors.name,
                        url: record.monitors.target,
                        last_check: record.timestamp,
                        consecutive_failures: consecutiveFailures,
                        last_error: record.error_message
                    });
                }

                if (highResponseTimes >= 3) { // At least 3 slow responses
                    slowMonitors.push({
                        id: record.monitor_id,
                        name: record.monitors.name,
                        url: record.monitors.target,
                        last_check: record.timestamp,
                        response_time: record.response_time
                    });
                }

                monitorStatus.set(record.monitor_id, true);
            }
        }

        // Send alerts for failed monitors
        for (const monitor of failedMonitors) {
            await sendFailureAlert(monitor);
        }

        // Send alerts for slow monitors
        for (const monitor of slowMonitors) {
            await sendPerformanceAlert(monitor);
        }

        // Log alert activity
        await logAlertActivity(failedMonitors.length, slowMonitors.length);

    } catch (error) {
        console.error('Error in alert system:', error);
    }
}

async function sendFailureAlert(monitor) {
    if (!ENABLE_EMAIL_ALERTS) {
        console.log(`[DEV] Would send failure alert for monitor: ${monitor.name}`);
        return;
    }

    const emailContent = {
        from: process.env.SMTP_FROM,
        to: process.env.SMTP_USER, // Using configured email since we don't have alert_email in schema
        subject: `🔴 Monitor Alert: ${monitor.name} is DOWN`,
        html: `
            <h2>Monitor Status Alert</h2>
            <p>Monitor <strong>${monitor.name}</strong> is currently DOWN</p>
            <ul>
                <li>Last Check: ${new Date(monitor.last_check).toLocaleString()}</li>
                <li>Consecutive Failures: ${monitor.consecutive_failures}</li>
                <li>Last Error: ${monitor.last_error}</li>
            </ul>
            <p>Please check the system and take necessary action.</p>
        `
    };

    await axios.post('https://api.resend.com/emails', {
        from: 'Vurbis Uptime Monitor <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: emailContent.subject,
        html: emailContent.html
    }, {
        headers: {
            'Authorization': `Bearer ${RESEND_API_KEY}`,
            'Content-Type': 'application/json'
        }
    });
}

async function sendPerformanceAlert(monitor) {
    if (!ENABLE_EMAIL_ALERTS) {
        console.log(`[DEV] Would send performance alert for monitor: ${monitor.name}`);
        return;
    }

    const emailContent = {
        from: process.env.SMTP_FROM,
        to: process.env.SMTP_USER, // Using configured email since we don't have alert_email in schema
        subject: `⚠️ Monitor Alert: ${monitor.name} is running slow`,
        html: `
            <h2>Monitor Performance Alert</h2>
            <p>Monitor <strong>${monitor.name}</strong> is experiencing high response times</p>
            <ul>
                <li>Last Check: ${new Date(monitor.last_check).toLocaleString()}</li>
                <li>Response Time: ${monitor.response_time}ms</li>
                <li>Threshold: ${RESPONSE_TIME_THRESHOLD}ms</li>
            </ul>
            <p>Please investigate potential performance issues.</p>
        `
    };

    await axios.post('https://api.resend.com/emails', {
        from: 'Vurbis Uptime Monitor <<EMAIL>>',
        to: ['<EMAIL>'],
        subject: emailContent.subject,
        html: emailContent.html
    }, {
        headers: {
            'Authorization': `Bearer ${RESEND_API_KEY}`,
            'Content-Type': 'application/json'
        }
    });
}

async function logAlertActivity(failedCount, slowCount) {
    const { error } = await supabase
        .from('alert_logs')
        .insert([{
            timestamp: new Date().toISOString(),
            failed_monitors: failedCount,
            slow_monitors: slowCount
        }]);

    if (error) {
        console.error('Error logging alert activity:', error);
    }
}
