# Row Level Security (RLS) Policies for Vurbis Uptime Monitor

This directory contains SQL scripts to set up Row Level Security (RLS) policies for the Vurbis Uptime Monitor application. These policies ensure that:

1. Regular users can only see their own data
2. Company admins can see data for their companies
3. Superadmins can see and manage all data

## Files

- `create_superadmin_functions.sql`: Creates functions to check superadmin status without circular dependencies
- `update_users_rls.sql`: RLS policies for the users table
- `update_user_roles_rls.sql`: RLS policies for the user_roles table
- `update_company_members_rls.sql`: RLS policies for the company_members table

## How to Apply These Policies

### Option 1: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of each SQL file
4. Run the queries

### Option 2: Using the Supabase CLI

If you have the Supabase CLI installed, you can run:

```bash
# First, create the superadmin functions to avoid circular dependencies
supabase db execute --project-id your-project-id --file ./sql/create_superadmin_functions.sql

# Then apply the RLS policies
supabase db execute --project-id your-project-id --file ./sql/update_users_rls.sql
supabase db execute --project-id your-project-id --file ./sql/update_user_roles_rls.sql
supabase db execute --project-id your-project-id --file ./sql/update_company_members_rls.sql
```

**Important**: You must run the `create_superadmin_functions.sql` script first to avoid circular dependencies.

### Option 3: Using the Supabase REST API

You can also execute these SQL scripts using the Supabase REST API:

```bash
curl -X POST 'https://your-project-id.supabase.co/rest/v1/rpc/exec_sql' \
  -H 'apikey: YOUR_SUPABASE_KEY' \
  -H 'Authorization: Bearer YOUR_SUPABASE_KEY' \
  -H 'Content-Type: application/json' \
  --data '{"sql": "CONTENTS_OF_SQL_FILE"}'
```

## Verifying the Policies

After applying the policies, you can verify they are working correctly by:

1. Logging in as a superadmin user and checking that you can see all users
2. Logging in as a regular user and verifying you can only see your own data

## Troubleshooting

If you encounter issues with the RLS policies:

1. Check the browser console for error messages

2. Verify that the superadmin functions exist and are working correctly:

```sql
-- Check if the functions exist
SELECT proname, prosrc
FROM pg_proc
WHERE proname IN ('is_global_superadmin', 'is_global_superadmin_bypass');

-- Test the functions
SELECT is_global_superadmin_bypass();
SELECT is_global_superadmin();
```

3. Verify that the policies were applied correctly by querying the `pg_policies` table:

```sql
SELECT * FROM pg_policies WHERE tablename IN ('users', 'user_roles', 'company_members');
```

4. Make sure your superadmin user has the correct role in the `user_roles` table:

```sql
SELECT * FROM user_roles WHERE user_id = 'YOUR_USER_ID' AND role_type = 'superadmin';
```

5. If you see an "infinite recursion detected" error, it means there's still a circular dependency in your policies. Make sure you're using the `is_global_superadmin_bypass()` function in all policies that check for superadmin status.
