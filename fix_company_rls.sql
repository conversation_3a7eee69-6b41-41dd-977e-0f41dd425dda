-- This script fixes the RLS policies for the company-related tables
-- Run this in the Supabase SQL Editor

-- Enable Row Level Security if not already enabled
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_members ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can view companies they are members of" ON public.companies;
DROP POLICY IF EXISTS "Company admins can update their companies" ON public.companies;
DROP POLICY IF EXISTS "Company admins can delete their companies" ON public.companies;
DROP POLICY IF EXISTS "Authenticated users can create companies" ON public.companies;
DROP POLICY IF EXISTS "Users can view company members for their companies" ON public.company_members;
DROP POLICY IF EXISTS "Company admins can manage company members" ON public.company_members;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.companies;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.company_members;

-- Create simplified policies for companies table
CREATE POLICY "Allow all operations for authenticated users"
ON public.companies
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Create simplified policies for company_members table
CREATE POLICY "Allow all operations for authenticated users"
ON public.company_members
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

-- Verify the policies
SELECT tablename, policyname, permissive, roles, cmd, qual, with_check 
FROM pg_policies 
WHERE schemaname = 'public' AND (tablename = 'companies' OR tablename = 'company_members')
ORDER BY tablename, policyname;
