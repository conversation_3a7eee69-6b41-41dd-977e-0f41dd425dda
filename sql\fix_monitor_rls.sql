-- Drop the existing policy
DROP POLICY IF EXISTS "Users can view monitors in their companies" ON public.monitors;

-- Create a new policy that only allows viewing non-deleted monitors
CREATE POLICY "Users can view monitors in their companies" 
ON public.monitors 
FOR SELECT 
USING (
  -- Superadmins can see all monitors (including deleted ones)
  is_superadmin() OR 
  
  -- Users can see their own monitors that aren't associated with any company
  (user_id = auth.uid() AND 
   NOT EXISTS (SELECT 1 FROM monitor_companies WHERE monitor_companies.monitor_id = monitors.id) AND
   deleted = false) OR
   
  -- Users can see monitors in their companies that aren't deleted
  EXISTS (
    SELECT 1 
    FROM monitor_companies mc
    JOIN company_members cm ON mc.company_id = cm.company_id
    WHERE mc.monitor_id = monitors.id 
    AND cm.user_id = auth.uid()
    AND monitors.deleted = false
  )
);

-- Create a separate policy for admins to see deleted monitors
CREATE POLICY "Ad<PERSON> can view deleted monitors in their companies" 
ON public.monitors 
FOR SELECT 
USING (
  -- <PERSON><PERSON><PERSON> can see all monitors
  is_superadmin() OR 
  
  -- Ad<PERSON> can see deleted monitors in their companies
  EXISTS (
    SELECT 1 
    FROM monitor_companies mc
    JOIN company_members cm ON mc.company_id = cm.company_id
    WHERE mc.monitor_id = monitors.id 
    AND cm.user_id = auth.uid()
    AND cm.role_type = 'admin'
    AND monitors.deleted = true
  )
);
