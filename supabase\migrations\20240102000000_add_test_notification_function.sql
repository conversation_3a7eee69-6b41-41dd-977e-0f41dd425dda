-- Create a function to test the email notification system
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION test_monitor_notification(
  p_monitor_id UUID DEFAULT NULL,
  p_status TEXT DEFAULT 'down',
  p_company_id UUID DEFAULT NULL
) RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_monitor_id UUID;
  v_company_id UUID;
  v_monitor_name TEXT;
  v_message TEXT;
BEGIN
  -- If no monitor ID is provided, get the first active monitor
  IF p_monitor_id IS NULL THEN
    SELECT id, name INTO v_monitor_id, v_monitor_name
    FROM monitors
    WHERE active = true
    LIMIT 1;
    
    IF v_monitor_id IS NULL THEN
      RETURN 'No active monitors found. Please create a monitor first.';
    END IF;
  ELSE
    SELECT id, name INTO v_monitor_id, v_monitor_name
    FROM monitors
    WHERE id = p_monitor_id;
    
    IF v_monitor_id IS NULL THEN
      RETURN 'Monitor not found with ID: ' || p_monitor_id;
    END IF;
  END IF;
  
  -- If no company ID is provided, get the first company associated with the monitor
  IF p_company_id IS NULL THEN
    SELECT company_id INTO v_company_id
    FROM monitor_companies
    WHERE monitor_id = v_monitor_id
    LIMIT 1;
    
    IF v_company_id IS NULL THEN
      -- If no company is associated, get the first company
      SELECT id INTO v_company_id
      FROM companies
      LIMIT 1;
      
      IF v_company_id IS NULL THEN
        RETURN 'No companies found. Please create a company first.';
      END IF;
    END IF;
  ELSE
    v_company_id := p_company_id;
  END IF;
  
  -- Validate status
  IF p_status NOT IN ('up', 'down', 'degraded') THEN
    RAISE WARNING 'Invalid status: %. Defaulting to down', p_status;
    p_status := 'down';
  END IF;
  
  -- Create a test monitor history record to trigger the notification
  INSERT INTO monitor_history (
    monitor_id,
    status,
    response_time,
    error_message,
    timestamp
  ) VALUES (
    v_monitor_id,
    p_status,
    CASE WHEN p_status = 'up' THEN 200 ELSE NULL END,
    CASE WHEN p_status = 'up' THEN NULL ELSE 'Test error message' END,
    NOW()
  );
  
  -- Return success message
  RETURN 'Test notification triggered for monitor: ' || v_monitor_name || 
         ' with status: ' || p_status || 
         ' for company ID: ' || v_company_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION test_monitor_notification TO authenticated;
