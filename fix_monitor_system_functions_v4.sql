-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS public.execute_sql(text);
DROP FUNCTION IF EXISTS public.get_monitor_history_columns();
DROP FUNCTION IF EXISTS public.get_monitor_status_counts();

-- Create a function to execute dynamic SQL (with proper security checks)
CREATE OR REPLACE FUNCTION public.execute_sql(query_text TEXT)
RETURNS SETOF jsonb AS $$
BEGIN
  -- Only allow SELECT queries for security
  IF position('SELECT' in upper(query_text)) != 1 THEN
    RAISE EXCEPTION 'Only SELECT queries are allowed';
  END IF;
  
  RETURN QUERY EXECUTE query_text;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor history column information
CREATE OR REPLACE FUNCTION public.get_monitor_history_columns()
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  WITH ordered_columns AS (
    SELECT 
      c.column_name,
      c.data_type,
      c.is_nullable,
      c.ordinal_position
    FROM 
      information_schema.columns c
    WHERE 
      c.table_schema = 'public' 
      AND c.table_name = 'monitor_history'
  )
  SELECT jsonb_agg(
    jsonb_build_object(
      'column_name', column_name,
      'data_type', data_type,
      'is_nullable', is_nullable
    )
    ORDER BY ordinal_position
  ) INTO result
  FROM ordered_columns;
    
  RETURN COALESCE(result, '[]'::jsonb);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get monitor status counts
CREATE OR REPLACE FUNCTION public.get_monitor_status_counts()
RETURNS jsonb AS $$
DECLARE
  result jsonb;
BEGIN
  WITH latest_history AS (
    SELECT DISTINCT ON (monitor_id)
      monitor_id,
      status
    FROM
      monitor_history
    ORDER BY
      monitor_id,
      timestamp DESC
  )
  SELECT 
    jsonb_build_object(
      'total', COUNT(*)::int,
      'up', COUNT(CASE WHEN status = true THEN 1 END)::int,
      'down', COUNT(CASE WHEN status = false THEN 1 END)::int
    )
  INTO result
  FROM latest_history;
  
  RETURN COALESCE(result, jsonb_build_object(
    'total', 0,
    'up', 0,
    'down', 0
  ));
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION public.execute_sql(text) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_monitor_history_columns() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_monitor_status_counts() TO authenticated;
