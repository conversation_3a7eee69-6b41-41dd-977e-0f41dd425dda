import { supabase } from '@/integrations/supabase/client';
import { DegradedThresholds } from '@/types/monitor';

/**
 * Get degraded settings for a specific monitor, falling back to global settings
 * @param monitorId The ID of the monitor
 * @returns The combined degraded settings
 */
export async function getMonitorDegradedSettings(monitorId: string): Promise<DegradedThresholds> {
  try {
    // Try to get the settings from the database function
    const { data, error } = await supabase
      .rpc('get_monitor_degraded_settings', { monitor_id: monitorId });
    
    if (error) {
      console.error('Error getting degraded settings:', error);
      // Fallback to default values if there's an error
      return {
        response_time: 1000,
        error_rate: 10,
        status_codes: [429, 503],
        consecutive_failures: 2
      };
    }
    
    return data as DegradedThresholds;
  } catch (error) {
    console.error('Error getting degraded settings:', error);
    // Fallback to default values if there's an error
    return {
      response_time: 1000,
      error_rate: 10,
      status_codes: [429, 503],
      consecutive_failures: 2
    };
  }
}

/**
 * Get global degraded settings
 * @returns The global degraded settings
 */
export async function getGlobalDegradedSettings(): Promise<DegradedThresholds> {
  try {
    const { data, error } = await supabase
      .from('degraded_settings')
      .select('*')
      .limit(1)
      .single();
    
    if (error) {
      console.error('Error getting global degraded settings:', error);
      // Fallback to default values if there's an error
      return {
        response_time: 1000,
        error_rate: 10,
        status_codes: [429, 503],
        consecutive_failures: 2
      };
    }
    
    return {
      response_time: data.response_time,
      error_rate: data.error_rate,
      status_codes: data.status_codes || [],
      consecutive_failures: data.consecutive_failures
    };
  } catch (error) {
    console.error('Error getting global degraded settings:', error);
    // Fallback to default values if there's an error
    return {
      response_time: 1000,
      error_rate: 10,
      status_codes: [429, 503],
      consecutive_failures: 2
    };
  }
}

/**
 * Determine if a monitor is in degraded state based on its check results and settings
 * @param monitorId The ID of the monitor
 * @param responseTime The response time in milliseconds
 * @param statusCode The HTTP status code (optional)
 * @param consecutiveFailures The number of consecutive failures (optional)
 * @param errorRate The error rate percentage (optional)
 * @returns Comprehensive degraded status information
 */
export async function isMonitorDegraded(
  monitorId: string,
  responseTime: number,
  statusCode?: number,
  consecutiveFailures?: number,
  errorRate?: number
): Promise<{
  is_degraded: boolean;
  reasons: string[];
  settings_used: DegradedThresholds;
  checked_values: {
    response_time: number;
    status_code?: number;
    consecutive_failures?: number;
    error_rate?: number;
  };
}> {
  try {
    // Use the comprehensive database function
    const { data, error } = await supabase
      .rpc('is_monitor_degraded', {
        p_monitor_id: monitorId,
        p_response_time: responseTime,
        p_status_code: statusCode || null,
        p_consecutive_failures: consecutiveFailures || 0,
        p_error_rate: errorRate || 0
      });

    if (error) {
      console.error('Error checking degraded status:', error);
      // Fallback to simple response time check
      const settings = await getMonitorDegradedSettings(monitorId);
      const isResponseTimeDegraded = responseTime > settings.response_time;

      return {
        is_degraded: isResponseTimeDegraded,
        reasons: isResponseTimeDegraded ? [`Response time ${responseTime}ms exceeds threshold ${settings.response_time}ms`] : [],
        settings_used: settings,
        checked_values: {
          response_time: responseTime,
          status_code: statusCode,
          consecutive_failures: consecutiveFailures,
          error_rate: errorRate
        }
      };
    }

    return data;
  } catch (error) {
    console.error('Error in isMonitorDegraded:', error);
    // Fallback to simple response time check
    const settings = await getMonitorDegradedSettings(monitorId);
    const isResponseTimeDegraded = responseTime > settings.response_time;

    return {
      is_degraded: isResponseTimeDegraded,
      reasons: isResponseTimeDegraded ? [`Response time ${responseTime}ms exceeds threshold ${settings.response_time}ms`] : [],
      settings_used: settings,
      checked_values: {
        response_time: responseTime,
        status_code: statusCode,
        consecutive_failures: consecutiveFailures,
        error_rate: errorRate
      }
    };
  }
}

/**
 * Evaluate comprehensive monitor status including degraded state detection
 * @param monitorId The ID of the monitor
 * @param currentStatus The current up/down status
 * @param responseTime The response time in milliseconds
 * @param statusCode The HTTP status code (optional)
 * @returns Comprehensive status evaluation
 */
export async function evaluateMonitorStatus(
  monitorId: string,
  currentStatus: boolean,
  responseTime: number,
  statusCode?: number
): Promise<{
  status: 'up' | 'down' | 'degraded';
  response_time: number;
  status_code?: number;
  consecutive_failures: number;
  error_rate: number;
  degraded_check?: any;
}> {
  try {
    const { data, error } = await supabase
      .rpc('evaluate_monitor_status', {
        p_monitor_id: monitorId,
        p_current_status: currentStatus,
        p_response_time: responseTime,
        p_status_code: statusCode || null
      });

    if (error) {
      console.error('Error evaluating monitor status:', error);
      return {
        status: currentStatus ? 'up' : 'down',
        response_time: responseTime,
        status_code: statusCode,
        consecutive_failures: 0,
        error_rate: 0
      };
    }

    return data;
  } catch (error) {
    console.error('Error in evaluateMonitorStatus:', error);
    return {
      status: currentStatus ? 'up' : 'down',
      response_time: responseTime,
      status_code: statusCode,
      consecutive_failures: 0,
      error_rate: 0
    };
  }
}
