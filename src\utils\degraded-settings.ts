import { supabase } from '@/integrations/supabase/client';
import { DegradedThresholds } from '@/types/monitor';

/**
 * Get degraded settings for a specific monitor, falling back to global settings
 * @param monitorId The ID of the monitor
 * @returns The combined degraded settings
 */
export async function getMonitorDegradedSettings(monitorId: string): Promise<DegradedThresholds> {
  try {
    // Try to get the settings from the database function
    const { data, error } = await supabase
      .rpc('get_monitor_degraded_settings', { monitor_id: monitorId });
    
    if (error) {
      console.error('Error getting degraded settings:', error);
      // Fallback to default values if there's an error
      return {
        response_time: 1000,
        error_rate: 10,
        status_codes: [429, 503],
        consecutive_failures: 2
      };
    }
    
    return data as DegradedThresholds;
  } catch (error) {
    console.error('Error getting degraded settings:', error);
    // Fallback to default values if there's an error
    return {
      response_time: 1000,
      error_rate: 10,
      status_codes: [429, 503],
      consecutive_failures: 2
    };
  }
}

/**
 * Get global degraded settings
 * @returns The global degraded settings
 */
export async function getGlobalDegradedSettings(): Promise<DegradedThresholds> {
  try {
    const { data, error } = await supabase
      .from('degraded_settings')
      .select('*')
      .limit(1)
      .single();
    
    if (error) {
      console.error('Error getting global degraded settings:', error);
      // Fallback to default values if there's an error
      return {
        response_time: 1000,
        error_rate: 10,
        status_codes: [429, 503],
        consecutive_failures: 2
      };
    }
    
    return {
      response_time: data.response_time,
      error_rate: data.error_rate,
      status_codes: data.status_codes || [],
      consecutive_failures: data.consecutive_failures
    };
  } catch (error) {
    console.error('Error getting global degraded settings:', error);
    // Fallback to default values if there's an error
    return {
      response_time: 1000,
      error_rate: 10,
      status_codes: [429, 503],
      consecutive_failures: 2
    };
  }
}

/**
 * Determine if a monitor is in degraded state based on its check results and settings
 * @param monitorId The ID of the monitor
 * @param responseTime The response time in milliseconds
 * @param statusCode The HTTP status code
 * @param consecutiveFailures The number of consecutive failures
 * @returns True if the monitor is in degraded state, false otherwise
 */
export async function isMonitorDegraded(
  monitorId: string,
  responseTime: number,
  statusCode: number,
  consecutiveFailures: number
): Promise<boolean> {
  const settings = await getMonitorDegradedSettings(monitorId);
  
  // Check if response time exceeds threshold
  if (responseTime > settings.response_time) {
    return true;
  }
  
  // Check if status code is in the degraded list
  if (settings.status_codes?.includes(statusCode)) {
    return true;
  }
  
  // Check if consecutive failures exceed threshold
  if (consecutiveFailures >= settings.consecutive_failures) {
    return true;
  }
  
  return false;
}
