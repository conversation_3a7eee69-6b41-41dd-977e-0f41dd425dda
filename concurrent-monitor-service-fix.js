// Concurrent Monitor Service with improved duplicate check prevention
// This version uses a more robust mechanism to prevent duplicate checks

// Import required modules
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}
const CHECK_INTERVAL = 60 * 1000; // Check every minute
const MAX_CONCURRENT_CHECKS = 10; // Maximum number of concurrent checks
const LOG_DIR = './logs';
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');

// Create log directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Store monitor schedules and locks
const monitorSchedules = new Map();
const monitorLocks = new Map(); // Track which monitors are currently being checked
const monitorQueue = []; // Queue for monitors waiting to be checked
let activeChecks = 0; // Number of active checks

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;

  console.log(logMessage);
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Function to perform a check on a monitor
async function performCheck(monitor) {
  // Skip if this monitor is already being checked
  if (monitorLocks.get(monitor.id)) {
    log(`Monitor ${monitor.name} is already being checked, skipping duplicate check`, 'WARN');
    return;
  }

  // Lock this monitor to prevent duplicate checks
  monitorLocks.set(monitor.id, true);
  activeChecks++;

  try {
    log(`Checking monitor: ${monitor.name} (${monitor.type})`);

    const startTime = Date.now();
    let status = false;
    let responseTime = null;
    let errorMessage = null;

    try {
      // Perform the check based on monitor type
      switch (monitor.type) {
        case 'http':
          // HTTP check
          try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);

            const response = await fetch(monitor.target, {
              method: 'GET',
              signal: controller.signal
            });

            clearTimeout(timeoutId);
            status = response.ok;
            responseTime = Date.now() - startTime;

            if (!status) {
              errorMessage = `HTTP status: ${response.status}`;
            }
          } catch (error) {
            errorMessage = error.message;
          }
          break;

        // Add other monitor types here (ping, port, etc.)
        default:
          errorMessage = `Unsupported monitor type: ${monitor.type}`;
          break;
      }
    } catch (error) {
      errorMessage = error.message;
    }

    // Save the check result
    try {
      const checkResult = {
        monitor_id: monitor.id,
        status,
        response_time: responseTime,
        error_message: errorMessage,
        timestamp: new Date().toISOString()
      };

      const { error } = await supabase
        .from('monitor_history')
        .insert(checkResult);

      if (error) {
        throw error;
      }

      log(`Saved check result for ${monitor.name}: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);

      // Check if status changed and send notification if needed
      await checkStatusChange(monitor, status);
    } catch (error) {
      log(`Failed to save check result: ${error.message}`, 'ERROR');
    }
  } finally {
    // Release the lock on this monitor
    monitorLocks.set(monitor.id, false);
    activeChecks--;

    // Process the next monitor in the queue if any
    processQueue();

    // Schedule the next check for this monitor
    scheduleNextCheck(monitor);
  }
}

// Process the monitor queue
function processQueue() {
  // Check if we can process more monitors
  while (activeChecks < MAX_CONCURRENT_CHECKS && monitorQueue.length > 0) {
    const monitor = monitorQueue.shift();

    // Skip if this monitor is already being checked
    if (monitorLocks.get(monitor.id)) {
      continue;
    }

    // Perform the check
    performCheck(monitor).catch(error => {
      log(`Error checking monitor ${monitor.name}: ${error.message}`, 'ERROR');
    });
  }
}

// Schedule the next check for a monitor
function scheduleNextCheck(monitor) {
  // Calculate when this monitor should next be checked
  getNextCheckTime(monitor).then(nextCheckTime => {
    const now = Date.now();
    const delay = Math.max(0, nextCheckTime - now);

    log(`Scheduling ${monitor.name} to be checked in ${Math.round(delay / 1000)} seconds`);

    // Clear any existing schedule
    if (monitorSchedules.has(monitor.id)) {
      clearTimeout(monitorSchedules.get(monitor.id));
    }

    // Schedule the check
    const timeoutId = setTimeout(() => {
      // Add to queue instead of checking immediately
      if (!monitorLocks.get(monitor.id)) {
        monitorQueue.push(monitor);
        processQueue();
      }
    }, delay);

    monitorSchedules.set(monitor.id, timeoutId);
  }).catch(error => {
    log(`Failed to schedule monitor ${monitor.name}: ${error.message}`, 'ERROR');

    // Retry scheduling after a delay
    const timeoutId = setTimeout(() => {
      scheduleNextCheck(monitor);
    }, 60000); // Retry after 1 minute

    monitorSchedules.set(monitor.id, timeoutId);
  });
}

// Calculate when a monitor should next be checked
async function getNextCheckTime(monitor) {
  try {
    // Get the most recent check for this monitor
    const { data, error } = await supabase
      .from('monitor_history')
      .select('timestamp')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .limit(1);

    if (error) {
      throw error;
    }

    const now = Date.now();

    // If no previous check, check immediately
    if (!data || data.length === 0) {
      return now;
    }

    const lastCheckTime = new Date(data[0].timestamp).getTime();
    const intervalMs = monitor.interval * 60 * 1000;
    const nextCheckTime = lastCheckTime + intervalMs;

    // If next check time is in the past, check immediately
    return Math.max(now, nextCheckTime);
  } catch (error) {
    log(`Failed to get next check time for ${monitor.name}: ${error.message}`, 'ERROR');
    return Date.now(); // Check immediately on error
  }
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(monitor, currentStatus) {
  try {
    // Get the previous check
    const { data: previousChecks, error } = await supabase
      .from('monitor_history')
      .select('status')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .limit(2);

    if (error) {
      throw error;
    }

    // If this is the first check or status changed, send notification
    if (previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
      const statusText = currentStatus ? 'up' : 'down';
      const previousStatus = previousChecks.length < 2 ? null : (previousChecks[1].status ? 'up' : 'down');

      if (previousStatus) {
        log(`Status change detected for ${monitor.name}: ${previousStatus} -> ${statusText}`);
      }

      await createNotificationsForMonitor(monitor, currentStatus);
      return true;
    }

    return false;
  } catch (error) {
    log(`Failed to check status change: ${error.message}`, 'ERROR');
    return false;
  }
}

// Function to create notifications for all companies associated with a monitor
async function createNotificationsForMonitor(monitor, status) {
  try {
    // Make sure we have the monitor's user_id
    if (!monitor.user_id) {
      // Get the monitor details including user_id
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('user_id, name')
        .eq('id', monitor.id)
        .single();

      if (monitorError) {
        log(`Error getting monitor details for ${monitor.id}: ${monitorError.message}`, 'ERROR');
        return false;
      }

      if (!monitorData) {
        log(`Monitor not found: ${monitor.id}`, 'ERROR');
        return false;
      }

      // Update the monitor object with the user_id
      monitor.user_id = monitorData.user_id;
      if (!monitor.name) {
        monitor.name = monitorData.name;
      }
    }

    // Get all companies associated with this monitor
    const { data: companies, error: companiesError } = await supabase
      .from('monitor_companies')
      .select('company_id')
      .eq('monitor_id', monitor.id);

    if (companiesError) {
      log(`Error getting companies for monitor ${monitor.id}: ${companiesError.message}`, 'ERROR');
      return false;
    }

    if (!companies || companies.length === 0) {
      log(`No companies found for monitor ${monitor.id}`, 'WARN');
      return false;
    }

    // Create a notification for each company
    let successCount = 0;
    for (const company of companies) {
      try {
        const statusMessage = status ? 'UP' : 'DOWN';

        // Create notification object with both timestamp and created_at for compatibility
        const notification = {
          monitor_id: monitor.id,
          user_id: monitor.user_id, // Ensure this is set
          company_id: company.company_id,
          message: `Monitor ${monitor.name} is now ${statusMessage}`,
          type: status ? 'up' : 'down',
          read: false,
          created_at: new Date().toISOString()
        };

        const { error: notificationError } = await supabase
          .from('notifications')
          .insert(notification);

        if (notificationError) {
          throw notificationError;
        }

        successCount++;
      } catch (error) {
        log(`Error creating notification for company ${company.company_id}: ${error.message}`, 'ERROR');
      }
    }

    log(`Created notifications for ${monitor.name} across ${successCount} companies`);
    return successCount > 0;
  } catch (error) {
    log(`Error creating notifications for monitor ${monitor.id}: ${error.message}`, 'ERROR');
    return false;
  }
}

// Load all active monitors and schedule them
async function loadAndScheduleMonitors() {
  try {
    log('Loading monitors from database...');

    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    log(`Loaded ${monitors.length} active monitors`);

    // Initialize locks for all monitors
    monitors.forEach(monitor => {
      monitorLocks.set(monitor.id, false);
    });

    // Schedule each monitor
    monitors.forEach(monitor => {
      scheduleNextCheck(monitor);
    });
  } catch (error) {
    log(`Failed to load monitors: ${error.message}`, 'ERROR');

    // Retry after a delay
    setTimeout(loadAndScheduleMonitors, 60000); // Retry after 1 minute
  }
}

// Periodically check for new or updated monitors
async function checkForMonitorUpdates() {
  try {
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    // Get current monitor IDs
    const currentMonitorIds = new Set(monitorSchedules.keys());
    const newMonitorIds = new Set(monitors.map(m => m.id));

    // Find monitors to add and remove
    const monitorsToAdd = monitors.filter(m => !currentMonitorIds.has(m.id));
    const monitorsToRemove = Array.from(currentMonitorIds).filter(id => !newMonitorIds.has(id));

    // Schedule new monitors
    monitorsToAdd.forEach(monitor => {
      log(`New monitor detected: ${monitor.name}`);
      monitorLocks.set(monitor.id, false);
      scheduleNextCheck(monitor);
    });

    // Remove old monitors
    monitorsToRemove.forEach(id => {
      log(`Monitor removed: ${id}`);
      if (monitorSchedules.has(id)) {
        clearTimeout(monitorSchedules.get(id));
        monitorSchedules.delete(id);
      }
      monitorLocks.delete(id);
    });
  } catch (error) {
    log(`Failed to check for monitor updates: ${error.message}`, 'ERROR');
  }

  // Schedule next check
  setTimeout(checkForMonitorUpdates, CHECK_INTERVAL);
}

// Start the service
async function startService() {
  log(`Maximum concurrent checks: ${MAX_CONCURRENT_CHECKS}`);

  try {
    // Test Supabase connection
    const { data, error } = await supabase
      .from('monitors')
      .select('id, name');

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${data.length} monitors.`);

    // Load and schedule monitors
    await loadAndScheduleMonitors();

    // Start checking for monitor updates
    checkForMonitorUpdates();

    log('Concurrent Monitor Service started successfully');
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startService, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  log('Shutting down Monitor Service...');
  log('Monitor Service stopped');
  process.exit(0);
});

// Start the service
startService();
