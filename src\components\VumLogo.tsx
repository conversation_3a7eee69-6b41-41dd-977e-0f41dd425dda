import React from 'react';
import { Link } from 'react-router-dom';

interface VumLogoProps {
  className?: string;
  linkTo?: string;
}

export function VumLogo({ className = '', linkTo }: VumLogoProps) {
  const logoContent = (
    <div className={`flex items-center space-x-2 ${className}`}>
      <img
        src="/static/Vurbis Logo.png"
        alt="Vurbis Logo"
        className="h-8 w-auto"
      />
      <div className="flex flex-col">
        <span className="text-xl font-bold">VUM</span>
        <span className="text-xs text-slate-500 dark:text-slate-400">Vurbis Uptime Monitor</span>
      </div>
    </div>
  );

  if (linkTo) {
    return <Link to={linkTo}>{logoContent}</Link>;
  }

  return logoContent;
}

export default VumLogo;
