// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface RequestBody {
  monitor_id: string
  updates: {
    name?: string
    target?: string
    type?: string
    interval?: number
    timeout?: number
    active?: boolean
  }
  company_ids: string[]
  user_id: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the request body
    const requestData: RequestBody = await req.json()
    const { monitor_id, updates, company_ids, user_id } = requestData

    console.log('Received request to update monitor:', {
      monitor_id,
      updates,
      company_ids,
      user_id
    })

    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
    const supabaseClient = createClient(supabaseUrl, supabaseKey)

    // Verify that the monitor exists and belongs to the user
    const { data: monitorData, error: monitorCheckError } = await supabaseClient
      .from('monitors')
      .select('id')
      .eq('id', monitor_id)
      .eq('user_id', user_id)
      .single()

    if (monitorCheckError) {
      console.error('Error checking monitor ownership:', monitorCheckError)
      return new Response(
        JSON.stringify({
          error: 'Monitor not found or you do not have permission to update it'
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403
        }
      )
    }

    // Update the monitor
    const { data: updatedMonitor, error: updateError } = await supabaseClient
      .from('monitors')
      .update(updates)
      .eq('id', monitor_id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating monitor:', updateError)
      return new Response(
        JSON.stringify({ error: `Error updating monitor: ${updateError.message}` }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    // Delete existing company associations
    const { error: deleteError } = await supabaseClient
      .from('monitor_companies')
      .delete()
      .eq('monitor_id', monitor_id)

    if (deleteError) {
      console.error('Error deleting existing company associations:', deleteError)
      return new Response(
        JSON.stringify({ error: `Error updating company associations: ${deleteError.message}` }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    // Add new company associations if provided
    if (company_ids && company_ids.length > 0) {
      const monitorCompanies = company_ids.map(company_id => ({
        monitor_id,
        company_id
      }))

      const { error: insertError } = await supabaseClient
        .from('monitor_companies')
        .insert(monitorCompanies)

      if (insertError) {
        console.error('Error inserting company associations:', insertError)
        return new Response(
          JSON.stringify({ error: `Error adding company associations: ${insertError.message}` }),
          {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: 500
          }
        )
      }
    }

    // Fetch the updated monitor with its company associations
    const { data: monitorWithCompanies, error: fetchError } = await supabaseClient
      .from('monitors')
      .select(`
        *,
        companies:monitor_companies(
          id,
          company_id,
          created_at
        )
      `)
      .eq('id', monitor_id)
      .single()

    if (fetchError) {
      console.error('Error fetching updated monitor data:', fetchError)
      return new Response(
        JSON.stringify({ error: `Error retrieving updated monitor: ${fetchError.message}` }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500
        }
      )
    }

    // Return the updated monitor
    return new Response(
      JSON.stringify({
        success: true,
        monitor: monitorWithCompanies
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      }
    )
  } catch (error) {
    console.error('Unexpected error:', error)
    return new Response(
      JSON.stringify({ error: `Unexpected error: ${error.message}` }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500
      }
    )
  }
})
