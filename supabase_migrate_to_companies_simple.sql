-- This script migrates existing monitors to the new company-based system
-- Run this in the Supabase SQL Editor after running the company_tables_simple.sql script

-- Create a default company for the current user
INSERT INTO public.companies (name, description)
VALUES ('My Company', 'Default company for your monitors');

-- Get the ID of the company we just created
DO $$
DECLARE
    company_id UUID;
    current_user_id UUID;
BEGIN
    -- Get the company ID
    SELECT id INTO company_id FROM public.companies ORDER BY created_at DESC LIMIT 1;
    
    -- Get the current user ID
    SELECT auth.uid() INTO current_user_id;
    
    -- Add the current user as an admin to the company
    INSERT INTO public.company_members (company_id, user_id, role)
    VALUES (company_id, current_user_id, 'admin');
    
    -- Update existing monitors to be associated with the company
    UPDATE public.monitors
    SET company_id = company_id
    WHERE user_id = current_user_id AND company_id IS NULL;
    
    -- Output the results
    RAISE NOTICE 'Created company with ID: %', company_id;
    RAISE NOTICE 'Added user % as admin', current_user_id;
END $$;
