-- This script creates a many-to-many relationship between monitors and companies
-- Run this in the Supabase SQL Editor

-- Create a new junction table for monitor-company relationships
CREATE TABLE IF NOT EXISTS public.monitor_companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    monitor_id UUID NOT NULL REFERENCES public.monitors(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(monitor_id, company_id)
);

-- Make sure the uuid-ossp extension is available for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_monitor_companies_monitor_id ON public.monitor_companies(monitor_id);
CREATE INDEX IF NOT EXISTS idx_monitor_companies_company_id ON public.monitor_companies(company_id);

-- Enable Row Level Security
ALTER TABLE public.monitor_companies ENABLE ROW LEVEL SECURITY;

-- Drop existing policies for monitor_companies table
DROP POLICY IF EXISTS "Users can view monitor-company relationships for their companies" ON public.monitor_companies;
DROP POLICY IF EXISTS "Company admins can manage monitor-company relationships" ON public.monitor_companies;
DROP POLICY IF EXISTS "Superadmins can manage all monitor-company relationships" ON public.monitor_companies;

-- Create policies for monitor_companies table
CREATE POLICY "Users can view monitor-company relationships for their companies"
ON public.monitor_companies
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitor_companies.company_id
        AND company_members.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can manage monitor-company relationships"
ON public.monitor_companies
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitor_companies.company_id
        AND company_members.user_id = auth.uid()
        AND (company_members.role_type = 'admin' OR company_members.role_type = 'superadmin')
    )
);

CREATE POLICY "Superadmins can manage all monitor-company relationships"
ON public.monitor_companies
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.user_id = auth.uid()
        AND company_members.role_type = 'superadmin'
    )
);

-- Migrate existing data: For each monitor with a company_id, create an entry in the junction table
INSERT INTO public.monitor_companies (monitor_id, company_id)
SELECT id, company_id
FROM public.monitors
WHERE company_id IS NOT NULL
ON CONFLICT (monitor_id, company_id) DO NOTHING;

-- Update the RLS policies for monitors table
DROP POLICY IF EXISTS "Users can view monitors in their companies" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can create monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can update monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can delete monitors" ON public.monitors;

-- Policy for viewing monitors (all users can view non-deleted monitors in their companies)
CREATE POLICY "Users can view monitors in their companies"
ON public.monitors
FOR SELECT
USING (
    (
        EXISTS (
            SELECT 1 FROM public.monitor_companies
            JOIN public.company_members ON monitor_companies.company_id = company_members.company_id
            WHERE monitor_companies.monitor_id = monitors.id
            AND company_members.user_id = auth.uid()
        )
        OR
        -- Allow users to view their own monitors that don't have any company relationships yet
        (monitors.user_id = auth.uid() AND NOT EXISTS (
            SELECT 1 FROM public.monitor_companies
            WHERE monitor_companies.monitor_id = monitors.id
        ))
    )
    AND
    -- Only show non-deleted monitors to regular users
    (monitors.deleted = FALSE OR
     -- Admins and superadmins can see deleted monitors
     EXISTS (
        SELECT 1 FROM public.company_members
        JOIN public.monitor_companies ON company_members.company_id = monitor_companies.company_id
        WHERE monitor_companies.monitor_id = monitors.id
        AND company_members.user_id = auth.uid()
        AND (company_members.role_type = 'admin' OR company_members.role_type = 'superadmin')
     ))
);

-- Policy for creating monitors (only admins and superadmins)
CREATE POLICY "Company admins can create monitors"
ON public.monitors
FOR INSERT
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = ANY(SELECT company_id FROM public.monitor_companies WHERE monitor_id = monitors.id)
        AND company_members.user_id = auth.uid()
        AND (company_members.role_type = 'admin' OR company_members.role_type = 'superadmin')
    )
    OR
    -- Superadmins can create monitors for any company
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.user_id = auth.uid()
        AND company_members.role_type = 'superadmin'
    )
);

-- Policy for updating monitors (only admins and superadmins)
CREATE POLICY "Company admins can update monitors"
ON public.monitors
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        JOIN public.monitor_companies ON company_members.company_id = monitor_companies.company_id
        WHERE monitor_companies.monitor_id = monitors.id
        AND company_members.user_id = auth.uid()
        AND (company_members.role_type = 'admin' OR company_members.role_type = 'superadmin')
    )
    OR
    -- Superadmins can update any monitor
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.user_id = auth.uid()
        AND company_members.role_type = 'superadmin'
    )
);

-- Policy for hard deleting monitors (only superadmins)
CREATE POLICY "Superadmins can hard delete monitors"
ON public.monitors
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.user_id = auth.uid()
        AND company_members.role_type = 'superadmin'
    )
);

-- Grant necessary permissions
GRANT ALL ON public.monitor_companies TO authenticated;

-- Verify the migration
SELECT
    m.id AS monitor_id,
    m.name AS monitor_name,
    COUNT(mc.company_id) AS company_count,
    array_agg(c.name) AS company_names
FROM
    public.monitors m
LEFT JOIN
    public.monitor_companies mc ON m.id = mc.monitor_id
LEFT JOIN
    public.companies c ON mc.company_id = c.id
GROUP BY
    m.id, m.name
ORDER BY
    m.name;
