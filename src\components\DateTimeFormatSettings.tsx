import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Button } from '@/components/ui/button';
import { Save, RefreshCw } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { format as formatDateFns } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';
import {
  DateFormatType,
  DateDisplayFormat,
  getUserTimeFormat,
  getUserDateDisplayFormat,
  formatDate,
  formatTime,
  formatDateTime
} from '@/utils/dateFormat';

const DateTimeFormatSettings: React.FC = () => {
  const [timeFormat, setTimeFormat] = useState<DateFormatType>('24h');
  const [dateFormat, setDateFormat] = useState<DateDisplayFormat>('iso');
  const [currentDate] = useState<Date>(new Date());
  const [isLoading, setIsLoading] = useState(false);

  // Load saved preferences on component mount
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        setIsLoading(true);
        // Get preferences from database using RPC function
        const { data, error } = await supabase.rpc('get_or_create_user_preferences');

        if (error) {
          console.error('Error loading preferences:', error);
          // Fall back to localStorage if database call fails
          const savedTimeFormat = getUserTimeFormat();
          const savedDateFormat = getUserDateDisplayFormat();
          setTimeFormat(savedTimeFormat);
          setDateFormat(savedDateFormat);
          return;
        }

        if (data) {
          // Set preferences from database
          setTimeFormat(data.time_format as DateFormatType);
          setDateFormat(data.date_format as DateDisplayFormat);

          // Also update localStorage for backward compatibility
          localStorage.setItem('timeFormat', data.time_format);
          localStorage.setItem('dateDisplayFormat', data.date_format);
        }
      } catch (err) {
        console.error('Error loading preferences:', err);
        // Fall back to localStorage
        const savedTimeFormat = getUserTimeFormat();
        const savedDateFormat = getUserDateDisplayFormat();
        setTimeFormat(savedTimeFormat);
        setDateFormat(savedDateFormat);
      } finally {
        setIsLoading(false);
      }
    };

    loadPreferences();
  }, []);

  // Save preferences
  const savePreferences = async () => {
    try {
      setIsLoading(true);

      // Save to database using RPC function
      const { data, error } = await supabase.rpc('update_user_preferences', {
        p_date_format: dateFormat,
        p_time_format: timeFormat
      });

      if (error) {
        throw error;
      }

      // Also update localStorage for backward compatibility
      localStorage.setItem('timeFormat', timeFormat);
      localStorage.setItem('dateDisplayFormat', dateFormat);

      // Reload the page to apply changes
      window.location.reload();
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast({
        title: 'Error',
        description: 'Failed to save preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Date & Time Format</CardTitle>
        <CardDescription>
          Choose how dates and times are displayed throughout the application.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <Label className="text-base">Date Format</Label>
            <RadioGroup
              value={dateFormat}
              onValueChange={(value) => setDateFormat(value as DateDisplayFormat)}
              className="mt-2 space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="iso" id="iso" />
                <Label htmlFor="iso" className="font-normal">
                  ISO (YYYY-MM-DD) - Example: {formatDateFns(currentDate, 'yyyy-MM-dd')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="us" id="us" />
                <Label htmlFor="us" className="font-normal">
                  US (MM/DD/YYYY) - Example: {formatDateFns(currentDate, 'MM/dd/yyyy')}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="eu" id="eu" />
                <Label htmlFor="eu" className="font-normal">
                  European (DD/MM/YYYY) - Example: {formatDateFns(currentDate, 'dd/MM/yyyy')}
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="mt-6">
            <Label className="text-base">Time Format</Label>
            <RadioGroup
              value={timeFormat}
              onValueChange={(value) => setTimeFormat(value as DateFormatType)}
              className="mt-2 space-y-2"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="24h" id="24h" />
                <Label htmlFor="24h" className="font-normal">
                  24-hour - Example: 14:30
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="12h" id="12h" />
                <Label htmlFor="12h" className="font-normal">
                  12-hour - Example: 2:30 PM
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="mt-6 p-4 border rounded-md bg-slate-50 dark:bg-slate-800">
            <h4 className="font-medium mb-2">Preview of Current Selection</h4>
            <p className="text-sm text-slate-500 dark:text-slate-400 mb-3">This shows how dates and times will appear with your selected formats.</p>
            <div className="space-y-2">
              <div>
                <span className="text-sm text-slate-500 dark:text-slate-400">Date:</span>
                <span className="ml-2 font-medium">
                  {dateFormat === 'iso' && formatDateFns(currentDate, 'yyyy-MM-dd')}
                  {dateFormat === 'us' && formatDateFns(currentDate, 'MM/dd/yyyy')}
                  {dateFormat === 'eu' && formatDateFns(currentDate, 'dd/MM/yyyy')}
                </span>
              </div>
              <div>
                <span className="text-sm text-slate-500 dark:text-slate-400">Time:</span>
                <span className="ml-2 font-medium">
                  {timeFormat === '24h' && formatDateFns(currentDate, 'HH:mm')}
                  {timeFormat === '12h' && formatDateFns(currentDate, 'h:mm a')}
                </span>
              </div>
              <div>
                <span className="text-sm text-slate-500 dark:text-slate-400">Date & Time:</span>
                <span className="ml-2 font-medium">
                  {dateFormat === 'iso' && timeFormat === '24h' && formatDateFns(currentDate, 'yyyy-MM-dd HH:mm')}
                  {dateFormat === 'iso' && timeFormat === '12h' && formatDateFns(currentDate, 'yyyy-MM-dd h:mm a')}
                  {dateFormat === 'us' && timeFormat === '24h' && formatDateFns(currentDate, 'MM/dd/yyyy HH:mm')}
                  {dateFormat === 'us' && timeFormat === '12h' && formatDateFns(currentDate, 'MM/dd/yyyy h:mm a')}
                  {dateFormat === 'eu' && timeFormat === '24h' && formatDateFns(currentDate, 'dd/MM/yyyy HH:mm')}
                  {dateFormat === 'eu' && timeFormat === '12h' && formatDateFns(currentDate, 'dd/MM/yyyy h:mm a')}
                </span>
              </div>
            </div>
          </div>

          <Button onClick={savePreferences} className="mt-6" disabled={isLoading}>
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Preferences
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default DateTimeFormatSettings;
