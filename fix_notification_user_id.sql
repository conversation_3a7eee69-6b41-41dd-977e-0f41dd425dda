-- This script fixes the issue with null user_id in notifications
-- Run this in the Supabase SQL Editor

-- First, check if there are any notifications with null user_id
SELECT COUNT(*) AS null_user_id_count
FROM public.notifications
WHERE user_id IS NULL;

-- Create a function to get the user_id for a monitor
CREATE OR REPLACE FUNCTION get_monitor_owner_id(monitor_id UUID)
RETURNS UUID AS $$
DECLARE
    owner_id UUID;
BEGIN
    -- Get the user_id from the monitors table
    SELECT user_id INTO owner_id
    FROM public.monitors
    WHERE id = monitor_id;
    
    RETURN owner_id;
END;
$$ LANGUAGE plpgsql;

-- Create a function to fix notifications with null user_id
CREATE OR REPLACE FUNCTION fix_notifications_with_null_user_id()
RETURNS INTEGER AS $$
DECLARE
    fixed_count INTEGER := 0;
    notification_rec RECORD;
    monitor_owner_id UUID;
BEGIN
    -- Loop through all notifications with null user_id
    FOR notification_rec IN 
        SELECT id, monitor_id
        FROM public.notifications
        WHERE user_id IS NULL
    LOOP
        -- Get the monitor owner
        SELECT get_monitor_owner_id(notification_rec.monitor_id) INTO monitor_owner_id;
        
        -- If we found an owner, update the notification
        IF monitor_owner_id IS NOT NULL THEN
            UPDATE public.notifications
            SET user_id = monitor_owner_id
            WHERE id = notification_rec.id;
            
            fixed_count := fixed_count + 1;
        END IF;
    END LOOP;
    
    RETURN fixed_count;
END;
$$ LANGUAGE plpgsql;

-- Run the function to fix notifications
SELECT fix_notifications_with_null_user_id() AS fixed_notifications_count;

-- Drop the temporary function
DROP FUNCTION fix_notifications_with_null_user_id();

-- Create a trigger to automatically set user_id when a notification is created
CREATE OR REPLACE FUNCTION set_notification_user_id()
RETURNS TRIGGER AS $$
BEGIN
    -- If user_id is NULL, get it from the monitor
    IF NEW.user_id IS NULL THEN
        NEW.user_id := get_monitor_owner_id(NEW.monitor_id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS ensure_notification_user_id ON public.notifications;
CREATE TRIGGER ensure_notification_user_id
BEFORE INSERT ON public.notifications
FOR EACH ROW
EXECUTE FUNCTION set_notification_user_id();

-- Check if there are still any notifications with null user_id
SELECT COUNT(*) AS remaining_null_user_id_count
FROM public.notifications
WHERE user_id IS NULL;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
