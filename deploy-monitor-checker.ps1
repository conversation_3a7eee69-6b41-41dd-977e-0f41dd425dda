# PowerShell script to deploy the monitor-checker Edge Function to Supabase

Write-Host "Deploying monitor-checker Edge Function to Supabase..." -ForegroundColor Cyan

# Change to the supabase/functions directory
Set-Location -Path "supabase/functions"

# Deploy the function using Supabase CLI
Write-Host "Running: npx supabase functions deploy monitor-checker" -ForegroundColor Yellow
npx supabase functions deploy monitor-checker

# Return to the original directory
Set-Location -Path "../.."

Write-Host "Deployment complete!" -ForegroundColor Green
Write-Host "You can now use the monitor-checker Edge Function for immediate checks when creating monitors." -ForegroundColor Green
