-- Migration script to support degraded status in the monitoring system

-- 1. Create system_settings table for global configuration
CREATE TABLE IF NOT EXISTS public.system_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    key TEXT NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add RLS policies for system_settings
ALTER TABLE public.system_settings ENABLE ROW LEVEL SECURITY;

-- Only superadmins can read system settings
CREATE POLICY "Superadmins can read system settings"
ON public.system_settings
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- Only superadmins can modify system settings
CREATE POLICY "Superadmins can modify system settings"
ON public.system_settings
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- 2. Add default system settings for degraded status
INSERT INTO public.system_settings (key, value, description)
VALUES (
    'degraded_thresholds',
    '{
        "response_time": 1000,
        "error_rate": 10,
        "status_codes": [429, 503],
        "consecutive_failures": 2
    }'::jsonb,
    'Thresholds for determining when a service is considered degraded'
)
ON CONFLICT (key) DO NOTHING;

-- 3. Add degraded_threshold to monitors table
ALTER TABLE public.monitors
ADD COLUMN IF NOT EXISTS degraded_threshold INTEGER;

-- 4. Create a function to convert boolean status to text in monitor_history
CREATE OR REPLACE FUNCTION convert_monitor_history_status()
RETURNS void AS $$
BEGIN
    -- First, add a temporary column
    ALTER TABLE public.monitor_history ADD COLUMN IF NOT EXISTS status_text TEXT;
    
    -- Convert existing boolean values to text
    UPDATE public.monitor_history
    SET status_text = CASE WHEN status = TRUE THEN 'up' ELSE 'down' END;
    
    -- Drop the old status column
    ALTER TABLE public.monitor_history DROP COLUMN status;
    
    -- Rename the new column to status
    ALTER TABLE public.monitor_history RENAME COLUMN status_text TO status;
    
    -- Add constraint to ensure valid status values
    ALTER TABLE public.monitor_history
    ADD CONSTRAINT valid_status CHECK (status IN ('up', 'degraded', 'down'));
END;
$$ LANGUAGE plpgsql;

-- Execute the function to convert the data
SELECT convert_monitor_history_status();

-- Drop the function after use
DROP FUNCTION convert_monitor_history_status();

-- 5. Update the monitor checker function to support degraded status
CREATE OR REPLACE FUNCTION check_monitor(
    monitor_id UUID,
    response_time INTEGER,
    http_status INTEGER DEFAULT NULL,
    error_message TEXT DEFAULT NULL
)
RETURNS TEXT AS $$
DECLARE
    monitor_record RECORD;
    system_degraded_settings JSONB;
    degraded_response_time INTEGER;
    status TEXT := 'down';
BEGIN
    -- Get the monitor record
    SELECT * INTO monitor_record FROM monitors WHERE id = monitor_id;
    
    -- Get system degraded settings
    SELECT value INTO system_degraded_settings 
    FROM system_settings 
    WHERE key = 'degraded_thresholds';
    
    -- Determine which degraded threshold to use (monitor-specific or system default)
    IF monitor_record.degraded_threshold IS NOT NULL THEN
        degraded_response_time := monitor_record.degraded_threshold;
    ELSE
        degraded_response_time := (system_degraded_settings->>'response_time')::INTEGER;
    END IF;
    
    -- Determine status based on HTTP status and response time
    IF http_status IS NOT NULL AND http_status >= 200 AND http_status < 300 THEN
        -- Success status codes
        IF response_time > degraded_response_time THEN
            status := 'degraded';
        ELSE
            status := 'up';
        END IF;
    ELSIF http_status IS NOT NULL AND (
        http_status = ANY(ARRAY(SELECT jsonb_array_elements_text(system_degraded_settings->'status_codes')::INTEGER))
    ) THEN
        -- Status codes that indicate degraded service
        status := 'degraded';
    ELSE
        -- All other cases are considered down
        status := 'down';
    END IF;
    
    -- Insert the check result
    INSERT INTO monitor_history (monitor_id, status, response_time, error_message)
    VALUES (monitor_id, status, response_time, error_message);
    
    RETURN status;
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION check_monitor TO authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO authenticated;
