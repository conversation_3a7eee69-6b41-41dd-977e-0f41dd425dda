-- Add history_retention_days column to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS history_retention_days INTEGER DEFAULT 7;

-- Add global_history_retention_days to app_settings table
-- First check if the app_settings table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'app_settings') THEN
        CREATE TABLE public.app_settings (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            key TEXT NOT NULL UNIQUE,
            value JSONB NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
        );
        
        -- Add RLS policies
        ALTER TABLE public.app_settings ENABLE ROW LEVEL SECURITY;
        
        -- Only superadmins can modify app settings
        CREATE POLICY "Superadmins can do anything with app_settings" ON public.app_settings
            USING (
                EXISTS (
                    SELECT 1 FROM public.user_roles
                    WHERE user_id = auth.uid() AND role = 'superadmin'
                )
            )
            WITH CHECK (
                EXISTS (
                    SELECT 1 FROM public.user_roles
                    WHERE user_id = auth.uid() AND role = 'superadmin'
                )
            );
    END IF;
END
$$;

-- Insert or update the global history retention setting
INSERT INTO public.app_settings (key, value)
VALUES ('global_history_retention', '{"days": 7}')
ON CONFLICT (key) 
DO UPDATE SET value = '{"days": 7}' WHERE app_settings.key = 'global_history_retention' AND app_settings.value->>'days' IS NULL;

-- Create a function to get history retention days for a company
CREATE OR REPLACE FUNCTION get_company_history_retention(company_id UUID)
RETURNS INTEGER AS $$
DECLARE
    company_days INTEGER;
    global_days INTEGER;
BEGIN
    -- Get company-specific retention days
    SELECT history_retention_days INTO company_days
    FROM public.companies
    WHERE id = company_id;
    
    -- If company has a specific value, return it
    IF company_days IS NOT NULL THEN
        RETURN company_days;
    END IF;
    
    -- Otherwise, get global default
    SELECT (value->>'days')::INTEGER INTO global_days
    FROM public.app_settings
    WHERE key = 'global_history_retention';
    
    -- Return global default or 7 days if not set
    RETURN COALESCE(global_days, 7);
END;
$$ LANGUAGE plpgsql;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
