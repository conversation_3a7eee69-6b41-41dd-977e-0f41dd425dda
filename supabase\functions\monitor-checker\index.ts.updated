// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper function to run tasks with concurrency limit
async function runWithConcurrency(items, taskFn, concurrency = 5) {
  const results = [];
  const chunks = [];

  // Split items into chunks based on concurrency
  for(let i = 0; i < items.length; i += concurrency){
    chunks.push(items.slice(i, i + concurrency));
  }

  // Process each chunk sequentially, but items within a chunk in parallel
  for (const chunk of chunks){
    // Use Promise.allSettled to handle errors gracefully
    const chunkPromises = await Promise.allSettled(chunk.map(async (item)=>{
      try {
        return await taskFn(item);
      } catch (error) {
        console.error(`Error processing item:`, error);
        // Return a default error result if possible
        if (typeof item === 'object' && item !== null && 'id' in item) {
          return {
            monitor_id: item.id,
            name: item.name || 'Unknown',
            status: false,
            response_time: 0,
            error_message: `Processing error: ${error.message}`
          };
        }
        throw error // Re-throw if we can't create a default result
        ;
      }
    }));

    // Filter out rejected promises and add fulfilled ones to results
    for (const result of chunkPromises){
      if (result.status === 'fulfilled') {
        results.push(result.value);
      }
    }
  }

  return results;
}

// Perform a check on a monitor
async function performCheck(monitor) {
  let status = false;
  let responseTime = null;
  let errorMessage = null;
  let portDetails = [];
  const startTime = Date.now();

  try {
    switch(monitor.type){
      case 'http':
        // HTTP check
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), monitor.timeout * 1000);
        try {
          const response = await fetch(monitor.target, {
            method: 'GET',
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          status = response.ok;
          responseTime = Date.now() - startTime;
          if (!status) {
            errorMessage = `HTTP status: ${response.status}`;
          }
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
        break;
      case 'ping':
        // For ping, we'll use a simple HTTP check as a proxy since Deno edge functions
        // don't have direct ping capabilities
        try {
          const pingResponse = await fetch(monitor.target, {
            method: 'HEAD',
            headers: {
              'Cache-Control': 'no-cache'
            }
          });
          status = pingResponse.ok;
          responseTime = Date.now() - startTime;
          if (!status) {
            errorMessage = `Host unreachable, status: ${pingResponse.status}`;
          }
        } catch (error) {
          throw new Error(`Ping failed: ${error.message}`);
        }
        break;
      case 'port':
        // For port checks, we'll use a more reliable method that works for all ports
        try {
          // Parse the target (format: host|port1,port2,port3)
          const [host, portsStr] = monitor.target.split('|');
          const ports = portsStr.split(',').map((p)=>parseInt(p.trim()));
          // Check if at least one port is open
          let portResults = [];
          let anyPortOpen = false;
          let totalResponseTime = 0;
          // Check each port
          for (const port of ports){
            const portStartTime = Date.now();
            let portStatus = false;
            let portError = null;
            try {
              // For HTTP/HTTPS ports, we'll use fetch which is more reliable for web servers
              if (port === 80 || port === 443 || port === 8080 || port === 8443) {
                const protocol = port === 443 || port === 8443 ? 'https' : 'http';
                const url = `${protocol}://${host}:${port}`;
                try {
                  const portResponse = await fetch(url, {
                    method: 'HEAD',
                    headers: {
                      'Cache-Control': 'no-cache'
                    },
                    // Set a shorter timeout for the fetch request
                    signal: AbortSignal.timeout(5000)
                  });
                  // Consider the port open if we get any response (even an error response)
                  portStatus = true;
                } catch (fetchError) {
                  // For HTTP/HTTPS ports, some errors actually indicate the port is open
                  // For example, if we get a CORS error, the server is responding
                  if (fetchError.message && (fetchError.message.includes('CORS') || fetchError.message.includes('SSL') || fetchError.message.includes('certificate') || fetchError.message.includes('redirect'))) {
                    portStatus = true;
                  } else {
                    portError = fetchError.message;
                  }
                }
              } else {
                // For non-HTTP ports, we'll use a simple TCP connection check
                // Since Deno Edge Functions don't support direct TCP connections,
                // we'll use a DNS lookup as a proxy for port availability
                try {
                  // Perform a DNS lookup to verify the host exists
                  const dnsResponse = await fetch(`https://dns.google/resolve?name=${host}`);
                  const dnsData = await dnsResponse.json();
                  if (dnsData.Answer && dnsData.Answer.length > 0) {
                    // If DNS resolves, we'll assume the port is open
                    // This is not accurate but the best we can do in an Edge Function
                    portStatus = true;
                  } else {
                    portError = 'Host not found in DNS';
                  }
                } catch (dnsError) {
                  portError = dnsError.message;
                }
              }
            } catch (error) {
              portError = error.message;
            }
            const portResponseTime = Date.now() - portStartTime;
            totalResponseTime += portResponseTime;
            if (portStatus) {
              anyPortOpen = true;
            }
            portResults.push({
              port,
              status: portStatus,
              responseTime: portResponseTime,
              error: portError
            });
          }
          // Overall status is true if any port is open
          status = anyPortOpen;
          responseTime = ports.length > 0 ? totalResponseTime / ports.length : 0;
          // Store port details for the response
          portDetails = portResults;
          if (!status) {
            const failedPorts = portResults.filter((r)=>!r.status).map((r)=>r.port).join(', ');
            errorMessage = `All ports failed (${failedPorts})`;
          }
        } catch (error) {
          throw new Error(`Port check failed: ${error.message}`);
        }
        break;
      default:
        throw new Error(`Unsupported monitor type: ${monitor.type}`);
    }
  } catch (error) {
    status = false;
    errorMessage = `Error: ${error.message}`;
    console.error(`Check failed for monitor ${monitor.name}:`, error);
  }

  return {
    monitor_id: monitor.id,
    status,
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString(),
    _portDetails: portDetails.length > 0 ? portDetails : undefined
  };
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(supabase, monitor, currentStatus) {
  // Get the previous check
  const { data: previousChecks } = await supabase.from('monitor_history').select('status').eq('monitor_id', monitor.id).order('timestamp', {
    ascending: false
  }).limit(2);
  // If this is the first check or status changed, return true
  if (!previousChecks || previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
    return true;
  }
  return false;
}

// Send a notification
async function sendNotification(supabase, monitor, status) {
  // In a real implementation, you'd send an email, SMS, webhook, etc.
  console.log(`Notification for monitor ${monitor.name}: Status is now ${status ? 'UP' : 'DOWN'}`);
  // For now, we'll just log the notification in a notifications table
  await supabase.from('notifications').insert({
    monitor_id: monitor.id,
    user_id: monitor.user_id,
    company_id: monitor.company_id,
    message: `Monitor ${monitor.name} is now ${status ? 'UP' : 'DOWN'}`,
    type: status ? 'up' : 'down',
    read: false,
    created_at: new Date().toISOString()
  });
}

serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }

  try {
    // Parse request body if present
    let requestBody = {};
    if (req.method === 'POST') {
      try {
        requestBody = await req.json();
      } catch (e) {
        // If parsing fails, assume empty body
        console.log('Failed to parse request body, assuming empty');
      }
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Check if this is a port check request with a temporary monitor
    if (requestBody?.tempMonitor && requestBody.tempMonitor.type === 'port') {
      console.log('Port check request received for temporary monitor');
      try {
        const tempMonitor = requestBody.tempMonitor;
        const checkResult = await performCheck(tempMonitor);

        // For port checks, extract the detailed port results
        let portResults = [];
        if (tempMonitor.type === 'port' && tempMonitor.target.includes('|')) {
          const [host, portsStr] = tempMonitor.target.split('|');
          const ports = portsStr.split(',').map((p)=>parseInt(p.trim()));

          // Create a result for each port
          portResults = ports.map((port)=>{
            // Find this port in the detailed results if available
            const portDetail = checkResult._portDetails?.find((p)=>p.port === port);
            return {
              port,
              status: portDetail ? portDetail.status : false,
              error: portDetail?.error
            };
          });
        }

        return new Response(JSON.stringify({
          success: true,
          status: checkResult.status,
          portResults
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        console.error('Error performing port check:', error);
        return new Response(JSON.stringify({
          success: false,
          error: error.message
        }), {
          status: 500,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
    }

    // Check if a specific monitor ID was provided
    const specificMonitorId = requestBody?.monitorId;
    console.log(`Request received. Specific monitor ID: ${specificMonitorId || 'none'}`);

    // Get monitors to check
    let monitorsQuery = supabase.from('monitors').select('*').eq('active', true);

    // If a specific monitor ID was provided, only check that one
    if (specificMonitorId) {
      monitorsQuery = monitorsQuery.eq('id', specificMonitorId);
    }

    const { data: monitors, error: monitorsError } = await monitorsQuery;

    if (monitorsError) {
      throw new Error(`Failed to fetch monitors: ${monitorsError.message}`);
    }

    // If no monitors found, handle appropriately
    if (!monitors || monitors.length === 0) {
      // If a specific monitor was requested but not found, return an error
      if (specificMonitorId) {
        return new Response(JSON.stringify({
          success: false,
          error: `Monitor with ID ${specificMonitorId} not found or not active`
        }), {
          status: 404,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }

      // Otherwise, just return success with no checks run
      return new Response(JSON.stringify({
        success: true,
        checksRun: 0,
        results: []
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }

    const now = Date.now();
    const checksToRun = [];

    // Determine which monitors need to be checked
    for (const monitor of monitors){
      // If a specific monitor ID was provided, we'll check it regardless of the interval
      if (specificMonitorId) {
        checksToRun.push(monitor);
        continue;
      }

      const { data: lastCheck } = await supabase.from('monitor_history').select('timestamp').eq('monitor_id', monitor.id).order('timestamp', {
        ascending: false
      }).limit(1);

      // If no previous check exists, check it now
      if (!lastCheck || lastCheck.length === 0) {
        checksToRun.push(monitor);
        continue;
      }

      const lastCheckTime = new Date(lastCheck[0].timestamp).getTime();
      const intervalMs = monitor.interval * 60 * 1000;
      const timeSinceLastCheck = now - lastCheckTime;

      console.log(`Monitor ${monitor.name}: interval=${monitor.interval}min, last check=${Math.floor(timeSinceLastCheck / 60000)}min ago`);

      // Check if it's time to run this monitor again
      // We add a small buffer (10 seconds) to account for processing time
      if (timeSinceLastCheck >= intervalMs - 10000) {
        checksToRun.push(monitor);
      }
    }

    console.log(`Running checks for ${checksToRun.length} monitors`);

    // Run checks with concurrency control
    const results = await runWithConcurrency(checksToRun, async (monitor)=>{
      console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);
      const checkResult = await performCheck(monitor);
      console.log(`Check result for ${monitor.name}: ${checkResult.status ? 'UP' : 'DOWN'} (${checkResult.response_time}ms)`);

      // Save the check result
      const { error: insertError } = await supabase.from('monitor_history').insert(checkResult);

      if (insertError) {
        console.error(`Error saving check result: ${insertError.message}`);
      }

      // Update the monitor's status in the monitors table
      let monitorStatus = checkResult.status ? 'up' : 'down';

      // Get the effective degraded settings for this monitor using the database function
      const { data: effectiveSettings, error: settingsError } = await supabase
        .rpc('get_monitor_degraded_settings', { monitor_id: monitor.id });

      if (settingsError) {
        console.error(`Error getting degraded settings for monitor ${monitor.name}:`, settingsError);
      }

      // If the monitor is up but meets degraded criteria, mark it as degraded
      if (monitorStatus === 'up' && checkResult.response_time && effectiveSettings) {
        // Get the response time threshold from the effective settings
        const responseTimeThreshold = effectiveSettings.response_time || 1000;

        if (checkResult.response_time > responseTimeThreshold) {
          console.log(`Monitor ${monitor.name} response time (${checkResult.response_time}ms) exceeds threshold (${responseTimeThreshold}ms), marking as degraded`);
          monitorStatus = 'degraded';
        } else {
          console.log(`Monitor ${monitor.name} response time (${checkResult.response_time}ms) is within threshold (${responseTimeThreshold}ms), status is UP`);
        }
      }

      // Update the monitor status in the database
      const { error: updateError } = await supabase
        .from('monitors')
        .update({
          status: monitorStatus,
          last_check_time: checkResult.timestamp,
          last_response_time: checkResult.response_time
        })
        .eq('id', monitor.id);

      if (updateError) {
        console.error(`Error updating monitor status: ${updateError.message}`);
      }

      // Check if status changed and send notification if needed
      const statusChanged = await checkStatusChange(supabase, monitor, checkResult.status);
      if (statusChanged) {
        await sendNotification(supabase, monitor, checkResult.status);
      }

      return {
        monitor_id: monitor.id,
        name: monitor.name,
        status: checkResult.status,
        response_time: checkResult.response_time,
        error_message: checkResult.error_message
      };
    }, 5);

    return new Response(JSON.stringify({
      success: true,
      checksRun: results.length,
      results
    }), {
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  } catch (error) {
    console.error('Error in monitor checker:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    });
  }
});
