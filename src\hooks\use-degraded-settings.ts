import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { DegradedThresholds } from '@/types/monitor';

export function useDegradedSettings(monitorId: string, isSuperadmin: boolean) {
  const [degradedSettings, setDegradedSettings] = useState<DegradedThresholds | null>(null);
  const [globalDegradedSettings, setGlobalDegradedSettings] = useState<DegradedThresholds | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchDegradedSettings = async () => {
    if (!monitorId || !isSuperadmin) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      // Fetch global settings
      const { data: globalData, error: globalError } = await supabase
        .from('degraded_settings')
        .select('*')
        .limit(1)
        .single();

      if (globalError && globalError.code !== 'PGRST116') {
        console.error('Error fetching global degraded settings:', globalError);
      } else if (globalData) {
        setGlobalDegradedSettings({
          response_time: globalData.response_time,
          error_rate: globalData.error_rate,
          status_codes: globalData.status_codes || [],
          consecutive_failures: globalData.consecutive_failures
        });
      }

      // Fetch monitor-specific settings using RPC function
      const { data: monitorData, error: monitorError } = await supabase
        .rpc('get_monitor_degraded_settings_rpc', { p_monitor_id: monitorId });

      if (monitorError) {
        console.error('Error fetching monitor degraded settings:', monitorError);
      } else if (monitorData) {
        setDegradedSettings({
          response_time: monitorData.response_time,
          error_rate: monitorData.error_rate,
          status_codes: monitorData.status_codes || [],
          consecutive_failures: monitorData.consecutive_failures
        });
      }
    } catch (err) {
      console.error('Error fetching degraded settings:', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDegradedSettings();
  }, [monitorId, isSuperadmin]);

  return {
    degradedSettings,
    globalDegradedSettings,
    isLoading,
    fetchDegradedSettings
  };
}
