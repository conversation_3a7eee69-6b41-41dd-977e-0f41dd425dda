import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Loader2, UserPlus, Shield, UserMinus, Building, User, UserCog } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface User {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  created_at: string;
  roles: {
    global: string[];
    companies: {
      company_id: string;
      company_name: string;
      role_type: string;
    }[];
  };
}

const UserRolesDisplay = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { user: currentUser } = useAuth();

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      // Use the RPC function to get all users with their roles
      const { data, error } = await supabase
        .rpc('get_all_users_with_roles');

      if (error) throw error;

      console.log('Users with roles:', data);

      // Transform the data to match our component's expected format
      const transformedUsers = data.map((user: any) => ({
        ...user,
        roles: {
          global: user.global_roles || [],
          companies: user.company_roles || []
        }
      }));

      setUsers(transformedUsers);
    } catch (err) {
      console.error('Error fetching users and roles:', err);
      toast({
        title: 'Error',
        description: `Failed to fetch users and roles: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = (name: string | null, email: string) => {
    if (name) {
      const nameParts = name.split(' ');
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return name[0].toUpperCase();
    }
    return email ? email[0].toUpperCase() : 'U';
  };

  // Add superadmin role to a user
  const addSuperadminRole = async (userId: string) => {
    try {
      // Use the RPC function to add the superadmin role
      const { data, error } = await supabase
        .rpc('add_superadmin_role', { p_user_id: userId });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Superadmin role added successfully',
      });

      // Refresh the users list
      fetchUsers();
    } catch (err) {
      console.error('Error adding superadmin role:', err);
      toast({
        title: 'Error',
        description: `Failed to add superadmin role: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  // Remove superadmin role from a user
  const removeSuperadminRole = async (userId: string) => {
    try {
      // Use the RPC function to remove the superadmin role
      const { data, error } = await supabase
        .rpc('remove_superadmin_role', { p_user_id: userId });

      if (error) throw error;

      toast({
        title: 'Success',
        description: 'Superadmin role removed successfully',
      });

      // Refresh the users list
      fetchUsers();
    } catch (err) {
      console.error('Error removing superadmin role:', err);
      toast({
        title: 'Error',
        description: `Failed to remove superadmin role: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  // Update a company member's role
  const updateCompanyMemberRole = async (memberId: string, newRole: string) => {
    try {
      // Use the RPC function to update the company member role
      const { data, error } = await supabase
        .rpc('set_company_member_role', {
          p_member_id: memberId,
          p_role_type: newRole
        });

      if (error) throw error;

      toast({
        title: 'Success',
        description: `Role updated to ${newRole} successfully`,
      });

      // Refresh the users list
      fetchUsers();
    } catch (err) {
      console.error('Error updating company member role:', err);
      toast({
        title: 'Error',
        description: `Failed to update role: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>All Users and Their Roles</CardTitle>
        <Button size="sm" onClick={fetchUsers} disabled={isLoading}>
          {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-6">
            {users.map((user) => (
              <div key={user.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <Avatar className="h-10 w-10 mr-3">
                      {user.avatar_url ? (
                        <AvatarImage src={user.avatar_url} alt={user.full_name || user.email} />
                      ) : null}
                      <AvatarFallback>
                        {getUserInitials(user.full_name, user.email)}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium">{user.full_name || 'No Name'}</h3>
                      <p className="text-sm text-slate-500">{user.email}</p>
                    </div>
                  </div>
                  {user.roles.global.includes('superadmin') ? (
                    <div className="flex items-center gap-2">
                      <Badge variant="default" className="bg-red-500">
                        <Shield className="h-3 w-3 mr-1" />
                        Superadmin
                      </Badge>
                      {/* Don't show remove button for current user */}
                      {currentUser && user.id !== currentUser.id && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => removeSuperadminRole(user.id)}
                          className="text-red-500 border-red-200 hover:bg-red-50 hover:text-red-600"
                        >
                          <UserMinus className="h-3 w-3 mr-1" />
                          Remove
                        </Button>
                      )}
                    </div>
                  ) : (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => addSuperadminRole(user.id)}
                    >
                      <UserPlus className="h-3 w-3 mr-1" />
                      Make Superadmin
                    </Button>
                  )}
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Global Roles:</h4>
                  {user.roles.global.length === 0 ? (
                    <p className="text-sm text-slate-500">No global roles</p>
                  ) : (
                    <div className="flex flex-wrap gap-2">
                      {user.roles.global.map((role) => (
                        <Badge key={role} variant="outline">
                          {role}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>

                <div className="mt-3 space-y-2">
                  <h4 className="text-sm font-medium">Company Roles:</h4>
                  {user.roles.companies.length === 0 ? (
                    <p className="text-sm text-slate-500">No company roles</p>
                  ) : (
                    <div className="space-y-2">
                      {user.roles.companies.map((company) => (
                        <div key={company.company_id} className="flex items-center justify-between bg-slate-50 dark:bg-slate-800 p-2 rounded-md">
                          <div className="flex items-center">
                            <Building className="h-3 w-3 mr-1 text-slate-500" />
                            <span className="text-sm">{company.company_name}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant="secondary">
                              {company.role_type === 'admin' ? (
                                <UserCog className="h-3 w-3 mr-1" />
                              ) : (
                                <User className="h-3 w-3 mr-1" />
                              )}
                              {company.role_type}
                            </Badge>
                            {/* Role change buttons */}
                            {company.role_type === 'user' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 px-2 text-xs"
                                onClick={() => updateCompanyMemberRole(company.id, 'admin')}
                              >
                                Make Admin
                              </Button>
                            )}
                            {company.role_type === 'admin' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                className="h-6 px-2 text-xs"
                                onClick={() => updateCompanyMemberRole(company.id, 'user')}
                              >
                                Make User
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UserRolesDisplay;
