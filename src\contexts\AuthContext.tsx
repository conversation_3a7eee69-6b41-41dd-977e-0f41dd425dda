import React, { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

type AuthContextType = {
  session: Session | null;
  user: User | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<{ error: any | null }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any | null, user: User | null }>;
  signOut: () => Promise<void>;
  updateProfile: (data: { fullName?: string; avatarUrl?: string }) => Promise<{ error: any | null, user: User | null }>;
  updatePassword: (password: string) => Promise<{ error: any | null }>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    });

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({ email, password });

      if (error) {
        toast({
          title: "Login failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Login successful",
        description: "Welcome back!",
      });

      return { error: null };
    } catch (error) {
      console.error('Login error:', error);
      let errorMessage = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      toast({
        title: "Login failed",
        description: errorMessage,
        variant: "destructive",
      });
      return { error };
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          }
        }
      });

      if (error) {
        toast({
          title: "Registration failed",
          description: error.message,
          variant: "destructive",
        });
        return { error, user: null };
      }

      // Create user profile in the users table
      if (data.user) {
        try {
          // First check if the users table exists
          const { error: tableCheckError } = await supabase
            .from('users')
            .select('count(*)', { count: 'exact', head: true });

          // If the table doesn't exist or there's an error, we'll try to create the user anyway
          // The trigger we set up in supabase_users_setup.sql should handle this automatically,
          // but we'll try manually as a fallback

          const { error: profileError } = await supabase
            .from('users')
            .insert({
              id: data.user.id,
              email: email,
              full_name: fullName,
            });

          if (profileError) {
            console.warn("Profile creation warning:", profileError);
            // Don't fail registration if profile creation fails
            // The trigger should handle this, or the user can run the setup script
            toast({
              title: "Note",
              description: "User created in Auth, but profile data may need to be synced.",
            });
          } else {
            console.log("User profile created successfully in users table");
          }
        } catch (profileError) {
          console.warn("Profile creation error:", profileError);
          // Continue with registration even if profile creation fails
        }
      }

      toast({
        title: "Registration successful",
        description: "Your account has been created!",
      });

      return { error: null, user: data.user };
    } catch (error) {
      console.error('Registration error:', error);
      let errorMessage = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      toast({
        title: "Registration failed",
        description: errorMessage,
        variant: "destructive",
      });
      return { error, user: null };
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    }
  };

  const updateProfile = async (data: { fullName?: string; avatarUrl?: string }) => {
    try {
      console.log('Updating user profile with data:', data);

      // Add a timestamp to the avatar URL to prevent caching issues
      let avatarUrl = data.avatarUrl;
      if (avatarUrl) {
        // Remove any existing timestamp parameter
        avatarUrl = avatarUrl.split('?')[0];
        // Add a new timestamp
        avatarUrl = `${avatarUrl}?t=${new Date().getTime()}`;
        console.log('Updated avatar URL with timestamp:', avatarUrl);

        // Log the data structure we're sending to Supabase
        console.log('Data being sent to updateUser:', {
          data: {
            full_name: data.fullName,
            avatar_url: avatarUrl,
          }
        });
      }

      const { data: userData, error } = await supabase.auth.updateUser({
        data: {
          full_name: data.fullName,
          avatar_url: avatarUrl,
        },
      });

      console.log('User update response:', userData);

      if (error) {
        toast({
          title: "Profile update failed",
          description: error.message,
          variant: "destructive",
        });
        return { error, user: null };
      }

      // Update the user profile in the users table if fullName is provided
      if (userData.user) {
        try {
          // First check if user exists in the users table
          const { data: userExists, error: checkError } = await supabase
            .from('users')
            .select('id')
            .eq('id', userData.user.id)
            .single();

          if (checkError || !userExists) {
            // User doesn't exist in users table, create it
            console.log('User not found in users table, creating profile...');

            const { error: insertError } = await supabase
              .from('users')
              .insert({
                id: userData.user.id,
                email: userData.user.email || '',
                full_name: data.fullName || userData.user.user_metadata?.full_name || '',
              });

            if (insertError) {
              console.warn("Profile creation error:", insertError);
            } else {
              console.log('User profile created successfully');
            }
          } else if (data.fullName) {
            // User exists, update the full_name
            const { error: profileError } = await supabase
              .from('users')
              .update({
                full_name: data.fullName,
              })
              .eq('id', userData.user.id);

            if (profileError) {
              console.warn("Profile update warning:", profileError);
            } else {
              console.log('User profile updated successfully');
            }
          }
        } catch (profileError) {
          console.warn("Profile update error:", profileError);
        }
      }

      return { error: null, user: userData.user };
    } catch (error) {
      console.error('Profile update error:', error);
      let errorMessage = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      toast({
        title: "Profile update failed",
        description: errorMessage,
        variant: "destructive",
      });
      return { error, user: null };
    }
  };

  const updatePassword = async (password: string) => {
    try {
      const { error } = await supabase.auth.updateUser({ password });

      if (error) {
        toast({
          title: "Password update failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Password updated",
        description: "Your password has been updated successfully.",
      });

      return { error: null };
    } catch (error) {
      console.error('Password update error:', error);
      let errorMessage = "An unexpected error occurred";

      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      toast({
        title: "Password update failed",
        description: errorMessage,
        variant: "destructive",
      });
      return { error };
    }
  };

  const value = {
    session,
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    updatePassword,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
