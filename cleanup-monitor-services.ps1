# PowerShell script to clean up duplicate monitor service implementations
# This script will rename the files we want to keep and remove the ones we don't need

# Navigate to the monitor-service directory
cd monitor-service

# Rename monitor-service.js to index.js if it's not already named that
if (Test-Path "monitor-service.js") {
    Write-Host "Renaming monitor-service.js to index.js..."
    if (Test-Path "index.js") {
        # Backup the existing index.js first
        Copy-Item "index.js" "index.js.bak"
        Write-Host "Backed up existing index.js to index.js.bak"
    }
    Copy-Item "monitor-service.js" "index.js"
    Write-Host "Copied monitor-service.js to index.js"
}

# Create a backup directory for the files we're removing
if (-not (Test-Path "backup")) {
    New-Item -ItemType Directory -Path "backup"
    Write-Host "Created backup directory"
}

# Move the files we don't need to the backup directory
$filesToBackup = @(
    "simple-monitor-service.js",
    "simple-monitor-service-ignore-active.js",
    "index-no-filter.js"
)

foreach ($file in $filesToBackup) {
    if (Test-Path $file) {
        Move-Item $file "backup/$file"
        Write-Host "Moved $file to backup directory"
    } else {
        Write-Host "$file not found, skipping"
    }
}

# Update package.json to remove scripts that reference the removed files
if (Test-Path "package.json") {
    Write-Host "Updating package.json..."
    $packageJson = Get-Content "package.json" -Raw | ConvertFrom-Json
    
    # Remove the scripts that reference the removed files
    if ($packageJson.scripts.PSObject.Properties["start-no-filter"]) {
        $packageJson.scripts.PSObject.Properties.Remove("start-no-filter")
    }
    
    # Save the updated package.json
    $packageJson | ConvertTo-Json -Depth 10 | Set-Content "package.json"
    Write-Host "Updated package.json"
}

Write-Host "Cleanup complete!"
