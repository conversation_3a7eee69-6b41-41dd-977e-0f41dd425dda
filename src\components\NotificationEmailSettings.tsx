import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Mail, Info, Users } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface NotificationEmailSettingsProps {
  isSuperadmin: boolean;
}

/**
 * This component replaces the previous NotificationEmailSettings component.
 * Instead of configuring a global notification email, all superadmins now
 * automatically receive notifications for all monitor status changes.
 */
const NotificationEmailSettings: React.FC<NotificationEmailSettingsProps> = ({
  isSuperadmin
}) => {
  if (!isSuperadmin) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Mail className="h-5 w-5 mr-2 text-blue-500" />
          Notification Recipients
          <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
            Superadmin Only
          </Badge>
        </CardTitle>
        <CardDescription>
          Information about who receives monitor status notifications.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-start gap-4 p-4 border rounded-md bg-slate-50 dark:bg-slate-800">
            <Users className="h-5 w-5 mt-0.5 text-blue-500" />
            <div>
              <h3 className="font-medium mb-1">Automatic Notification System</h3>
              <p className="text-sm text-slate-600 dark:text-slate-300">
                Monitor status notifications are automatically sent to:
              </p>
              <ul className="mt-2 space-y-1 text-sm list-disc pl-5">
                <li>All company admins for the affected company</li>
                <li>All superadmins in the system</li>
              </ul>
              <p className="mt-3 text-sm text-slate-500 dark:text-slate-400">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="flex items-center cursor-help">
                        <Info className="h-4 w-4 mr-1 text-slate-400" />
                        Why was this changed?
                      </span>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-md">
                      <div className="space-y-2 p-2">
                        <p className="text-sm">
                          The previous global notification email setting has been replaced with automatic
                          notifications to all superadmins. This ensures that all system administrators
                          are informed of monitor status changes without requiring manual configuration.
                        </p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationEmailSettings;
