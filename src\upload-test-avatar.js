// This script will upload a test avatar to your Supabase storage
// Run this in the browser console while on your application

async function uploadTestAvatar() {
  console.log('Starting test avatar upload...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('User is logged in:', user);
  
  // Create a simple test image (1x1 pixel blue PNG)
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
  const byteCharacters = atob(base64Image);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: 'image/png' });
  const file = new File([blob], 'test-avatar.png', { type: 'image/png' });
  
  // Try to upload the test image
  console.log('Uploading test image...');
  const fileName = `${user.id}.png`;
  
  // First, check if we can list the buckets
  console.log('Checking buckets...');
  const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
  
  if (bucketsError) {
    console.error('Error listing buckets:', bucketsError);
    return;
  }
  
  console.log('Available buckets:', buckets);
  
  // Check if the avatars bucket exists
  const avatarBucket = buckets.find(b => b.name === 'avatars');
  if (!avatarBucket) {
    console.error('Avatars bucket does not exist!');
    return;
  }
  
  console.log('Avatars bucket found:', avatarBucket);
  
  // Try to list files in the avatars bucket
  console.log('Listing files in avatars bucket...');
  const { data: files, error: filesError } = await supabase.storage
    .from('avatars')
    .list();
  
  if (filesError) {
    console.error('Error listing files in avatars bucket:', filesError);
    // Continue anyway, as we might still be able to upload
  } else {
    console.log('Files in avatars bucket:', files);
  }
  
  // Try to upload the file
  console.log('Uploading file to avatars bucket...');
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('avatars')
    .upload(fileName, file, { upsert: true });
  
  if (uploadError) {
    console.error('Error uploading file:', uploadError);
    
    // Try with a different path
    console.log('Trying with a different path...');
    const altFileName = `avatar.png`;
    
    const { data: altUploadData, error: altUploadError } = await supabase.storage
      .from('avatars')
      .upload(altFileName, file, { upsert: true });
    
    if (altUploadError) {
      console.error('Error uploading with alternative path:', altUploadError);
      return;
    }
    
    console.log('Upload with alternative path successful:', altUploadData);
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(altFileName);
    
    const avatarUrl = urlData.publicUrl;
    console.log('Generated public URL for avatar:', avatarUrl);
    
    // Update the user's metadata with the avatar URL
    console.log('Updating user metadata with avatar URL...');
    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: {
        avatar_url: avatarUrl,
      },
    });
    
    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      return;
    }
    
    console.log('User metadata updated successfully:', userData);
    
    return {
      success: true,
      avatarUrl,
      userData,
    };
  }
  
  console.log('Upload successful:', uploadData);
  
  // Get the public URL
  const { data: urlData } = supabase.storage
    .from('avatars')
    .getPublicUrl(fileName);
  
  const avatarUrl = urlData.publicUrl;
  console.log('Generated public URL for avatar:', avatarUrl);
  
  // Update the user's metadata with the avatar URL
  console.log('Updating user metadata with avatar URL...');
  const { data: userData, error: updateError } = await supabase.auth.updateUser({
    data: {
      avatar_url: avatarUrl,
    },
  });
  
  if (updateError) {
    console.error('Error updating user metadata:', updateError);
    return;
  }
  
  console.log('User metadata updated successfully:', userData);
  
  // Create a preview of the avatar
  const img = new Image();
  img.src = avatarUrl;
  img.onload = () => {
    console.log('Avatar image loaded successfully!');
    
    // Create a div to display the image on the page
    const div = document.createElement('div');
    div.style.position = 'fixed';
    div.style.top = '10px';
    div.style.right = '10px';
    div.style.zIndex = '9999';
    div.style.background = 'white';
    div.style.padding = '10px';
    div.style.border = '1px solid black';
    div.style.borderRadius = '5px';
    div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
    
    const imgElement = document.createElement('img');
    imgElement.src = avatarUrl;
    imgElement.style.width = '100px';
    imgElement.style.height = '100px';
    imgElement.style.borderRadius = '50%';
    imgElement.style.objectFit = 'cover';
    
    const message = document.createElement('p');
    message.textContent = 'Avatar uploaded successfully! Please refresh the page.';
    message.style.marginTop = '10px';
    message.style.fontSize = '14px';
    
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.marginTop = '10px';
    closeButton.style.padding = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => document.body.removeChild(div);
    
    div.appendChild(imgElement);
    div.appendChild(message);
    div.appendChild(closeButton);
    
    document.body.appendChild(div);
  };
  
  img.onerror = () => {
    console.error('Error loading avatar image. The URL might not be publicly accessible.');
  };
  
  return {
    success: true,
    avatarUrl,
    userData,
  };
}

// Run the test
uploadTestAvatar().then(result => {
  console.log('Test completed. Results:', result);
});
