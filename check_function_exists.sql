-- This script creates a function to check if another function exists
-- Run this in the Supabase SQL Editor

CREATE OR REPLACE FUNCTION check_function_exists(function_name TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    func_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1
        FROM information_schema.routines
        WHERE routine_name = function_name
        AND routine_schema = 'public'
    ) INTO func_exists;
    
    RETURN func_exists;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
