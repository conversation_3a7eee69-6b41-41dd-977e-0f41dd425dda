import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw, ExternalLink } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import MonitorStatusCard from './MonitorStatusCard';
import MonitorResponseTimeChart from './MonitorResponseTimeChart';

interface MonitorOverviewTabProps {
  monitor: any;
  history: any[];
  chartData: any[];
  uptimeStats: {
    uptime24h: number;
    uptime7d: number;
    avgResponseTime: number;
  };
  isLoading: boolean;
  retentionDays: number;
  onRefresh: () => void;
}

const MonitorOverviewTab: React.FC<MonitorOverviewTabProps> = ({
  monitor,
  history,
  chartData,
  uptimeStats,
  isLoading,
  retentionDays,
  onRefresh
}) => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <MonitorStatusCard
          status={history.length > 0 ? history[0]?.status : false}
          isActive={monitor.active}
          hasHistory={history.length > 0}
          errorMessage={history.length > 0 ? history[0]?.error_message : undefined}
        />

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-slate-500 dark:text-slate-400">Uptime (24h)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <div className="text-2xl font-bold">
                {uptimeStats.uptime24h.toFixed(2)}%
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-slate-500 dark:text-slate-400">Uptime (7d)</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-24" />
            ) : (
              <div className="text-2xl font-bold">
                {uptimeStats.uptime7d.toFixed(2)}%
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Monitor Details</CardTitle>
          <CardDescription>
            Configuration and status information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-medium mb-2">Basic Information</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Target URL:</span>
                  <a
                    href={monitor.target}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="font-medium text-blue-600 dark:text-blue-400 hover:underline flex items-center"
                  >
                    {monitor.target}
                    <ExternalLink className="h-3 w-3 ml-1" />
                  </a>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Status:</span>
                  <span className="font-medium">
                    {monitor.active ? 'Active' : 'Paused'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Check Interval:</span>
                  <span className="font-medium">{monitor.interval} minutes</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-medium mb-2">Performance</h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Avg. Response Time:</span>
                  <span className="font-medium">
                    {isLoading ? (
                      <Skeleton className="h-4 w-16 inline-block" />
                    ) : (
                      `${uptimeStats.avgResponseTime.toFixed(0)} ms`
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Last Check:</span>
                  <span className="font-medium">
                    {isLoading ? (
                      <Skeleton className="h-4 w-24 inline-block" />
                    ) : history.length > 0 ? (
                      new Date(history[0].timestamp).toLocaleString()
                    ) : (
                      'No checks yet'
                    )}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Total Checks:</span>
                  <span className="font-medium">
                    {isLoading ? (
                      <Skeleton className="h-4 w-12 inline-block" />
                    ) : (
                      history.length
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4">
            <Button
              variant="outline"
              onClick={onRefresh}
              disabled={isLoading}
              className="w-full"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Loading...' : 'Refresh Data'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <MonitorResponseTimeChart
        chartData={chartData}
        isLoading={isLoading}
        retentionDays={retentionDays}
      />
    </div>
  );
};

export default MonitorOverviewTab;
