import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useCompany } from '@/contexts/CompanyContext';
import { useAuth } from '@/contexts/AuthContext';
import {
  getSubscriptionTiers,
  getCompanySubscriptionInfo,
  updateCompanySubscriptionTier,
  updateCompanySubscriptionOverrides,
  canAddMonitor,
  checkCompanyMonitorLimit,
  createSubscriptionTier,
  updateSubscriptionTier,
  deleteSubscriptionTier,
  getSubscriptionFeatures,
  createSubscriptionFeature,
  updateSubscriptionFeature,
  deleteSubscriptionFeature,
  getTierFeatures,
  updateTierFeature
} from '@/services/subscription-service';
import { SubscriptionOverrides } from '@/types/subscription';

export function useSubscription() {
  const queryClient = useQueryClient();
  const { currentCompany } = useCompany();
  const { user, isSuperadmin } = useAuth();

  // Get all subscription tiers
  const useSubscriptionTiers = () => {
    return useQuery({
      queryKey: ['subscription-tiers'],
      queryFn: () => getSubscriptionTiers(),
      enabled: !!user,
    });
  };

  // Get company subscription info
  const useCompanySubscription = (companyId?: string) => {
    const targetCompanyId = companyId || currentCompany?.id;

    return useQuery({
      queryKey: ['company-subscription', targetCompanyId],
      queryFn: () => getCompanySubscriptionInfo(targetCompanyId!),
      enabled: !!targetCompanyId && !!user,
    });
  };

  // Update company subscription tier
  const useUpdateCompanySubscriptionTier = () => {
    return useMutation({
      mutationFn: ({ companyId, tierIdOrName }: { companyId: string, tierIdOrName: string }) =>
        updateCompanySubscriptionTier(companyId, tierIdOrName),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['company-subscription', variables.companyId] });
        toast({
          title: 'Subscription Updated',
          description: 'The company subscription tier has been updated successfully.',
        });
      },
      onError: (error) => {
        console.error('Error updating subscription tier:', error);
        toast({
          title: 'Error',
          description: 'Failed to update the subscription tier. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Update company subscription overrides
  const useUpdateCompanySubscriptionOverrides = () => {
    return useMutation({
      mutationFn: ({ companyId, overrides }: { companyId: string, overrides: SubscriptionOverrides }) =>
        updateCompanySubscriptionOverrides(companyId, overrides),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['company-subscription', variables.companyId] });
        toast({
          title: 'Subscription Overrides Updated',
          description: 'The company subscription overrides have been updated successfully.',
        });
      },
      onError: (error) => {
        console.error('Error updating subscription overrides:', error);
        toast({
          title: 'Error',
          description: 'Failed to update the subscription overrides. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Check if company can add more monitors
  const useCanAddMonitor = (companyId?: string) => {
    const targetCompanyId = companyId || currentCompany?.id;

    return useQuery({
      queryKey: ['can-add-monitor', targetCompanyId],
      queryFn: () => canAddMonitor(targetCompanyId!),
      enabled: !!targetCompanyId && !!user,
    });
  };

  // Get detailed monitor limit information
  const useCompanyMonitorLimit = (companyId?: string) => {
    const targetCompanyId = companyId || currentCompany?.id;

    return useQuery({
      queryKey: ['company-monitor-limit', targetCompanyId],
      queryFn: () => checkCompanyMonitorLimit(targetCompanyId!),
      enabled: !!targetCompanyId && !!user,
    });
  };

  // Create a new subscription tier
  const useCreateSubscriptionTier = () => {
    return useMutation({
      mutationFn: (tierData: Omit<SubscriptionTier, 'id' | 'created_at' | 'updated_at'>) =>
        createSubscriptionTier(tierData),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['subscription-tiers'] });
        toast({
          title: 'Tier Created',
          description: 'The subscription tier has been created successfully.',
        });
      },
      onError: (error) => {
        console.error('Error creating subscription tier:', error);
        toast({
          title: 'Error',
          description: 'Failed to create the subscription tier. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Delete a subscription tier
  const useDeleteSubscriptionTier = () => {
    return useMutation({
      mutationFn: (tierId: string) => deleteSubscriptionTier(tierId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['subscription-tiers'] });
        toast({
          title: 'Tier Deleted',
          description: 'The subscription tier has been deleted successfully.',
        });
      },
      onError: (error: any) => {
        console.error('Error deleting subscription tier:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete the subscription tier. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Get all subscription features
  const useSubscriptionFeatures = () => {
    return useQuery({
      queryKey: ['subscription-features'],
      queryFn: () => getSubscriptionFeatures(),
      enabled: !!user,
    });
  };

  // Create a new subscription feature
  const useCreateSubscriptionFeature = () => {
    return useMutation({
      mutationFn: (featureData: { name: string; description: string }) =>
        createSubscriptionFeature(featureData),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['subscription-features'] });
        toast({
          title: 'Feature Created',
          description: 'The subscription feature has been created successfully.',
        });
      },
      onError: (error) => {
        console.error('Error creating subscription feature:', error);
        toast({
          title: 'Error',
          description: 'Failed to create the subscription feature. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Update a subscription feature
  const useUpdateSubscriptionFeature = () => {
    return useMutation({
      mutationFn: ({ id, updates }: { id: string; updates: { name?: string; description?: string } }) =>
        updateSubscriptionFeature(id, updates),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['subscription-features'] });
        toast({
          title: 'Feature Updated',
          description: 'The subscription feature has been updated successfully.',
        });
      },
      onError: (error) => {
        console.error('Error updating subscription feature:', error);
        toast({
          title: 'Error',
          description: 'Failed to update the subscription feature. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Delete a subscription feature
  const useDeleteSubscriptionFeature = () => {
    return useMutation({
      mutationFn: (featureId: string) => deleteSubscriptionFeature(featureId),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['subscription-features'] });
        toast({
          title: 'Feature Deleted',
          description: 'The subscription feature has been deleted successfully.',
        });
      },
      onError: (error: any) => {
        console.error('Error deleting subscription feature:', error);
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete the subscription feature. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  // Get features for a specific tier
  const useTierFeatures = (tierId?: string) => {
    return useQuery({
      queryKey: ['tier-features', tierId],
      queryFn: () => getTierFeatures(tierId!),
      enabled: !!tierId && !!user,
    });
  };

  // Update a tier feature
  const useUpdateTierFeature = () => {
    return useMutation({
      mutationFn: ({
        tierId,
        featureId,
        value,
        isEnabled
      }: {
        tierId: string;
        featureId: string;
        value: string;
        isEnabled: boolean
      }) => updateTierFeature(tierId, featureId, value, isEnabled),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['tier-features', variables.tierId] });
        toast({
          title: 'Feature Updated',
          description: 'The tier feature has been updated successfully.',
        });
      },
      onError: (error) => {
        console.error('Error updating tier feature:', error);
        toast({
          title: 'Error',
          description: 'Failed to update the tier feature. Please try again.',
          variant: 'destructive',
        });
      },
    });
  };

  return {
    useSubscriptionTiers,
    useCompanySubscription,
    useUpdateCompanySubscriptionTier,
    useUpdateCompanySubscriptionOverrides,
    useCanAddMonitor,
    useCompanyMonitorLimit,
    useCreateSubscriptionTier,
    useDeleteSubscriptionTier,
    useSubscriptionFeatures,
    useCreateSubscriptionFeature,
    useUpdateSubscriptionFeature,
    useDeleteSubscriptionFeature,
    useTierFeatures,
    useUpdateTierFeature,
    isSuperadmin,
  };
}
