-- This script creates the company-related tables if they don't exist
-- Run this in the Supabase SQL Editor

-- Create companies table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create company_members table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.company_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'member', 'viewer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(company_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_company_members_company_id ON public.company_members(company_id);
CREATE INDEX IF NOT EXISTS idx_company_members_user_id ON public.company_members(user_id);

-- Create a default company for the current user if they don't have one
DO $$
DECLARE
    current_user_id UUID;
    user_email TEXT;
    user_company_count INT;
    new_company_id UUID;
BEGIN
    -- Get the current user ID
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RAISE NOTICE 'No authenticated user found';
        RETURN;
    END IF;
    
    -- Get the user's email
    SELECT email INTO user_email FROM auth.users WHERE id = current_user_id;
    
    -- Check if the user already has a company
    SELECT COUNT(*) INTO user_company_count
    FROM public.company_members
    WHERE user_id = current_user_id;
    
    RAISE NOTICE 'User % has % companies', current_user_id, user_company_count;
    
    -- If the user doesn't have a company, create one
    IF user_company_count = 0 THEN
        -- Create a default company for the user
        INSERT INTO public.companies (name, description)
        VALUES (CONCAT(user_email, '''s Company'), CONCAT('Default company for ', user_email))
        RETURNING id INTO new_company_id;

        RAISE NOTICE 'Created new company with ID: %', new_company_id;

        -- Add the user as an admin to the company
        INSERT INTO public.company_members (company_id, user_id, role)
        VALUES (new_company_id, current_user_id, 'admin');

        RAISE NOTICE 'Added user % as admin to company %', current_user_id, new_company_id;
    END IF;
END $$;
