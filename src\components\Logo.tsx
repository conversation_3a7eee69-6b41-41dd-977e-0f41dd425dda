import React from 'react';
import { Link } from 'react-router-dom';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  showText = true,
  className = ''
}) => {
  // Size mappings
  const sizeClasses = {
    sm: 'h-6 w-auto',
    md: 'h-8 w-auto',
    lg: 'h-12 w-auto'
  };

  const textSizeClasses = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  return (
    <Link to="/" className={`flex items-center space-x-2 ${className}`}>
      <img 
        src="/static/Vurbis Logo.png" 
        alt="Vurbis Logo" 
        className={sizeClasses[size]} 
      />
      {showText && (
        <span className={`font-bold ${textSizeClasses[size]}`}>
          Vurbis Uptime Monitor
        </span>
      )}
    </Link>
  );
};

export default Logo;
