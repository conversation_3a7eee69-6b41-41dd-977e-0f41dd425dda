// This is a simple script to test uploading a file to the avatars bucket
// Run this in the browser console

async function simpleUploadTest() {
  console.log('Starting simple upload test...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('User is logged in:', user);
  
  // Create a simple test image (1x1 pixel blue PNG)
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==';
  const byteCharacters = atob(base64Image);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: 'image/png' });
  const file = new File([blob], 'test-avatar.png', { type: 'image/png' });
  
  // Try to upload the test image with a simple file name
  console.log('Uploading test image...');
  const fileName = 'test-avatar.png';
  
  try {
    // Try to upload the file
    console.log('Uploading file to avatars bucket...');
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(fileName, file, { upsert: true });
    
    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return;
    }
    
    console.log('Upload successful:', uploadData);
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(fileName);
    
    const avatarUrl = urlData.publicUrl;
    console.log('Generated public URL for avatar:', avatarUrl);
    
    // Try to access the URL
    const img = new Image();
    img.src = avatarUrl;
    
    img.onload = () => {
      console.log('Image loaded successfully!');
      console.log('Test PASSED ✅');
    };
    
    img.onerror = () => {
      console.error('Image failed to load. The URL might not be publicly accessible.');
      console.log('Test FAILED ❌');
    };
    
    return {
      success: true,
      avatarUrl,
      uploadData
    };
  } catch (error) {
    console.error('Unexpected error during upload test:', error);
    return {
      success: false,
      error
    };
  }
}

// Run the test
simpleUploadTest().then(result => {
  console.log('Test completed. Results:', result);
});
