-- This script fixes the relationship between company_members and users tables
-- Run this in the Supabase SQL Editor

-- First, check if the auth.users table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'auth' AND table_name = 'users'
    ) THEN
        RAISE NOTICE 'auth.users table exists';
    ELSE
        RAISE EXCEPTION 'auth.users table does not exist';
    END IF;
END $$;

-- Drop the view if it exists
DROP VIEW IF EXISTS public.users;

-- Create a public.users view that references auth.users
CREATE VIEW public.users AS
SELECT 
    id,
    email,
    raw_user_meta_data->>'full_name' as full_name,
    raw_user_meta_data->>'avatar_url' as avatar_url
FROM auth.users;

-- Grant permissions on the view
GRANT SELECT ON public.users TO anon, authenticated, service_role;

-- Verify the view was created
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'users';

-- Test the view
SELECT * FROM public.users LIMIT 5;
