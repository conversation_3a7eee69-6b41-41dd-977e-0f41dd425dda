import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, AlertTriangle, Clock, Mail } from 'lucide-react';
import UnifiedHeader from '@/components/UnifiedHeader';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import HistoryRetentionSettings from '@/components/HistoryRetentionSettings';
import SuperadminTestNotification from '@/components/SuperadminTestNotification';
import DegradedSettingsForm from '@/components/DegradedSettingsForm';
import { toast } from '@/components/ui/use-toast';
import AppLayout from '@/components/AppLayout';
import DocumentTitle from '@/components/DocumentTitle';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const GlobalSettings = () => {
  const navigate = useNavigate();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false, isLoading: checkingSuperadmin } = useGlobalSuperadminQuery();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is superadmin
    if (!checkingSuperadmin && !isSuperadmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access global settings.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }

    setIsLoading(false);
  }, [isSuperadmin, checkingSuperadmin, navigate]);

  if (isLoading || checkingSuperadmin) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <DocumentTitle title="Loading..." />
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold">Loading settings...</h2>
        </div>
      </div>
    );
  }

  if (!isSuperadmin) {
    return null; // Will redirect in useEffect
  }

  const header = (
    <UnifiedHeader
      title="Global Settings"
      icon={Settings}
      description="Configure global application settings"
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Global Settings" />
      <div className="container mx-auto py-6 px-4">
        <div className="max-w-3xl mx-auto">

          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Global Configuration</CardTitle>
              <CardDescription>
                These settings apply to all companies and users unless overridden.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-slate-500 dark:text-slate-400 mb-4">
                As a superadmin, you can configure global settings that affect the entire application.
                Individual companies may override some of these settings with their own configurations.
              </p>
            </CardContent>
          </Card>

          <Tabs defaultValue="retention" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="retention" className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                Data Retention
              </TabsTrigger>
              <TabsTrigger value="notifications" className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="degraded" className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Degraded Service Thresholds
              </TabsTrigger>
            </TabsList>

            <TabsContent value="retention" className="space-y-4">
              {/* History Retention Settings */}
              <HistoryRetentionSettings isSuperadmin={isSuperadmin} />
            </TabsContent>

            <TabsContent value="notifications" className="space-y-4">
              {/* Test Notification Feature */}
              <SuperadminTestNotification isSuperadmin={isSuperadmin} />
            </TabsContent>

            <TabsContent value="degraded" className="space-y-4">
              <DegradedSettingsForm />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AppLayout>
  );
};

export default GlobalSettings;
