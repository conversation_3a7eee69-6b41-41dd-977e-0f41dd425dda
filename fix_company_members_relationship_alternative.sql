-- This script fixes the relationship between company_members and users tables
-- Run this in the Supabase SQL Editor

-- First, check if the auth.users table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'auth' AND table_name = 'users'
    ) THEN
        RAISE NOTICE 'auth.users table exists';
    ELSE
        RAISE EXCEPTION 'auth.users table does not exist';
    END IF;
END $$;

-- Drop the users table if it exists
DROP TABLE IF EXISTS public.users;

-- Create a public.users table that mirrors auth.users
CREATE TABLE public.users (
    id UUID PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT
);

-- Insert data from auth.users into public.users
INSERT INTO public.users (id, email, full_name, avatar_url)
SELECT 
    id,
    email,
    raw_user_meta_data->>'full_name' as full_name,
    raw_user_meta_data->>'avatar_url' as avatar_url
FROM auth.users;

-- Create a trigger to keep public.users in sync with auth.users
CREATE OR REPLACE FUNCTION sync_users()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        -- Upsert into public.users
        INSERT INTO public.users (id, email, full_name, avatar_url)
        VALUES (
            NEW.id,
            NEW.email,
            NEW.raw_user_meta_data->>'full_name',
            NEW.raw_user_meta_data->>'avatar_url'
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            full_name = EXCLUDED.full_name,
            avatar_url = EXCLUDED.avatar_url;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Delete from public.users
        DELETE FROM public.users WHERE id = OLD.id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers on auth.users
DROP TRIGGER IF EXISTS users_sync_insert_update ON auth.users;
CREATE TRIGGER users_sync_insert_update
AFTER INSERT OR UPDATE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION sync_users();

DROP TRIGGER IF EXISTS users_sync_delete ON auth.users;
CREATE TRIGGER users_sync_delete
AFTER DELETE ON auth.users
FOR EACH ROW
EXECUTE FUNCTION sync_users();

-- Grant permissions on the table
GRANT SELECT ON public.users TO anon, authenticated, service_role;

-- Verify the table was created
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' AND table_name = 'users';

-- Test the table
SELECT * FROM public.users LIMIT 5;
