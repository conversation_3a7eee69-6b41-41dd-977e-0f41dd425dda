// Node.js script to trigger the monitor-checker Edge Function
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration from environment variables
const supabaseProjectRef = process.env.SUPABASE_PROJECT_REF;
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
const logFilePath = path.join(__dirname, 'monitor-checker.log');

// Validate required environment variables
if (!supabaseProjectRef || !supabaseAnonKey) {
  console.error('Error: Required environment variables SUPABASE_PROJECT_REF and SUPABASE_ANON_KEY must be set');
  process.exit(1);
}

// Function to write to log file
function writeLog(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} - ${message}\n`;
  
  fs.appendFileSync(logFilePath, logMessage);
  console.log(message);
}

// Function to trigger the monitor checker
async function triggerMonitorChecker() {
  writeLog('Triggering monitor checker...');
  
  try {
    const url = `https://${supabaseProjectRef}.functions.supabase.co/monitor-checker`;
    const response = await axios.post(url, {}, {
      headers: {
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    writeLog(`Response: Checked ${response.data.checksRun} monitors`);
    
    if (response.data.checksRun > 0) {
      response.data.results.forEach(result => {
        const status = result.status ? 'UP' : 'DOWN';
        writeLog(`Monitor '${result.name}' is ${status} (Response time: ${result.response_time}ms)`);
      });
    } else {
      writeLog('No monitors were due for checking');
    }
  } catch (error) {
    writeLog(`Error: ${error.message}`);
    if (error.response) {
      writeLog(`Response status: ${error.response.status}`);
      writeLog(`Response data: ${JSON.stringify(error.response.data)}`);
    }
  }
}

// Run immediately
triggerMonitorChecker();

// Then run every minute
setInterval(triggerMonitorChecker, 60000);

writeLog('Monitor checker script started');

// Handle graceful shutdown
process.on('SIGINT', () => {
  writeLog('Monitor checker script stopping...');
  process.exit(0);
});
