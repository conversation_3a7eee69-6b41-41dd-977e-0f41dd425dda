-- Drop existing RLS policies on the company_members table
DROP POLICY IF EXISTS "Users can view their own company memberships" ON public.company_members;
DROP POLICY IF EXISTS "Company admins can view company members" ON public.company_members;
DROP POLICY IF EXISTS "Superadmins can view all company memberships" ON public.company_members;
DROP POLICY IF EXISTS "Superadmins can insert company memberships" ON public.company_members;
DROP POLICY IF EXISTS "Superadmins can update company memberships" ON public.company_members;
DROP POLICY IF EXISTS "Superadmins can delete company memberships" ON public.company_members;

-- Create RLS policies for the company_members table
-- 1. Users can view their own company memberships
CREATE POLICY "Users can view their own company memberships"
ON public.company_members
FOR SELECT
USING (user_id = auth.uid());

-- 2. Company admins can view all members of their companies
CREATE POLICY "Company admins can view company members"
ON public.company_members
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = auth.uid()
    AND company_id = company_members.company_id
    AND role_type = 'admin'
  )
);

-- 3. Use the is_global_superadmin function to check superadmin status
-- This avoids the circular dependency
CREATE POLICY "Superadmins can view all company memberships"
ON public.company_members
FOR SELECT
USING (is_global_superadmin());

-- 4. Superadmins can insert new company memberships
CREATE POLICY "Superadmins can insert company memberships"
ON public.company_members
FOR INSERT
WITH CHECK (is_global_superadmin());

-- 5. Superadmins can update company memberships
CREATE POLICY "Superadmins can update company memberships"
ON public.company_members
FOR UPDATE
USING (is_global_superadmin());

-- 6. Superadmins can delete company memberships
CREATE POLICY "Superadmins can delete company memberships"
ON public.company_members
FOR DELETE
USING (is_global_superadmin());

-- Make sure RLS is enabled on the company_members table
ALTER TABLE public.company_members ENABLE ROW LEVEL SECURITY;
