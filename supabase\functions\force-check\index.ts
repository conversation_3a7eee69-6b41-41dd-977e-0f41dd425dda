// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { monitorId } = await req.json()

    // Create a Supabase client with the service role key
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Function to update a monitor's timestamp
    async function updateMonitorTimestamp(id: string, interval: number) {
      // Get the most recent check for this monitor
      const { data: lastCheck, error: lastCheckError } = await supabase
        .from('monitor_history')
        .select('id, timestamp')
        .eq('monitor_id', id)
        .order('timestamp', { ascending: false })
        .limit(1)

      if (lastCheckError) {
        throw new Error(`Failed to get last check: ${lastCheckError.message}`)
      }

      if (!lastCheck || lastCheck.length === 0) {
        console.log(`No previous checks found for monitor ${id}. The monitor service will check it automatically.`)
        return
      }

      // Calculate a new timestamp that will make the monitor due for checking
      // Set it to (interval * 2) minutes ago to ensure it's checked immediately
      // This is more aggressive to ensure it gets picked up
      const newTimestamp = new Date()
      newTimestamp.setMinutes(newTimestamp.getMinutes() - (interval * 2))
      console.log(`Setting timestamp to ${interval * 2} minutes ago: ${newTimestamp.toISOString()}`)

      // Update the timestamp of the most recent check
      const { error: updateError } = await supabase
        .from('monitor_history')
        .update({ timestamp: newTimestamp.toISOString() })
        .eq('id', lastCheck[0].id)

      if (updateError) {
        throw new Error(`Failed to update timestamp: ${updateError.message}`)
      }

      console.log(`Updated timestamp for monitor ${id} to ${newTimestamp.toISOString()}`)
    }

    // If a specific monitor ID is provided, update only that monitor
    if (monitorId) {
      // Get the monitor
      const { data: monitor, error: monitorError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('id', monitorId)
        .single()

      if (monitorError) {
        throw new Error(`Failed to get monitor: ${monitorError.message}`)
      }

      if (!monitor) {
        throw new Error(`Monitor with ID ${monitorId} not found`)
      }

      // Update the monitor's timestamp
      await updateMonitorTimestamp(monitor.id, monitor.interval)

      return new Response(
        JSON.stringify({
          success: true,
          message: `Successfully updated monitor ${monitor.name}. It will be checked in the next cycle.`,
          checksRun: 1,
          results: [{
            monitor_id: monitor.id,
            name: monitor.name,
            status: true,
            response_time: 0,
            message: 'Monitor will be checked in the next cycle'
          }]
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } else {
      // Get all active monitors
      const { data: monitors, error: monitorsError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('active', true)

      if (monitorsError) {
        throw new Error(`Failed to get monitors: ${monitorsError.message}`)
      }

      if (!monitors || monitors.length === 0) {
        return new Response(
          JSON.stringify({
            success: true,
            message: 'No active monitors found',
            checksRun: 0,
            results: []
          }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        )
      }

      // Update each monitor's timestamp
      for (const monitor of monitors) {
        try {
          await updateMonitorTimestamp(monitor.id, monitor.interval)
        } catch (error) {
          console.error(`Error updating monitor ${monitor.name}: ${error.message}`)
        }
      }

      return new Response(
        JSON.stringify({
          success: true,
          message: `Successfully updated ${monitors.length} monitors. They will be checked in the next cycle.`,
          checksRun: monitors.length,
          results: monitors.map(monitor => ({
            monitor_id: monitor.id,
            name: monitor.name,
            status: true,
            response_time: 0,
            message: 'Monitor will be checked in the next cycle'
          }))
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
  } catch (error) {
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    )
  }
})
