import React from 'react';
import { cn } from '@/lib/utils';

interface KeyboardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export function Keyboard({ className, children, ...props }: KeyboardProps) {
  return (
    <div
      className={cn(
        "flex items-center gap-1 text-xs text-slate-500 dark:text-slate-400",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}

interface KeyboardKeyProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: React.ReactNode;
}

export function KeyboardKey({ className, children, ...props }: KeyboardKeyProps) {
  return (
    <span
      className={cn(
        "inline-flex h-5 min-w-5 items-center justify-center rounded border border-slate-200 bg-slate-100 px-1 font-mono text-[10px] font-medium text-slate-800 dark:border-slate-700 dark:bg-slate-800 dark:text-slate-300",
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}
