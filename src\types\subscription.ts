// Subscription tier types

export interface SubscriptionTier {
  id: string;
  name: string;
  description: string | null;
  max_monitors: number;
  history_retention_days: number;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionFeature {
  id: string;
  name: string;
  description: string;
  created_at?: string;
  updated_at?: string;
}

export interface TierFeature {
  id: string;
  tier_id: string;
  feature_id: string;
  value: string;
  is_enabled: boolean;
  created_at?: string;
  updated_at?: string;
  feature?: SubscriptionFeature;
  tier?: SubscriptionTier;
}

export interface CompanySubscriptionInfo {
  tier_name: string;
  tier_description: string | null;
  max_monitors: number;
  current_monitors: number;
  history_retention_days: number;
  is_custom: boolean;
}

export type SubscriptionTierName = 'Free' | 'Premium' | 'Professional' | 'Enterprise';

export interface SubscriptionOverrides {
  custom_max_monitors?: number | null;
  custom_history_retention_days?: number | null;
}
