// This script tests your storage permissions
// Run this in the browser console

async function testStoragePermissions() {
  console.log('Starting storage permissions test...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('User is logged in:', user);
  
  // Test 1: List buckets
  console.log('Test 1: Listing buckets...');
  try {
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
    
    if (bucketsError) {
      console.error('Error listing buckets:', bucketsError);
      console.log('Test 1: FAILED ❌');
    } else {
      console.log('Buckets:', buckets);
      console.log('Test 1: PASSED ✅');
      
      // Check if avatars bucket exists
      const avatarBucket = buckets.find(b => b.name === 'avatars');
      if (avatarBucket) {
        console.log('Avatars bucket found:', avatarBucket);
      } else {
        console.error('Avatars bucket not found in the list of buckets!');
      }
    }
  } catch (error) {
    console.error('Unexpected error in Test 1:', error);
    console.log('Test 1: FAILED ❌');
  }
  
  // Test 2: List files in avatars bucket
  console.log('Test 2: Listing files in avatars bucket...');
  try {
    const { data: files, error: filesError } = await supabase.storage
      .from('avatars')
      .list();
    
    if (filesError) {
      console.error('Error listing files in avatars bucket:', filesError);
      console.log('Test 2: FAILED ❌');
    } else {
      console.log('Files in avatars bucket:', files);
      console.log('Test 2: PASSED ✅');
    }
  } catch (error) {
    console.error('Unexpected error in Test 2:', error);
    console.log('Test 2: FAILED ❌');
  }
  
  // Test 3: Create a tiny text file
  console.log('Test 3: Creating a tiny text file...');
  try {
    const textFile = new File(['Hello, World!'], 'test.txt', { type: 'text/plain' });
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload('test.txt', textFile, { upsert: true });
    
    if (uploadError) {
      console.error('Error uploading text file:', uploadError);
      console.log('Test 3: FAILED ❌');
    } else {
      console.log('Text file uploaded successfully:', uploadData);
      console.log('Test 3: PASSED ✅');
      
      // Get the public URL
      const { data: urlData } = supabase.storage
        .from('avatars')
        .getPublicUrl('test.txt');
      
      console.log('Public URL for text file:', urlData.publicUrl);
    }
  } catch (error) {
    console.error('Unexpected error in Test 3:', error);
    console.log('Test 3: FAILED ❌');
  }
  
  // Test 4: Check if we can get a public URL
  console.log('Test 4: Getting a public URL...');
  try {
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl('test.txt');
    
    console.log('Public URL:', urlData.publicUrl);
    console.log('Test 4: PASSED ✅');
    
    // Try to fetch the URL
    fetch(urlData.publicUrl)
      .then(response => {
        if (response.ok) {
          console.log('URL is accessible!');
          return response.text();
        } else {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
      })
      .then(text => {
        console.log('Content of the file:', text);
      })
      .catch(error => {
        console.error('Error fetching URL:', error);
      });
  } catch (error) {
    console.error('Unexpected error in Test 4:', error);
    console.log('Test 4: FAILED ❌');
  }
  
  console.log('All tests completed.');
}

// Run the test
testStoragePermissions().then(() => {
  console.log('Storage permissions test completed.');
});
