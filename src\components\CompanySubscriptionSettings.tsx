import { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { SubscriptionOverrides } from '@/types/subscription';

interface CompanySubscriptionSettingsProps {
  companyId: string;
  companyName: string;
}

export function CompanySubscriptionSettings({ companyId, companyName }: CompanySubscriptionSettingsProps) {
  const { 
    useSubscriptionTiers, 
    useCompanySubscription, 
    useUpdateCompanySubscriptionTier,
    useUpdateCompanySubscriptionOverrides,
    isSuperadmin 
  } = useSubscription();
  
  const { data: tiers, isLoading: tiersLoading } = useSubscriptionTiers();
  const { data: subscription, isLoading: subscriptionLoading, refetch } = useCompanySubscription(companyId);
  const updateTierMutation = useUpdateCompanySubscriptionTier();
  const updateOverridesMutation = useUpdateCompanySubscriptionOverrides();
  
  const [selectedTierId, setSelectedTierId] = useState<string>('');
  const [overrides, setOverrides] = useState<SubscriptionOverrides>({
    custom_max_monitors: null,
    custom_history_retention_days: null
  });
  const [isEditing, setIsEditing] = useState(false);

  // If not a superadmin, don't show this component
  if (!isSuperadmin) {
    return null;
  }

  const isLoading = tiersLoading || subscriptionLoading;
  const isSaving = updateTierMutation.isPending || updateOverridesMutation.isPending;

  const handleEdit = () => {
    if (subscription) {
      // Find the tier ID based on the tier name
      const tierObj = tiers?.find(t => t.name === subscription.tier_name);
      setSelectedTierId(tierObj?.id || '');
      
      setOverrides({
        custom_max_monitors: subscription.is_custom ? subscription.max_monitors : null,
        custom_history_retention_days: subscription.is_custom ? subscription.history_retention_days : null
      });
      
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (!companyId) return;

    try {
      // Update tier if changed
      if (selectedTierId) {
        await updateTierMutation.mutateAsync({ companyId, tierIdOrName: selectedTierId });
      }

      // Update overrides if any
      await updateOverridesMutation.mutateAsync({ companyId, overrides });

      setIsEditing(false);
      refetch();
    } catch (error) {
      console.error('Error saving subscription settings:', error);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleResetOverrides = () => {
    setOverrides({
      custom_max_monitors: null,
      custom_history_retention_days: null
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!subscription) {
    return (
      <div className="text-center p-8">
        <p>No subscription information available for this company.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Subscription Settings</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => refetch()} disabled={isLoading || isEditing}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {!isEditing && (
            <Button onClick={handleEdit} disabled={isLoading}>
              Edit Subscription
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>{companyName}</CardTitle>
              <CardDescription>Subscription settings for this company</CardDescription>
            </div>
            {subscription.is_custom && (
              <Badge variant="outline" className="ml-2">
                Custom Settings
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {isEditing ? (
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="subscription-tier">Subscription Tier</Label>
                <Select
                  value={selectedTierId}
                  onValueChange={setSelectedTierId}
                  disabled={isSaving}
                >
                  <SelectTrigger id="subscription-tier">
                    <SelectValue placeholder="Select a tier" />
                  </SelectTrigger>
                  <SelectContent>
                    {tiers?.map((tier) => (
                      <SelectItem key={tier.id} value={tier.id}>
                        {tier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="font-medium">Custom Overrides</h3>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleResetOverrides}
                    disabled={isSaving || (!overrides.custom_max_monitors && !overrides.custom_history_retention_days)}
                  >
                    Reset to Tier Defaults
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="max-monitors">Max Monitors</Label>
                    <Input
                      id="max-monitors"
                      type="number"
                      min="1"
                      value={overrides.custom_max_monitors === null ? '' : overrides.custom_max_monitors}
                      onChange={(e) => setOverrides({
                        ...overrides,
                        custom_max_monitors: e.target.value === '' ? null : parseInt(e.target.value)
                      })}
                      placeholder={`Using tier default (${subscription.max_monitors})`}
                      disabled={isSaving}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retention-days">History Retention (days)</Label>
                    <Input
                      id="retention-days"
                      type="number"
                      min="1"
                      value={overrides.custom_history_retention_days === null ? '' : overrides.custom_history_retention_days}
                      onChange={(e) => setOverrides({
                        ...overrides,
                        custom_history_retention_days: e.target.value === '' ? null : parseInt(e.target.value)
                      })}
                      placeholder={`Using tier default (${subscription.history_retention_days})`}
                      disabled={isSaving}
                    />
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h3 className="text-sm font-medium mb-1">Current Tier</h3>
                  <p className="text-2xl font-bold">{subscription.tier_name}</p>
                  <p className="text-sm text-muted-foreground mt-1">{subscription.tier_description || ''}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium mb-1">Monitor Usage</h3>
                  <p className="text-2xl font-bold">
                    {subscription.current_monitors} / {subscription.max_monitors}
                  </p>
                  <p className="text-sm text-muted-foreground mt-1">
                    {subscription.current_monitors >= subscription.max_monitors 
                      ? 'Limit reached' 
                      : `${subscription.max_monitors - subscription.current_monitors} monitors available`}
                  </p>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium mb-2">Retention Settings</h3>
                <p className="text-lg">
                  <span className="font-medium">History Retention:</span> {subscription.history_retention_days} days
                </p>
                {subscription.is_custom && (
                  <p className="text-sm text-muted-foreground mt-1">
                    This company has custom retention settings
                  </p>
                )}
              </div>
            </div>
          )}
        </CardContent>
        {isEditing && (
          <CardFooter className="flex justify-end space-x-2">
            <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
              Cancel
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Changes'
              )}
            </Button>
          </CardFooter>
        )}
      </Card>

      <div className="bg-muted/50 rounded-lg p-4">
        <h3 className="font-medium mb-2">About Company Subscriptions</h3>
        <p className="text-sm text-muted-foreground">
          Each company is assigned a subscription tier that determines their usage limits. You can override these limits for individual companies as needed.
        </p>
        <ul className="text-sm text-muted-foreground mt-2 list-disc list-inside space-y-1">
          <li><strong>Max Monitors:</strong> The maximum number of monitors this company can create</li>
          <li><strong>History Retention:</strong> How many days of monitoring history to keep for this company</li>
        </ul>
      </div>
    </div>
  );
}
