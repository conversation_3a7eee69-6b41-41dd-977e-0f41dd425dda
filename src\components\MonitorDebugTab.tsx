import React, { useState, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, AlertTriangle, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { formatTooltipDateTime } from '@/utils/dateFormat';
import { useQueryClient } from '@tanstack/react-query';
import { useMonitorStatusUpdate } from '@/hooks/use-monitor-status-update';

interface MonitorDebugTabProps {
  monitorId: string;
  monitorName: string;
  monitorUrl: string;
}

const MonitorDebugTab: React.FC<MonitorDebugTabProps> = ({ monitorId, monitorName, monitorUrl }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastCheck, setLastCheck] = useState<any>(null);
  const [rawHistory, setRawHistory] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const queryClient = useQueryClient();
  const { updateMonitorStatus } = useMonitorStatusUpdate();

  // Perform a manual check and show the raw result
  const performManualCheck = async () => {
    setIsLoading(true);
    try {
      // Get the monitor type first
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('type')
        .eq('id', monitorId)
        .single();

      if (monitorError) throw monitorError;

      // Use the monitor-checker Edge Function for consistency
      const { data, error } = await supabase.functions.invoke('monitor-checker', {
        body: { monitorId },
      });

      if (error) throw error;

      // Invalidate queries to refresh the data
      queryClient.invalidateQueries({ queryKey: ['monitors'] });
      queryClient.invalidateQueries({ queryKey: ['monitor-history-4h', monitorId] });
      queryClient.invalidateQueries({ queryKey: ['monitorHistory', 'latest'] });

      // Force a refresh of all monitor-related queries
      queryClient.refetchQueries({ queryKey: ['monitors'] });
      queryClient.refetchQueries({ queryKey: ['monitor-history-4h', monitorId] });
      queryClient.refetchQueries({ queryKey: ['monitorHistory', 'latest'] });

      // Refresh the raw history
      fetchRawHistory();

      if (data.results && data.results.length > 0) {
        const result = data.results[0];
        // Store the result for display
        setLastCheck(result);

        const status = result.status ? 'UP' : 'DOWN';

        // Update the monitor status in our store for immediate UI update
        updateMonitorStatus(
          monitorId,
          result.status ? 'up' : 'down',
          result.response_time,
          monitorData?.type,
          data.portResults // Include port results for port monitors
        );
        toast({
          title: 'Manual Check Completed',
          description: (
            <div className="space-y-1">
              <p><strong>Status:</strong> {status}</p>
              <p><strong>Response time:</strong> {result.response_time}ms</p>
              {result.error_message && (
                <p className="text-red-500"><strong>Error:</strong> {result.error_message}</p>
              )}

              {/* Show port-specific information for port monitors */}
              {monitorData?.type === 'port' && data.portResults && (
                <div className="mt-2 space-y-1">
                  <p><strong>Port Details:</strong></p>
                  <div className="max-h-32 overflow-y-auto">
                    {data.portResults.map((port) => (
                      <div key={port.port} className="flex items-center space-x-2">
                        <span className={`inline-block w-2 h-2 rounded-full ${port.status ? 'bg-green-500' : 'bg-red-500'}`}></span>
                        <span>Port {port.port}: {port.status ? 'Open' : 'Closed'}</span>
                        {port.error && <span className="text-xs text-red-500">({port.error})</span>}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ),
          variant: result.status ? 'default' : 'destructive',
        });
      } else {
        toast({
          title: 'Manual Check Completed',
          description: 'No results returned. The monitor may not exist or you may not have permission to check it.',
          variant: 'destructive',
        });
      }
    } catch (err) {
      console.error('Error performing manual check:', err);
      toast({
        title: 'Error',
        description: 'Failed to perform manual check',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch the raw history data
  const fetchRawHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitorId)
        .order('timestamp', { ascending: false })
        .limit(10);

      if (error) throw error;

      setRawHistory(data || []);
    } catch (err) {
      console.error('Error fetching raw history:', err);
      toast({
        title: 'Error',
        description: 'Failed to fetch raw history',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingHistory(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Monitor Debug Tools</CardTitle>
          <CardDescription>
            Tools for diagnosing issues with this monitor
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-4">
            <Button
              onClick={performManualCheck}
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Perform Manual Check
                </>
              )}
            </Button>
            <Button
              variant="outline"
              onClick={fetchRawHistory}
              disabled={isLoadingHistory}
              className="flex-1"
            >
              {isLoadingHistory ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Loading...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Fetch Raw History
                </>
              )}
            </Button>
          </div>

          {lastCheck && (
            <div className="mt-4 border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
              <h3 className="font-medium mb-2 flex items-center">
                {lastCheck.status ? (
                  <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500 mr-2" />
                )}
                Manual Check Result
              </h3>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Status:</span>
                  <Badge variant={lastCheck.status ? 'success' : 'destructive'}>
                    {lastCheck.status ? 'UP' : 'DOWN'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Response Time:</span>
                  <span className="font-medium">{lastCheck.response_time} ms</span>
                </div>
                {lastCheck.error_message && (
                  <div className="flex flex-col">
                    <span className="text-slate-600 dark:text-slate-400">Error Message:</span>
                    <span className="font-medium text-red-500 mt-1 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800">
                      {lastCheck.error_message}
                    </span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-slate-600 dark:text-slate-400">Timestamp:</span>
                  <span className="font-medium">{formatTooltipDateTime(new Date(lastCheck.timestamp))}</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {rawHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Raw History Data</CardTitle>
            <CardDescription>
              Last {rawHistory.length} checks from the database
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {rawHistory.map((item) => (
                <div key={item.id} className="border rounded-md p-4 bg-slate-50 dark:bg-slate-800">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      {typeof item.status === 'string' ? (
                        item.status === 'up' ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                        ) : item.status === 'degraded' ? (
                          <AlertTriangle className="h-5 w-5 text-amber-500 mr-2" />
                        ) : (
                          <XCircle className="h-5 w-5 text-red-500 mr-2" />
                        )
                      ) : item.status ? (
                        <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500 mr-2" />
                      )}
                      <Badge variant={
                        typeof item.status === 'string' ?
                          item.status === 'up' ? 'success' :
                          item.status === 'degraded' ? 'warning' :
                          'destructive'
                        : item.status ? 'success' : 'destructive'
                      }>
                        {typeof item.status === 'string' ?
                          item.status.toUpperCase() :
                          item.status ? 'UP' : 'DOWN'
                        }
                      </Badge>
                    </div>
                    <span className="text-sm text-slate-500">
                      {formatTooltipDateTime(new Date(item.timestamp))}
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-slate-500">Response Time:</span>
                      <span className="ml-2 font-medium">{item.response_time} ms</span>
                    </div>
                    {item.status_code && (
                      <div>
                        <span className="text-sm text-slate-500">Status Code:</span>
                        <span className="ml-2 font-medium">{item.status_code}</span>
                      </div>
                    )}
                  </div>
                  {item.error_message && (
                    <div className="mt-2">
                      <span className="text-sm text-slate-500">Error Message:</span>
                      <div className="mt-1 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800 text-sm font-mono">
                        {item.error_message}
                      </div>
                    </div>
                  )}
                  <div className="mt-2 text-xs text-slate-500">
                    <span>Raw Status Value: </span>
                    <code className="bg-slate-100 dark:bg-slate-700 px-1 py-0.5 rounded">
                      {JSON.stringify(item.status)}
                    </code>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MonitorDebugTab;
