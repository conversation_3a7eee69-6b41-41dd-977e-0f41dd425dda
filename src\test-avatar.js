// This is a test script to check if the avatar URL is valid
// You can run this in the browser console to test the avatar URL

// Function to test avatar URL
async function testAvatarUrl() {
  console.log('Starting avatar URL test...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('User is logged in:', user);
  console.log('User metadata:', user.user_metadata);
  
  // Check if the user has an avatar URL
  const avatarUrl = user.user_metadata?.avatar_url;
  if (!avatarUrl) {
    console.error('User does not have an avatar URL in metadata.');
    return;
  }
  
  console.log('Avatar URL:', avatarUrl);
  
  // Try to load the image
  console.log('Testing if image is accessible...');
  const img = new Image();
  
  img.onload = () => {
    console.log('Image loaded successfully!');
    console.log('Avatar URL test PASSED ✅');
    
    // Display the image in the console
    console.log('Avatar image preview:', img);
    
    // Create a div to display the image on the page
    const div = document.createElement('div');
    div.style.position = 'fixed';
    div.style.top = '10px';
    div.style.right = '10px';
    div.style.zIndex = '9999';
    div.style.background = 'white';
    div.style.padding = '10px';
    div.style.border = '1px solid black';
    div.style.borderRadius = '5px';
    div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
    
    const imgElement = document.createElement('img');
    imgElement.src = avatarUrl;
    imgElement.style.width = '100px';
    imgElement.style.height = '100px';
    imgElement.style.borderRadius = '50%';
    imgElement.style.objectFit = 'cover';
    
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.marginTop = '10px';
    closeButton.style.padding = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => document.body.removeChild(div);
    
    div.appendChild(imgElement);
    div.appendChild(document.createElement('br'));
    div.appendChild(closeButton);
    
    document.body.appendChild(div);
  };
  
  img.onerror = () => {
    console.error('Image failed to load. The URL might not be publicly accessible.');
    console.log('Avatar URL test FAILED ❌');
    
    // Check if the avatars bucket exists
    console.log('Checking if avatars bucket exists...');
    supabase.storage.listBuckets().then(({ data: buckets, error: bucketsError }) => {
      if (bucketsError) {
        console.error('Error listing buckets:', bucketsError);
        return;
      }
      
      const avatarBucket = buckets.find(b => b.name === 'avatars');
      if (!avatarBucket) {
        console.error('Avatars bucket does not exist! Please run the supabase_storage_setup.sql script.');
        return;
      }
      
      console.log('Avatars bucket exists:', avatarBucket);
      
      // Check if the file exists in the bucket
      const fileName = avatarUrl.split('/').pop().split('?')[0];
      console.log('Checking if file exists in bucket:', fileName);
      
      supabase.storage
        .from('avatars')
        .list()
        .then(({ data: files, error: filesError }) => {
          if (filesError) {
            console.error('Error listing files:', filesError);
            return;
          }
          
          console.log('Files in avatars bucket:', files);
          
          const file = files.find(f => f.name === fileName);
          if (!file) {
            console.error('File not found in avatars bucket.');
          } else {
            console.log('File found in avatars bucket:', file);
          }
        });
    });
  };
  
  img.src = avatarUrl;
  
  // Return the test results
  return {
    user,
    avatarUrl,
    testImage: img
  };
}

// Run the test
testAvatarUrl().then(results => {
  console.log('Test completed. Results:', results);
});
