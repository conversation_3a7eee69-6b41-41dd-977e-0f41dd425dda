import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import DocumentTitle from '@/components/DocumentTitle';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import {
  ArrowLeft,
  UserPlus,
  Search,
  Trash2,
  Edit,
  Shield,
  ShieldOff,
  Building,
  Plus,
  X,
  Loader2,
  User,
  UserCog,
  Users
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { useUsers, UserWithRoles, CreateUserData, UpdateUserData } from '@/hooks/use-users';
import { useCompany } from '@/contexts/CompanyContext';
import { useCompanies } from '@/hooks/use-companies';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const UserManagement = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { useGetCompaniesQuery } = useCompanies();
  const { data: companies = [] } = useGetCompaniesQuery();
  const { useGlobalSuperadminQuery, isGlobalSuperadmin } = useCompanyRoles();
  const {
    useGetUsersQuery,
    useCreateUserMutation,
    useUpdateUserMutation,
    useDeleteUserMutation,
    useAssignUserToCompanyMutation,
    useRemoveUserFromCompanyMutation,
    useSetSuperadminStatusMutation
  } = useUsers();

  // State for user management
  const [searchQuery, setSearchQuery] = useState('');
  const [showOnlySuperadmins, setShowOnlySuperadmins] = useState(false);
  const [createUserDialogOpen, setCreateUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [deleteUserDialogOpen, setDeleteUserDialogOpen] = useState(false);
  const [assignCompanyDialogOpen, setAssignCompanyDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithRoles | null>(null);

  // Form state
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPassword, setNewUserPassword] = useState('');
  const [newUserFullName, setNewUserFullName] = useState('');
  const [editUserEmail, setEditUserEmail] = useState('');
  const [editUserFullName, setEditUserFullName] = useState('');
  const [selectedCompanyId, setSelectedCompanyId] = useState('');
  const [selectedCompanyRole, setSelectedCompanyRole] = useState('member');

  // Fetch users
  const { data: users = [], isLoading, error, refetch } = useGetUsersQuery();

  // Mutations
  const createUserMutation = useCreateUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const deleteUserMutation = useDeleteUserMutation();
  const assignUserToCompanyMutation = useAssignUserToCompanyMutation();
  const removeUserFromCompanyMutation = useRemoveUserFromCompanyMutation();
  const setSuperadminStatusMutation = useSetSuperadminStatusMutation();

  // Filter users based on search query and superadmin filter
  const filteredUsers = users.filter(user => {
    // Apply search filter
    const matchesSearch =
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (user.full_name && user.full_name.toLowerCase().includes(searchQuery.toLowerCase()));

    // Apply superadmin filter if enabled
    const matchesSuperadminFilter = !showOnlySuperadmins || user.is_superadmin;

    return matchesSearch && matchesSuperadminFilter;
  });

  // Handle create user
  const handleCreateUser = async () => {
    if (!newUserEmail || !newUserPassword) {
      toast({
        title: 'Missing fields',
        description: 'Email and password are required.',
        variant: 'destructive',
      });
      return;
    }

    const userData: CreateUserData = {
      email: newUserEmail,
      password: newUserPassword,
      full_name: newUserFullName || undefined,
    };

    try {
      await createUserMutation.mutateAsync(userData);
      setCreateUserDialogOpen(false);
      resetCreateUserForm();
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  // Handle edit user
  const handleEditUser = async () => {
    if (!selectedUser) return;

    const userData: UpdateUserData = {};
    if (editUserEmail && editUserEmail !== selectedUser.email) {
      userData.email = editUserEmail;
    }
    if (editUserFullName !== selectedUser.full_name) {
      userData.full_name = editUserFullName;
    }

    if (Object.keys(userData).length === 0) {
      setEditUserDialogOpen(false);
      return;
    }

    try {
      await updateUserMutation.mutateAsync({
        userId: selectedUser.id,
        data: userData,
      });
      setEditUserDialogOpen(false);
    } catch (error) {
      console.error('Error updating user:', error);
    }
  };

  // Handle delete user
  const handleDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      await deleteUserMutation.mutateAsync(selectedUser.id);
      setDeleteUserDialogOpen(false);
      setSelectedUser(null);
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  };

  // Handle assign user to company
  const handleAssignUserToCompany = async () => {
    if (!selectedUser || !selectedCompanyId || !selectedCompanyRole) return;

    try {
      await assignUserToCompanyMutation.mutateAsync({
        userId: selectedUser.id,
        companyId: selectedCompanyId,
        role: selectedCompanyRole,
      });
      setAssignCompanyDialogOpen(false);
      setSelectedCompanyId('');
      setSelectedCompanyRole('member');
    } catch (error) {
      console.error('Error assigning user to company:', error);
    }
  };

  // Handle remove user from company
  const handleRemoveUserFromCompany = async (userId: string, companyId: string) => {
    try {
      await removeUserFromCompanyMutation.mutateAsync({
        userId,
        companyId,
      });
    } catch (error) {
      console.error('Error removing user from company:', error);
    }
  };

  // Handle toggle superadmin status
  const handleToggleSuperadminStatus = async (userId: string, isSuperadmin: boolean) => {
    try {
      await setSuperadminStatusMutation.mutateAsync({
        userId,
        isSuperadmin: !isSuperadmin,
      });
    } catch (error) {
      console.error('Error toggling superadmin status:', error);
    }
  };

  // Reset create user form
  const resetCreateUserForm = () => {
    setNewUserEmail('');
    setNewUserPassword('');
    setNewUserFullName('');
  };

  // Set edit user form values
  const setEditUserFormValues = (user: UserWithRoles) => {
    setSelectedUser(user);
    setEditUserEmail(user.email);
    setEditUserFullName(user.full_name || '');
    setEditUserDialogOpen(true);
  };

  // Get user initials for avatar fallback
  const getUserInitials = (name: string, email: string) => {
    if (name) {
      const nameParts = name.split(' ');
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return name[0].toUpperCase();
    }

    return email ? email[0].toUpperCase() : 'U';
  };

  // Check if user is the current logged-in user
  const isCurrentUser = (userId: string) => {
    return user?.id === userId;
  };

  // Get available companies for assignment (companies the user is not already a member of)
  const getAvailableCompanies = (user: UserWithRoles) => {
    const userCompanyIds = user.companies.map(c => c.company_id);
    return companies.filter(company => !userCompanyIds.includes(company.id));
  };

  // We don't need this check anymore since we're using SuperadminRoute in App.tsx
  // The component will only be rendered if the user is a superadmin

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="container mx-auto py-8 px-4">
          <h1 className="text-3xl font-bold mb-6">User Management</h1>

          <Card>
            <CardHeader>
              <CardTitle className="text-red-500">Error</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Failed to load users. This might be due to permission issues.</p>
              <p className="text-sm text-slate-500 mt-2">
                {error instanceof Error ? error.message : 'Unknown error'}
              </p>
              <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                <p className="text-amber-800 text-sm">
                  <strong>Troubleshooting:</strong> If you're seeing a permission error, it might be because:
                </p>
                <ul className="list-disc ml-5 mt-2 text-sm text-amber-700">
                  <li>Your superadmin role is not properly set up</li>
                  <li>There's an issue with the database functions</li>
                  <li>The RLS policies need to be updated</li>
                </ul>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={() => refetch()} className="mr-2">
                <Loader2 className="h-4 w-4 mr-2" />
                Retry
              </Button>
              <Button variant="outline" onClick={() => navigate('/dashboard')}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </CardFooter>
          </Card>
        </div>
      </AppLayout>
    );
  }

  const header = (
    <UnifiedHeader
      title="User Management"
      icon={Users}
      description="Manage users, their company memberships, and permissions"
      actions={
        <Button onClick={() => setCreateUserDialogOpen(true)}>
          <UserPlus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      }
    />
  );

  return (
    <>
      <DocumentTitle title="User Management" />
      <AppLayout header={header}>

        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-6xl mx-auto">

          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-4">
              <div className="relative w-full max-w-sm">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-500" />
                <Input
                  placeholder="Search users..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Only show this checkbox to superadmins */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="superadmin-filter"
                  checked={showOnlySuperadmins}
                  onCheckedChange={(checked) => setShowOnlySuperadmins(checked === true)}
                />
                <label
                  htmlFor="superadmin-filter"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
                >
                  <Shield className="h-3.5 w-3.5 mr-1 text-red-500" />
                  Show only superadmins
                </label>
              </div>
            </div>
            <Dialog open={createUserDialogOpen} onOpenChange={setCreateUserDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                  <DialogDescription>
                    Add a new user to the system. They will be able to log in with these credentials.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUserEmail}
                      onChange={(e) => setNewUserEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUserPassword}
                      onChange={(e) => setNewUserPassword(e.target.value)}
                      placeholder="••••••••"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fullName">Full Name (Optional)</Label>
                    <Input
                      id="fullName"
                      value={newUserFullName}
                      onChange={(e) => setNewUserFullName(e.target.value)}
                      placeholder="John Doe"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setCreateUserDialogOpen(false);
                      resetCreateUserForm();
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateUser}
                    disabled={createUserMutation.isPending || !newUserEmail || !newUserPassword}
                  >
                    {createUserMutation.isPending && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    Create User
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>
                Manage users, their company memberships, and permissions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredUsers.length === 0 ? (
                <div className="text-center py-8 text-slate-500">
                  {searchQuery ? 'No users match your search' : 'No users found'}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>User</TableHead>
                        <TableHead>Companies</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8">
                                {user.avatar_url ? (
                                  <AvatarImage src={user.avatar_url} alt={user.full_name || user.email} />
                                ) : null}
                                <AvatarFallback>
                                  {getUserInitials(user.full_name || '', user.email)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                {user.full_name && <div className="font-medium">{user.full_name}</div>}
                                <div className="text-sm text-slate-500">{user.email}</div>
                                {isCurrentUser(user.id) && (
                                  <Badge variant="outline" className="mt-1 text-xs">You</Badge>
                                )}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {user.companies.length === 0 ? (
                              <span className="text-slate-500 text-sm">No companies</span>
                            ) : (
                              <div className="flex flex-wrap gap-1">
                                {user.companies.map((company) => (
                                  <Badge
                                    key={company.company_id}
                                    variant="outline"
                                    className="flex items-center gap-1"
                                  >
                                    {company.role_type === 'admin' ? (
                                      <UserCog className="h-3 w-3 mr-1" />
                                    ) : (
                                      <User className="h-3 w-3 mr-1" />
                                    )}
                                    {company.company_name}
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                                      onClick={() => handleRemoveUserFromCompany(user.id, company.company_id)}
                                      title="Remove from company"
                                    >
                                      <X className="h-3 w-3" />
                                    </Button>
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            {user.is_superadmin ? (
                              <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                Superadmin
                              </Badge>
                            ) : (
                              <span className="text-slate-500 text-sm">Regular User</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedUser(user);
                                  setAssignCompanyDialogOpen(true);
                                }}
                                title="Assign to company"
                              >
                                <Building className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setEditUserFormValues(user)}
                                title="Edit user"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleToggleSuperadminStatus(user.id, user.is_superadmin)}
                                title={user.is_superadmin ? "Revoke superadmin" : "Grant superadmin"}
                                disabled={isCurrentUser(user.id)}
                              >
                                {user.is_superadmin ? (
                                  <ShieldOff className="h-4 w-4 text-red-500" />
                                ) : (
                                  <Shield className="h-4 w-4" />
                                )}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setSelectedUser(user);
                                  setDeleteUserDialogOpen(true);
                                }}
                                disabled={isCurrentUser(user.id)}
                                title="Delete user"
                              >
                                <Trash2 className="h-4 w-4 text-red-500" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        </main>

        {/* Edit User Dialog */}
        <Dialog open={editUserDialogOpen} onOpenChange={setEditUserDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user information
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="editEmail">Email</Label>
            <Input
              id="editEmail"
              type="email"
              value={editUserEmail}
              onChange={(e) => setEditUserEmail(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="editFullName">Full Name</Label>
            <Input
              id="editFullName"
              value={editUserFullName}
              onChange={(e) => setEditUserFullName(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setEditUserDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleEditUser}
            disabled={updateUserMutation.isPending}
          >
            {updateUserMutation.isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Delete User Confirmation Dialog */}
    <AlertDialog open={deleteUserDialogOpen} onOpenChange={setDeleteUserDialogOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the user
            account and remove their data from our servers.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDeleteUser}
            className="bg-red-500 hover:bg-red-600"
          >
            {deleteUserMutation.isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            Delete
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>

    {/* Assign Company Dialog */}
    <Dialog open={assignCompanyDialogOpen} onOpenChange={setAssignCompanyDialogOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign to Company</DialogTitle>
          <DialogDescription>
            Add this user to a company and set their role
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {selectedUser && getAvailableCompanies(selectedUser).length === 0 ? (
            <p className="text-center text-slate-500">
              This user is already a member of all available companies.
            </p>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="companySelect">Company</Label>
                <Select
                  value={selectedCompanyId}
                  onValueChange={setSelectedCompanyId}
                >
                  <SelectTrigger id="companySelect">
                    <SelectValue placeholder="Select a company" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedUser && getAvailableCompanies(selectedUser).map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="roleSelect">Role</Label>
                <Select
                  value={selectedCompanyRole}
                  onValueChange={setSelectedCompanyRole}
                >
                  <SelectTrigger id="roleSelect">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => setAssignCompanyDialogOpen(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAssignUserToCompany}
            disabled={
              assignUserToCompanyMutation.isPending ||
              !selectedCompanyId ||
              !selectedCompanyRole ||
              (selectedUser && getAvailableCompanies(selectedUser).length === 0)
            }
          >
            {assignUserToCompanyMutation.isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            Assign
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default UserManagement;
