import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Loader2, UserPlus, Users } from 'lucide-react';
import DocumentTitle from '@/components/DocumentTitle';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';

// Custom hooks
import { useUserManagement } from './hooks/useUserManagement';

// Components
import UserSearch from './components/UserSearch';
import UserTable from './components/UserTable';
import CreateUserDialog from './components/CreateUserDialog';
import EditUserDialog from './components/EditUserDialog';
import DeleteUserDialog from './components/DeleteUserDialog';
import AssignCompanyDialog from './components/AssignCompanyDialog';
import ErrorDisplay from './components/ErrorDisplay';

const UserManagement = () => {
  const {
    // State
    user,
    filteredUsers,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
    showOnly<PERSON><PERSON><PERSON><PERSON>,
    setShowOnly<PERSON><PERSON>radmins,
    createUserDialogOpen,
    setCreateUserDialogOpen,
    editUserDialogOpen,
    setEditUserDialogOpen,
    deleteUserDialogOpen,
    setDeleteUserDialogOpen,
    assignCompanyDialogOpen,
    setAssignCompanyDialogOpen,
    selectedUser,
    setSelectedUser,
    newUserEmail,
    setNewUserEmail,
    newUserPassword,
    setNewUserPassword,
    newUserFullName,
    setNewUserFullName,
    editUserEmail,
    setEditUserEmail,
    editUserFullName,
    setEditUserFullName,
    selectedCompanyId,
    setSelectedCompanyId,
    selectedCompanyRole,
    setSelectedCompanyRole,

    // Mutations
    createUserMutation,
    updateUserMutation,
    deleteUserMutation,
    assignUserToCompanyMutation,

    // Functions
    handleCreateUser,
    handleEditUser,
    handleDeleteUser,
    handleAssignUserToCompany,
    handleRemoveUserFromCompany,
    handleToggleSuperadminStatus,
    resetCreateUserForm,
    setEditUserFormValues,
    getAvailableCompanies,
    refetch
  } = useUserManagement();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
      </div>
    );
  }

  if (error) {
    const errorHeader = (
      <UnifiedHeader
        title="User Management Error"
        icon={Users}
      />
    );

    return (
      <AppLayout header={errorHeader}>
        <ErrorDisplay error={error} refetch={refetch} />
      </AppLayout>
    );
  }

  const header = (
    <UnifiedHeader
      title="User Management"
      icon={Users}
      description="Manage users, their company memberships, and permissions"
      actions={
        <Button onClick={() => setCreateUserDialogOpen(true)}>
          <UserPlus className="h-4 w-4 mr-2" />
          Add User
        </Button>
      }
    />
  );

  return (
    <>
      <DocumentTitle title="User Management" />
      <AppLayout header={header}>
        <main className="flex-1 overflow-y-auto p-6">
          <div className="max-w-6xl mx-auto">
            <div className="flex justify-between items-center mb-6">
              <UserSearch
                searchQuery={searchQuery}
                setSearchQuery={setSearchQuery}
                showOnlySuperadmins={showOnlySuperadmins}
                setShowOnlySuperadmins={setShowOnlySuperadmins}
              />

              <CreateUserDialog
                open={createUserDialogOpen}
                onOpenChange={setCreateUserDialogOpen}
                newUserEmail={newUserEmail}
                setNewUserEmail={setNewUserEmail}
                newUserPassword={newUserPassword}
                setNewUserPassword={setNewUserPassword}
                newUserFullName={newUserFullName}
                setNewUserFullName={setNewUserFullName}
                handleCreateUser={handleCreateUser}
                resetCreateUserForm={resetCreateUserForm}
                isPending={createUserMutation.isPending}
              />
            </div>

            <UserTable
              users={filteredUsers}
              searchQuery={searchQuery}
              currentUserId={user?.id}
              setEditUserFormValues={setEditUserFormValues}
              handleToggleSuperadminStatus={handleToggleSuperadminStatus}
              handleRemoveUserFromCompany={handleRemoveUserFromCompany}
              setSelectedUser={setSelectedUser}
              setDeleteUserDialogOpen={setDeleteUserDialogOpen}
              setAssignCompanyDialogOpen={setAssignCompanyDialogOpen}
            />
          </div>
        </main>
      </AppLayout>

      {/* Dialogs */}
      <EditUserDialog
        open={editUserDialogOpen}
        onOpenChange={setEditUserDialogOpen}
        editUserEmail={editUserEmail}
        setEditUserEmail={setEditUserEmail}
        editUserFullName={editUserFullName}
        setEditUserFullName={setEditUserFullName}
        handleEditUser={handleEditUser}
        isPending={updateUserMutation.isPending}
      />

      <DeleteUserDialog
        open={deleteUserDialogOpen}
        onOpenChange={setDeleteUserDialogOpen}
        handleDeleteUser={handleDeleteUser}
        isPending={deleteUserMutation.isPending}
      />

      <AssignCompanyDialog
        open={assignCompanyDialogOpen}
        onOpenChange={setAssignCompanyDialogOpen}
        selectedUser={selectedUser}
        selectedCompanyId={selectedCompanyId}
        setSelectedCompanyId={setSelectedCompanyId}
        selectedCompanyRole={selectedCompanyRole}
        setSelectedCompanyRole={setSelectedCompanyRole}
        handleAssignUserToCompany={handleAssignUserToCompany}
        getAvailableCompanies={getAvailableCompanies}
        isPending={assignUserToCompanyMutation.isPending}
      />
    </>
  );
};

export default UserManagement;
