import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, Plus, Trash2, AlertTriangle } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { SubscriptionTier } from '@/types/subscription';
import { updateSubscriptionTier } from '@/services/subscription-service';
import { TierFeatureEditor } from './TierFeatureEditor';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export function SubscriptionTierSettings() {
  const {
    useSubscriptionTiers,
    useCreateSubscriptionTier,
    useDeleteSubscriptionTier,
    isSuperadmin
  } = useSubscription();

  const { data: tiers, isLoading, refetch } = useSubscriptionTiers();
  const createTierMutation = useCreateSubscriptionTier();
  const deleteTierMutation = useDeleteSubscriptionTier();

  const [editingTier, setEditingTier] = useState<SubscriptionTier | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [tierToDelete, setTierToDelete] = useState<SubscriptionTier | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newTier, setNewTier] = useState<{
    name: string;
    description: string;
    max_monitors: number;
    history_retention_days: number;
  }>({
    name: '',
    description: '',
    max_monitors: 1,
    history_retention_days: 1
  });

  // If not a superadmin, don't show this component
  if (!isSuperadmin) {
    return null;
  }

  const handleEdit = (tier: SubscriptionTier) => {
    setEditingTier({ ...tier });
  };

  const handleSave = async () => {
    if (!editingTier) return;

    setIsSaving(true);
    try {
      await updateSubscriptionTier(editingTier.id, {
        name: editingTier.name,
        description: editingTier.description,
        max_monitors: editingTier.max_monitors,
        history_retention_days: editingTier.history_retention_days
      });

      toast({
        title: 'Tier Updated',
        description: `The ${editingTier.name} tier has been updated successfully.`,
      });

      refetch();
      setEditingTier(null);
    } catch (error) {
      console.error('Error updating tier:', error);
      toast({
        title: 'Error',
        description: 'Failed to update the subscription tier. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingTier(null);
  };

  const handleInputChange = (field: keyof SubscriptionTier, value: string | number) => {
    if (!editingTier) return;

    setEditingTier({
      ...editingTier,
      [field]: value
    });
  };

  const handleNewTierInputChange = (field: string, value: string | number) => {
    setNewTier({
      ...newTier,
      [field]: value
    });
  };

  const handleCreateTier = async () => {
    setIsSaving(true);
    try {
      await createTierMutation.mutateAsync({
        name: newTier.name,
        description: newTier.description,
        max_monitors: newTier.max_monitors,
        history_retention_days: newTier.history_retention_days
      });

      // Reset form and close dialog
      setNewTier({
        name: '',
        description: '',
        max_monitors: 1,
        history_retention_days: 1
      });
      setShowCreateDialog(false);
      refetch();
    } catch (error) {
      console.error('Error creating tier:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteClick = (tier: SubscriptionTier) => {
    setTierToDelete(tier);
    setShowDeleteAlert(true);
  };

  const handleConfirmDelete = async () => {
    if (!tierToDelete) return;

    try {
      await deleteTierMutation.mutateAsync(tierToDelete.id);
      setShowDeleteAlert(false);
      setTierToDelete(null);
      refetch();
    } catch (error) {
      console.error('Error deleting tier:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Subscription Tiers</h2>
        <div className="flex space-x-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Tier
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Subscription Tier</DialogTitle>
                <DialogDescription>
                  Add a new subscription tier with custom limits and features.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-tier-name">Name</Label>
                    <Input
                      id="new-tier-name"
                      value={newTier.name}
                      onChange={(e) => handleNewTierInputChange('name', e.target.value)}
                      placeholder="e.g., Business"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-tier-description">Description</Label>
                    <Input
                      id="new-tier-description"
                      value={newTier.description}
                      onChange={(e) => handleNewTierInputChange('description', e.target.value)}
                      placeholder="e.g., For small businesses"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-tier-max-monitors">Max Monitors</Label>
                    <Input
                      id="new-tier-max-monitors"
                      type="number"
                      min="1"
                      value={newTier.max_monitors}
                      onChange={(e) => handleNewTierInputChange('max_monitors', parseInt(e.target.value))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-tier-retention-days">History Retention (days)</Label>
                    <Input
                      id="new-tier-retention-days"
                      type="number"
                      min="1"
                      value={newTier.history_retention_days}
                      onChange={(e) => handleNewTierInputChange('history_retention_days', parseInt(e.target.value))}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTier} disabled={isSaving || !newTier.name}>
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Tier'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-6">
        {tiers?.map((tier) => (
          <Card key={tier.id}>
            <CardHeader>
              <CardTitle>{tier.name}</CardTitle>
              <CardDescription>{tier.description || 'No description'}</CardDescription>
            </CardHeader>
            <CardContent>
              {editingTier && editingTier.id === tier.id ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`name-${tier.id}`}>Name</Label>
                      <Input
                        id={`name-${tier.id}`}
                        value={editingTier.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`description-${tier.id}`}>Description</Label>
                      <Input
                        id={`description-${tier.id}`}
                        value={editingTier.description || ''}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`max-monitors-${tier.id}`}>Max Monitors</Label>
                      <Input
                        id={`max-monitors-${tier.id}`}
                        type="number"
                        min="1"
                        value={editingTier.max_monitors}
                        onChange={(e) => handleInputChange('max_monitors', parseInt(e.target.value))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`retention-days-${tier.id}`}>History Retention (days)</Label>
                      <Input
                        id={`retention-days-${tier.id}`}
                        type="number"
                        min="1"
                        value={editingTier.history_retention_days}
                        onChange={(e) => handleInputChange('history_retention_days', parseInt(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Max Monitors</p>
                      <p className="text-2xl font-bold">{tier.max_monitors}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">History Retention</p>
                      <p className="text-2xl font-bold">{tier.history_retention_days} days</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                {!editingTier && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteClick(tier)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
              <div className="space-x-2">
                {editingTier && editingTier.id === tier.id ? (
                  <>
                    <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave} disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </>
                ) : (
                  <Button variant="outline" onClick={() => handleEdit(tier)}>
                    Edit
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {tiers?.map((tier) => (
        <div key={`features-${tier.id}`} className="mt-8">
          <TierFeatureEditor tier={tier} />
        </div>
      ))}

      <div className="bg-muted/50 rounded-lg p-4">
        <h3 className="font-medium mb-2">About Subscription Tiers</h3>
        <p className="text-sm text-muted-foreground">
          These settings define the default limits for each subscription tier. You can override these limits for individual companies as needed.
        </p>
        <ul className="text-sm text-muted-foreground mt-2 list-disc list-inside space-y-1">
          <li><strong>Max Monitors:</strong> The maximum number of monitors a company can create</li>
          <li><strong>History Retention:</strong> How many days of monitoring history to keep</li>
          <li><strong>Other Features:</strong> The feature comparison table above shows the default features for each tier</li>
        </ul>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" /> Delete Subscription Tier
            </AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4">
                Are you sure you want to delete the <strong>{tierToDelete?.name}</strong> tier?
              </p>
              <p className="mb-2">
                This action cannot be undone. Make sure no companies are using this tier before deleting it.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteAlert(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
