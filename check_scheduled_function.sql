-- Check if the pg_cron extension is enabled
SELECT * FROM pg_extension WHERE extname = 'pg_cron';

-- Check if the scheduled job exists
SELECT * FROM cron.job WHERE jobname = 'check-monitors-every-minute';

-- Check the job run history
SELECT * FROM cron.job_run_details 
WHERE jobid = (SELECT jobid FROM cron.job WHERE jobname = 'check-monitors-every-minute')
ORDER BY start_time DESC
LIMIT 10;

-- Check the monitor_history table for recent checks
SELECT 
  m.name AS monitor_name, 
  m.interval AS configured_interval_minutes,
  MAX(mh.timestamp) AS last_check_time,
  EXTRACT(EPOCH FROM (NOW() - MAX(mh.timestamp)))/60 AS minutes_since_last_check
FROM 
  monitors m
LEFT JOIN 
  monitor_history mh ON m.id = mh.monitor_id
WHERE 
  m.active = true
GROUP BY 
  m.id, m.name, m.interval
ORDER BY 
  minutes_since_last_check DESC;
