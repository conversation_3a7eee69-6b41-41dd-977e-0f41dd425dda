import React from 'react';
import { LucideIcon, PanelLeft, PanelLeftClose } from 'lucide-react';
import Vum<PERSON><PERSON> from './VumLogo';
import CompanySelector from './CompanySelector';
import SystemHealthStatus from './SystemHealthStatus';
import { useSidebar } from '@/contexts/SidebarContext';
import { useCompany } from '@/contexts/CompanyContext';
import { Button } from './ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface UnifiedHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  actions?: React.ReactNode;
  showCompanySelector?: boolean;
}

const UnifiedHeader: React.FC<UnifiedHeaderProps> = ({
  title,
  description,
  icon: Icon,
  actions,
  showCompanySelector = false
}) => {
  const { sidebarVisible, toggleSidebar } = useSidebar();
  const { isSuperadmin } = useCompany();

  // Debug log
  console.log('UnifiedHeader - isSuperadmin:', isSuperadmin);

  // Debug log
  console.log('UnifiedHeader - isSuperadmin:', isSuperadmin);
  return (
    <header className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 py-4 px-6 sticky top-0 z-10 flex items-center">
      {/* Left side - Logo with sidebar toggle */}
      <div className={`flex-shrink-0 mr-4 border-r border-slate-200 dark:border-slate-700 pr-4 flex items-center transition-all duration-300 ${sidebarVisible ? 'w-64' : 'w-auto'}`}>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleSidebar}
                className="mr-2 hover:bg-slate-100 dark:hover:bg-slate-700"
              >
                {sidebarVisible ?
                  <PanelLeftClose className="h-5 w-5 text-slate-500" /> :
                  <PanelLeft className="h-5 w-5 text-slate-500" />
                }
              </Button>
            </TooltipTrigger>
            <TooltipContent side="bottom">
              <p>{sidebarVisible ? 'Hide sidebar' : 'Show sidebar'}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
        <VumLogo linkTo="/dashboard" />
      </div>

      {/* Right side - Page header content */}
      <div className="flex-1 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          {Icon && <Icon className="h-6 w-6 mr-2 text-blue-600" />}
          <h1 className="text-2xl font-bold text-blue-600">{title}</h1>
          {showCompanySelector && <CompanySelector className="ml-4" />}
          {description && (
            <p className="text-sm text-slate-500 dark:text-slate-400 ml-4">{description}</p>
          )}
        </div>
        <div className="flex items-center space-x-4">
          {/* System Health Status - only show for superadmins */}
          {isSuperadmin && (
            <div className="mr-4">
              <SystemHealthStatus compact={true} showDetails={true} />
            </div>
          )}
          {actions}
        </div>
      </div>
    </header>
  );
};

export default UnifiedHeader;
