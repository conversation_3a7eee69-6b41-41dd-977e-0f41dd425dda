-- This script adds sample data to your Supabase database for testing
-- Run this in the Supabase SQL Editor after creating the tables

-- First, let's add some sample monitor history data
-- Note: Replace the monitor_id values with actual monitor IDs from your database

-- Sample monitor history for a monitor (replace 'your-monitor-id' with an actual monitor ID)
INSERT INTO public.monitor_history (monitor_id, status, response_time, error_message, timestamp)
VALUES 
  ('your-monitor-id', true, 156, NULL, NOW() - INTERVAL '10 minutes'),
  ('your-monitor-id', true, 142, NULL, NOW() - INTERVAL '20 minutes'),
  ('your-monitor-id', true, 189, NULL, NOW() - INTERVAL '30 minutes'),
  ('your-monitor-id', false, 3240, 'Connection timeout', NOW() - INTERVAL '40 minutes'),
  ('your-monitor-id', true, 165, NULL, NOW() - INTERVAL '50 minutes'),
  ('your-monitor-id', true, 178, NULL, NOW() - INTERVAL '60 minutes');

-- To add this data, you'll need to:
-- 1. Create a monitor first through the UI
-- 2. Get the monitor ID from the database
-- 3. Replace 'your-monitor-id' with the actual monitor ID
-- 4. Run this script in the Supabase SQL Editor
