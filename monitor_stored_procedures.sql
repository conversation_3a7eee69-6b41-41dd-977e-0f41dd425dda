-- This script creates stored procedures for handling monitors with multiple companies
-- Run this in the Supabase SQL Editor

-- Make sure the uuid-ossp extension is available for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add role enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'admin', 'superadmin');
    END IF;
END$$;

-- Add deleted column to monitors table if it doesn't exist
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted BOOLEAN DEFAULT FALSE;
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted_by UUID;

-- Add role column to company_members table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_schema = 'public'
                   AND table_name = 'company_members'
                   AND column_name = 'role_type') THEN
        ALTER TABLE public.company_members ADD COLUMN role_type user_role DEFAULT 'user';
    END IF;
END$$;

-- Create a function to create a monitor and assign it to multiple companies
CREATE OR REPLACE FUNCTION create_monitor_with_companies(
  monitor_name TEXT,
  monitor_target TEXT,
  monitor_type TEXT,
  monitor_interval INTEGER DEFAULT 300,
  monitor_timeout INTEGER DEFAULT 30,
  monitor_active BOOLEAN DEFAULT TRUE,
  user_id UUID DEFAULT NULL,
  company_ids UUID[] DEFAULT '{}'::UUID[]
) RETURNS JSONB AS $$
DECLARE
  new_monitor_id UUID;
  company_id UUID;
  result JSONB;
BEGIN
  -- Create the monitor
  INSERT INTO monitors (
    name,
    target,
    type,
    interval,
    timeout,
    active,
    user_id
  ) VALUES (
    monitor_name,
    monitor_target,
    monitor_type,
    monitor_interval,
    monitor_timeout,
    monitor_active,
    user_id
  ) RETURNING id INTO new_monitor_id;

  -- Create monitor-company relationships if company_ids is not empty
  IF company_ids IS NOT NULL AND array_length(company_ids, 1) > 0 THEN
    FOREACH company_id IN ARRAY company_ids
    LOOP
      INSERT INTO monitor_companies (monitor_id, company_id)
      VALUES (new_monitor_id, company_id);
    END LOOP;
  END IF;

  -- Get the created monitor with its companies
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'target', m.target,
      'type', m.type,
      'interval', m.interval,
      'timeout', m.timeout,
      'active', m.active,
      'user_id', m.user_id,
      'created_at', m.created_at,
      'companies', (
        SELECT jsonb_agg(
          jsonb_build_object(
            'id', mc.id,
            'monitor_id', mc.monitor_id,
            'company_id', mc.company_id,
            'created_at', mc.created_at
          )
        )
        FROM monitor_companies mc
        WHERE mc.monitor_id = m.id
      )
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = new_monitor_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update a monitor and its company assignments
CREATE OR REPLACE FUNCTION update_monitor_with_companies(
  monitor_id UUID,
  monitor_name TEXT DEFAULT NULL,
  monitor_target TEXT DEFAULT NULL,
  monitor_type TEXT DEFAULT NULL,
  monitor_interval INTEGER DEFAULT NULL,
  monitor_timeout INTEGER DEFAULT NULL,
  monitor_active BOOLEAN DEFAULT NULL,
  company_ids UUID[] DEFAULT '{}'::UUID[]
) RETURNS JSONB AS $$
DECLARE
  company_id UUID;
  result JSONB;
  monitor_exists BOOLEAN;
BEGIN
  -- Check if the monitor exists
  SELECT EXISTS(SELECT 1 FROM monitors WHERE id = monitor_id) INTO monitor_exists;

  IF NOT monitor_exists THEN
    RAISE EXCEPTION 'Monitor with ID % does not exist', monitor_id;
  END IF;

  -- Update the monitor
  UPDATE monitors
  SET
    name = COALESCE(monitor_name, name),
    target = COALESCE(monitor_target, target),
    type = COALESCE(monitor_type, type),
    interval = COALESCE(monitor_interval, interval),
    timeout = COALESCE(monitor_timeout, timeout),
    active = COALESCE(monitor_active, active)
  WHERE
    id = monitor_id;

  -- If company_ids is provided, update the monitor-company relationships
  IF company_ids IS NOT NULL THEN
    -- Use a transaction to ensure atomicity
    BEGIN
      -- Delete existing relationships
      DELETE FROM monitor_companies
      WHERE monitor_id = monitor_id;

      -- Create new relationships if company_ids is not empty
      IF array_length(company_ids, 1) > 0 THEN
        -- Insert all company relationships at once
        INSERT INTO monitor_companies (monitor_id, company_id)
        SELECT monitor_id, unnest(company_ids);
      END IF;
    EXCEPTION WHEN OTHERS THEN
      -- Log the error and re-raise
      RAISE NOTICE 'Error updating monitor company relationships: %', SQLERRM;
      RAISE;
    END;
  END IF;

  -- Get the updated monitor with its companies
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'target', m.target,
      'type', m.type,
      'interval', m.interval,
      'timeout', m.timeout,
      'active', m.active,
      'user_id', m.user_id,
      'created_at', m.created_at,
      'companies', COALESCE(
        (
          SELECT jsonb_agg(
            jsonb_build_object(
              'id', mc.id,
              'monitor_id', mc.monitor_id,
              'company_id', mc.company_id,
              'created_at', mc.created_at
            )
          )
          FROM monitor_companies mc
          WHERE mc.monitor_id = m.id
        ),
        '[]'::jsonb
      )
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = monitor_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to update monitor company associations only
CREATE OR REPLACE FUNCTION update_monitor_companies(
  monitor_id UUID,
  company_ids UUID[] DEFAULT '{}'::UUID[]
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  monitor_exists BOOLEAN;
BEGIN
  -- Check if the monitor exists
  SELECT EXISTS(SELECT 1 FROM monitors WHERE id = monitor_id) INTO monitor_exists;

  IF NOT monitor_exists THEN
    RAISE EXCEPTION 'Monitor with ID % does not exist', monitor_id;
  END IF;

  -- Use a transaction to ensure atomicity
  BEGIN
    -- Delete existing relationships
    DELETE FROM monitor_companies
    WHERE monitor_id = monitor_id;

    -- Create new relationships if company_ids is not empty
    IF company_ids IS NOT NULL AND array_length(company_ids, 1) > 0 THEN
      -- Insert all company relationships at once
      INSERT INTO monitor_companies (monitor_id, company_id)
      SELECT monitor_id, unnest(company_ids);
    END IF;
  EXCEPTION WHEN OTHERS THEN
    -- Log the error and re-raise
    RAISE NOTICE 'Error updating monitor company relationships: %', SQLERRM;
    RAISE;
  END;

  -- Get the updated monitor with its companies
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'companies', COALESCE(
        (
          SELECT jsonb_agg(
            jsonb_build_object(
              'id', mc.id,
              'monitor_id', mc.monitor_id,
              'company_id', mc.company_id,
              'created_at', mc.created_at
            )
          )
          FROM monitor_companies mc
          WHERE mc.monitor_id = m.id
        ),
        '[]'::jsonb
      )
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = monitor_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to soft delete a monitor
CREATE OR REPLACE FUNCTION soft_delete_monitor(
  monitor_id UUID,
  user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  user_role user_role;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the user has admin or superadmin role in any company associated with the monitor
  SELECT
    CASE
      WHEN EXISTS (
        SELECT 1 FROM company_members cm
        JOIN monitor_companies mc ON cm.company_id = mc.company_id
        WHERE mc.monitor_id = monitor_id
        AND cm.user_id = user_id
        AND cm.role_type = 'superadmin'
      ) THEN TRUE
      ELSE FALSE
    END INTO is_superadmin;

  SELECT
    CASE
      WHEN EXISTS (
        SELECT 1 FROM company_members cm
        JOIN monitor_companies mc ON cm.company_id = mc.company_id
        WHERE mc.monitor_id = monitor_id
        AND cm.user_id = user_id
        AND (cm.role_type = 'admin' OR cm.role_type = 'superadmin')
      ) THEN TRUE
      ELSE FALSE
    END INTO is_admin;

  -- Only allow admins and superadmins to soft delete monitors
  IF NOT (is_admin OR is_superadmin) THEN
    RAISE EXCEPTION 'User does not have permission to delete this monitor';
  END IF;

  -- Soft delete the monitor
  UPDATE monitors
  SET
    deleted = TRUE,
    deleted_at = NOW(),
    deleted_by = user_id
  WHERE
    id = monitor_id
    AND deleted = FALSE;

  -- Get the updated monitor
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'deleted', m.deleted,
      'deleted_at', m.deleted_at,
      'deleted_by', m.deleted_by
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = monitor_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to restore a soft-deleted monitor
CREATE OR REPLACE FUNCTION restore_monitor(
  monitor_id UUID,
  user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the user has admin or superadmin role in any company associated with the monitor
  SELECT
    CASE
      WHEN EXISTS (
        SELECT 1 FROM company_members cm
        JOIN monitor_companies mc ON cm.company_id = mc.company_id
        WHERE mc.monitor_id = monitor_id
        AND cm.user_id = user_id
        AND cm.role_type = 'superadmin'
      ) THEN TRUE
      ELSE FALSE
    END INTO is_superadmin;

  SELECT
    CASE
      WHEN EXISTS (
        SELECT 1 FROM company_members cm
        JOIN monitor_companies mc ON cm.company_id = mc.company_id
        WHERE mc.monitor_id = monitor_id
        AND cm.user_id = user_id
        AND (cm.role_type = 'admin' OR cm.role_type = 'superadmin')
      ) THEN TRUE
      ELSE FALSE
    END INTO is_admin;

  -- Only allow admins and superadmins to restore monitors
  IF NOT (is_admin OR is_superadmin) THEN
    RAISE EXCEPTION 'User does not have permission to restore this monitor';
  END IF;

  -- Restore the monitor
  UPDATE monitors
  SET
    deleted = FALSE,
    deleted_at = NULL,
    deleted_by = NULL
  WHERE
    id = monitor_id
    AND deleted = TRUE;

  -- Get the updated monitor
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'deleted', m.deleted,
      'deleted_at', m.deleted_at,
      'deleted_by', m.deleted_by
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = monitor_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;