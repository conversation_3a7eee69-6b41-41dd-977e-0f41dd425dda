-- This script checks the Supabase storage configuration
-- Run this in the Supabase SQL Editor to diagnose storage issues

-- Check if the avatars bucket exists
SELECT * FROM storage.buckets WHERE id = 'avatars';

-- Check storage policies for the avatars bucket
SELECT
  p.policyname,
  p.cmd AS operation, -- Using cmd instead of operation
  p.qual AS definition,
  p.roles
FROM pg_policies p
WHERE p.schemaname = 'storage'
AND p.tablename = 'objects'
AND p.policyname LIKE '%avatar%';

-- Check if there are any objects in the avatars bucket
SELECT * FROM storage.objects WHERE bucket_id = 'avatars';

-- Check if the storage.objects table is accessible
SELECT COUNT(*) FROM storage.objects;

-- Check if the current user has the necessary permissions
SELECT
  grantee,
  table_schema,
  table_name,
  privilege_type
FROM information_schema.role_table_grants
WHERE table_schema = 'storage'
AND table_name = 'objects';

-- Check if the RLS policies are enabled on the storage.objects table
SELECT
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables
WHERE schemaname = 'storage'
AND tablename = 'objects';

-- Check if the storage API extension is installed
SELECT * FROM pg_extension WHERE extname = 'storage';

-- Check if the storage schema exists
SELECT schema_name FROM information_schema.schemata WHERE schema_name = 'storage';

-- Check if the current user can create objects in the avatars bucket
-- This is a test query that simulates what happens when a user tries to upload a file
SELECT
  CASE
    WHEN EXISTS (
      SELECT 1 FROM storage.buckets WHERE id = 'avatars'
    ) THEN 'Avatars bucket exists'
    ELSE 'Avatars bucket does not exist'
  END AS bucket_check,
  CASE
    WHEN (
      SELECT COUNT(*) FROM pg_policies
      WHERE schemaname = 'storage'
      AND tablename = 'objects'
      AND policyname LIKE '%avatar%'
    ) > 0 THEN 'Avatar policies exist'
    ELSE 'No avatar policies found'
  END AS policy_check;
