// This script tests if the avatar is being correctly retrieved from user metadata
// Run this in the browser console

async function testAvatarDisplay() {
  console.log('Starting avatar display test...');

  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }

  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }

  console.log('User is logged in:', user);
  console.log('User metadata:', user.user_metadata);

  // Check if the user has an avatar URL
  const avatarUrl = user.user_metadata?.avatar_url;
  if (!avatarUrl) {
    console.error('User does not have an avatar URL in metadata.');

    // Try to update the user metadata with the avatar URL
    console.log('Attempting to update user metadata with avatar URL...');

    // Get the Supabase URL from the window config or environment
    const supabaseUrl = window.VUM_CONFIG?.SUPABASE_URL ||
                        window.env?.SUPABASE_URL ||
                        'your-project-url.supabase.co';

    const testAvatarUrl = `${supabaseUrl}/storage/v1/object/public/avatars/test-avatar.png`;
    console.log(`Using test avatar URL: ${testAvatarUrl}`);

    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: {
        avatar_url: testAvatarUrl,
      },
    });

    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      return;
    }

    console.log('User metadata updated successfully:', userData);
    console.log('New user metadata:', userData.user.user_metadata);

    // Refresh the page to see the changes
    console.log('Please refresh the page to see the changes.');
    return;
  }

  console.log('Avatar URL found in user metadata:', avatarUrl);

  // Try to load the avatar image
  console.log('Testing if avatar image is accessible...');
  const img = new Image();
  img.src = avatarUrl;

  img.onload = () => {
    console.log('Avatar image loaded successfully!');
    console.log('Test PASSED ✅');

    // Create a preview of the avatar
    const div = document.createElement('div');
    div.style.position = 'fixed';
    div.style.top = '10px';
    div.style.right = '10px';
    div.style.zIndex = '9999';
    div.style.background = 'white';
    div.style.padding = '10px';
    div.style.border = '1px solid black';
    div.style.borderRadius = '5px';
    div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';

    const imgElement = document.createElement('img');
    imgElement.src = avatarUrl;
    imgElement.style.width = '100px';
    imgElement.style.height = '100px';
    imgElement.style.borderRadius = '50%';
    imgElement.style.objectFit = 'cover';

    const message = document.createElement('p');
    message.textContent = 'Avatar is working! If you don\'t see it in your profile, please refresh the page.';
    message.style.marginTop = '10px';
    message.style.fontSize = '14px';

    const refreshButton = document.createElement('button');
    refreshButton.textContent = 'Refresh Page';
    refreshButton.style.marginTop = '10px';
    refreshButton.style.padding = '5px';
    refreshButton.style.cursor = 'pointer';
    refreshButton.onclick = () => window.location.reload();

    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close';
    closeButton.style.marginTop = '10px';
    closeButton.style.marginLeft = '10px';
    closeButton.style.padding = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.onclick = () => document.body.removeChild(div);

    div.appendChild(imgElement);
    div.appendChild(message);
    div.appendChild(refreshButton);
    div.appendChild(closeButton);

    document.body.appendChild(div);
  };

  img.onerror = () => {
    console.error('Error loading avatar image. The URL might not be publicly accessible.');
    console.log('Test FAILED ❌');
  };
}

// Run the test
testAvatarDisplay().then(() => {
  console.log('Avatar display test completed.');
});
