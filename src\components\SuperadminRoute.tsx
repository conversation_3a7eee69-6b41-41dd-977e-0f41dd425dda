import React, { useEffect } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { useAuth } from '@/contexts/AuthContext';

/**
 * A route component that only allows superadmins to access the wrapped content.
 * Redirects to the dashboard if the user is not a superadmin.
 */
const SuperadminRoute = ({ children }: { children: React.ReactNode }) => {
  const navigate = useNavigate();
  const { user, loading: authLoading } = useAuth();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin, isLoading: checkingSuperadmin, error } = useGlobalSuperadminQuery();
  
  useEffect(() => {
    // Only check after both auth and superadmin status are loaded
    if (!authLoading && !checkingSuperadmin && !isSuperadmin) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }

    if (error) {
      console.error('Error checking superadmin status:', error);
      toast({
        title: 'Error',
        description: 'Failed to verify permissions. Please try again.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [isSuperadmin, checkingSuperadmin, authLoading, navigate, error]);
  
  // Show loading state while checking
  if (authLoading || checkingSuperadmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold">Verifying permissions...</h2>
        </div>
      </div>
    );
  }
  
  // Redirect if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }
  
  // Return null during redirect (handled by useEffect)
  if (!isSuperadmin) {
    return null;
  }
  
  // User is a superadmin, render the children
  return <>{children}</>;
};

export default SuperadminRoute;
