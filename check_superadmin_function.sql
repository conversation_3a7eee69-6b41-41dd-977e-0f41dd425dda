-- This script checks if the is_global_superadmin function exists and is working correctly
-- Run this in the Supabase SQL Editor

-- Check if the function exists
SELECT 
    routine_name, 
    routine_type,
    data_type AS return_type,
    external_language
FROM 
    information_schema.routines
WHERE 
    routine_name = 'is_global_superadmin'
    AND routine_schema = 'public';

-- Check if the user_roles table exists
SELECT 
    table_name, 
    table_schema
FROM 
    information_schema.tables
WHERE 
    table_name = 'user_roles'
    AND table_schema = 'public';

-- Check the structure of the user_roles table
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM 
    information_schema.columns
WHERE 
    table_name = 'user_roles'
    AND table_schema = 'public';

-- Check if the user_role enum type exists
SELECT 
    typname, 
    typtype
FROM 
    pg_type
WHERE 
    typname = 'user_role';

-- Check the values of the user_role enum
SELECT 
    enumlabel
FROM 
    pg_enum
WHERE 
    enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role');

-- Check if there are any superadmins in the user_roles table
SELECT 
    *
FROM 
    public.user_roles
WHERE 
    role_type = 'superadmin';

-- Create or replace the is_global_superadmin function
CREATE OR REPLACE FUNCTION is_global_superadmin(
  check_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_to_check UUID;
  is_superadmin BOOLEAN;
BEGIN
  -- If no user_id is provided, use the authenticated user
  user_to_check := COALESCE(check_user_id, auth.uid());
  
  -- Check if the user has the superadmin role
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = user_to_check
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create or replace the is_global_superadmin_bypass function
CREATE OR REPLACE FUNCTION is_global_superadmin_bypass(
  check_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_to_check UUID;
  is_superadmin BOOLEAN;
BEGIN
  -- If no user_id is provided, use the authenticated user
  user_to_check := COALESCE(check_user_id, auth.uid());
  
  -- Check if the user has the superadmin role
  -- This function bypasses RLS by using SECURITY DEFINER
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = user_to_check
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make <EMAIL> a superadmin
DO $$
DECLARE
    target_user_id UUID;
BEGIN
    -- Get the user ID from auth.users
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    -- Check if the user exists
    IF target_user_id IS NULL THEN
        RAISE NOTICE 'User <NAME_EMAIL> not found';
        RETURN;
    END IF;
    
    -- Delete any existing roles for this user to avoid conflicts
    DELETE FROM public.user_roles WHERE user_id = target_user_id;
    
    -- Insert the user as a global superadmin
    INSERT INTO public.user_roles (user_id, role_type)
    VALUES (target_user_id, 'superadmin');
    
    RAISE NOTICE 'User <EMAIL> (ID: %) has been made a global superadmin', target_user_id;
END $$;
