import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { GlobalUserRole, UserRole } from '@/types/company';

export function useCompanyRoles() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Check if the current user is a global superadmin
  const isGlobalSuperadmin = async (): Promise<boolean> => {
    if (!user) {
      return false;
    }

    try {
      // Use the new reliable function for checking superadmin access
      const { data, error } = await supabase.rpc('check_superadmin_access');

      if (error) {
        console.error('Error checking superadmin access:', error);

        // Fallback to direct query if the function fails
        const { data: directData, error: directError } = await supabase
          .from('user_roles')
          .select('*')
          .eq('user_id', user.id)
          .eq('role_type', 'superadmin');

        if (directError) {
          console.error('Error with direct query fallback:', directError);
          return false;
        }

        return directData && directData.length > 0;
      }

      return data || false;
    } catch (err) {
      // Only log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error checking superadmin status:', err);
      }
      return false;
    }
  };

  // Assign global superadmin role to a user
  const assignGlobalSuperadmin = async (userId: string) => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase.rpc('assign_global_superadmin', {
      target_user_id: userId,
      admin_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error assigning global superadmin role',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    toast({
      title: 'Role updated',
      description: 'User has been assigned global superadmin role.',
    });

    return data;
  };

  // Revoke global superadmin role from a user
  const revokeGlobalSuperadmin = async (userId: string) => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase.rpc('revoke_global_superadmin', {
      target_user_id: userId,
      admin_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error revoking global superadmin role',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    toast({
      title: 'Role updated',
      description: 'Global superadmin role has been revoked.',
    });

    return data;
  };

  // Get all global superadmins
  const getGlobalSuperadmins = async (): Promise<GlobalUserRole[]> => {
    if (!user) return [];

    try {
      const { data, error } = await supabase
        .from('user_roles')
        .select('*')
        .eq('role_type', 'superadmin');

      if (error) {
        // Try a different approach with a custom RPC function if available
        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc('get_all_superadmins');

          if (rpcError) {
            if (process.env.NODE_ENV === 'development') {
              console.error('Failed to fetch superadmins:', rpcError);
            }
            return [];
          }

          return rpcData || [];
        } catch (rpcErr) {
          if (process.env.NODE_ENV === 'development') {
            console.error('Error in RPC fallback:', rpcErr);
          }
          return [];
        }
      }

      return data || [];
    } catch (err) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error fetching superadmins:', err);
      }
      return [];
    }
  };

  // Assign admin role to a user
  const assignAdminRole = async ({ userId, companyId }: { userId: string; companyId: string }) => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase.rpc('assign_admin_role', {
      user_id: userId,
      company_id: companyId,
      admin_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error assigning admin role',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    toast({
      title: 'Role updated',
      description: 'User has been assigned admin role.',
    });

    return data;
  };

  // Assign user role to a user
  const assignUserRole = async ({ userId, companyId }: { userId: string; companyId: string }) => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase.rpc('assign_user_role', {
      user_id: userId,
      company_id: companyId,
      admin_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error assigning user role',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    toast({
      title: 'Role updated',
      description: 'User has been assigned user role.',
    });

    return data;
  };

  // Remove a user from a company
  const removeCompanyMember = async ({ userId, companyId }: { userId: string; companyId: string }) => {
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase.rpc('remove_company_member', {
      user_id: userId,
      company_id: companyId,
      admin_user_id: user.id
    });

    if (error) {
      toast({
        title: 'Error removing user',
        description: error.message,
        variant: 'destructive',
      });
      throw error;
    }

    toast({
      title: 'User removed',
      description: 'User has been removed from the company.',
    });

    return data;
  };

  // Check if the current user is a superadmin (either global or in the company)
  const isSuperadmin = async (companyId?: string): Promise<boolean> => {
    if (!user) return false;

    // First check if the user is a global superadmin
    const isGlobal = await isGlobalSuperadmin();
    if (isGlobal) return true;

    // If not global and no companyId provided, return false
    if (!companyId) return false;

    // Check if the user is a superadmin in the specific company
    const { data, error } = await supabase
      .from('company_members')
      .select('role_type')
      .eq('company_id', companyId)
      .eq('user_id', user.id)
      .eq('role_type', 'superadmin')
      .single();

    if (error || !data) {
      return false;
    }

    return true;
  };

  // Check if the current user is an admin in the company (or a global superadmin)
  const isAdmin = async (companyId: string): Promise<boolean> => {
    if (!user) return false;

    // First check if the user is a global superadmin
    const isGlobal = await isGlobalSuperadmin();
    if (isGlobal) return true;

    // Check if the user is an admin in the specific company
    const { data, error } = await supabase
      .from('company_members')
      .select('role_type')
      .eq('company_id', companyId)
      .eq('user_id', user.id)
      .in('role_type', ['admin', 'superadmin'])
      .single();

    if (error || !data) {
      return false;
    }

    return true;
  };

  // Update a user's role
  const updateUserRole = async ({
    userId,
    companyId,
    role_type,
    isGlobal = false
  }: {
    userId: string;
    companyId: string;
    role_type: UserRole;
    isGlobal?: boolean;
  }) => {
    // Handle global superadmin role
    if (isGlobal && role_type === 'superadmin') {
      return assignGlobalSuperadmin(userId);
    }

    // Handle company-specific roles
    switch (role_type) {
      case 'admin':
        return assignAdminRole({ userId, companyId });
      case 'user':
        return assignUserRole({ userId, companyId });
      default:
        throw new Error(`Invalid role: ${role_type}`);
    }
  };

  // React Query mutations
  const useUpdateUserRoleMutation = () => {
    return useMutation({
      mutationFn: updateUserRole,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['company-members'] });
      },
    });
  };

  const useRemoveCompanyMemberMutation = () => {
    return useMutation({
      mutationFn: removeCompanyMember,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['company-members'] });
      },
    });
  };

  // React Query hooks for global superadmin
  const useGlobalSuperadminQuery = () => {
    return useQuery({
      queryKey: ['global-superadmin', user?.id],
      queryFn: isGlobalSuperadmin,
      enabled: !!user,
    });
  };

  const useGlobalSuperadminsQuery = () => {
    return useQuery({
      queryKey: ['global-superadmins'],
      queryFn: getGlobalSuperadmins,
      enabled: !!user,
    });
  };

  const useAssignGlobalSuperadminMutation = () => {
    return useMutation({
      mutationFn: assignGlobalSuperadmin,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['global-superadmins'] });
      },
    });
  };

  const useRevokeGlobalSuperadminMutation = () => {
    return useMutation({
      mutationFn: revokeGlobalSuperadmin,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['global-superadmins'] });
      },
    });
  };

  return {
    // Role check functions
    isSuperadmin,
    isAdmin,
    isGlobalSuperadmin,

    // Direct mutation functions
    assignGlobalSuperadmin,
    revokeGlobalSuperadmin,
    getGlobalSuperadmins,

    // React Query hooks
    useUpdateUserRoleMutation,
    useRemoveCompanyMemberMutation,
    useGlobalSuperadminQuery,
    useGlobalSuperadminsQuery,
    useAssignGlobalSuperadminMutation,
    useRevokeGlobalSuperadminMutation,
  };
}
