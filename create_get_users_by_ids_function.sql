-- This script creates the get_users_by_ids RPC function
-- Run this in the Supabase SQL Editor

-- Create the function to get user details by IDs
CREATE OR REPLACE FUNCTION get_users_by_ids(user_ids UUID[])
RETURNS SETOF json AS $$
BEGIN
  -- First try to get from public.users table
  RETURN QUERY
  SELECT 
    json_build_object(
      'id', u.id,
      'email', u.email,
      'full_name', u.full_name,
      'avatar_url', au.raw_user_meta_data->>'avatar_url'
    )
  FROM 
    public.users u
  JOIN
    auth.users au ON u.id = au.id
  WHERE 
    u.id = ANY(user_ids);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_users_by_ids(UUID[]) TO authenticated;

-- Test the function
SELECT * FROM get_users_by_ids(ARRAY['3a19a1f7-3f61-48a8-949c-caa3fba04924'::UUID]);
