// This script checks the status of the monitor system
// Run with: node check_monitor_system.js

import 'dotenv/config';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { rateLimiter, requestLogger, isIpWhitelisted } from './security_middleware.js';
import { checkAndSendAlerts } from './monitor_alerts.js';

dotenv.config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('Error: Required environment variables SUPABASE_URL and SUPABASE_SERVICE_KEY must be set');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function checkMonitorSystem(clientIp) {
  // Check if IP is whitelisted for sensitive operations
  if (!await isIpWhitelisted(clientIp)) {
    console.error('Access denied: IP not whitelisted');
    return;
  }
  console.log('Checking Monitor System Status...\n');
  
  try {
    // 1. Check monitors table
    console.log('1. Checking monitors table...');
    const { data: monitorSamples, error: monitorSamplesError } = await supabase
      .from('monitors')
      .select('id, name, active, type, target, interval, timeout')
      .limit(10);
      
    if (monitorSamplesError) {
      console.error('  Error accessing monitors table:', monitorSamplesError.message);
    } else {
      console.log(`  Found ${monitorSamples.length} monitors`);
      if (monitorSamples.length > 0) {
        console.log('  Sample monitor:');
        console.log(`    ID: ${monitorSamples[0].id}`);
        console.log(`    Name: ${monitorSamples[0].name}`);
        console.log(`    Active: ${monitorSamples[0].active} (${typeof monitorSamples[0].active})`);
        console.log(`    Type: ${monitorSamples[0].type}`);
        console.log(`    Target: ${monitorSamples[0].target}`);
        console.log(`    Interval: ${monitorSamples[0].interval} minutes`);
        console.log(`    Timeout: ${monitorSamples[0].timeout} seconds`);
      }
    }
    
    // 2. Check monitor_history table
    console.log('\n2. Checking monitor_history table...');
    const { data: historyColumns, error: historyColumnsError } = await supabase
      .rpc('get_monitor_history_columns');
    
    if (historyColumnsError) {
      console.error('  Error checking monitor_history columns:', historyColumnsError.message);
    } else {
      console.log('  Monitor History Table Structure:');
      historyColumns.forEach(col => {
        console.log(`    ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
    }
    
    // 3. Check monitor_history data
    const { data: history, error: historyError } = await supabase
      .from('monitor_history')
      .select('id, monitor_id, status, response_time, error_message, timestamp')
      .order('timestamp', { ascending: false })
      .limit(5);
      
    if (historyError) {
      console.error('  Error accessing monitor_history data:', historyError.message);
    } else {
      console.log(`\n  Found ${history.length} recent history records`);
      if (history.length > 0) {
        console.log('  Most recent records:');
        history.forEach((record, i) => {
          console.log(`    Record ${i+1}:`);
          console.log(`      ID: ${record.id}`);
          console.log(`      Monitor ID: ${record.monitor_id}`);
          console.log(`      Status: ${record.status} (${typeof record.status})`);
          console.log(`      Response Time: ${record.response_time}ms`);
          console.log(`      Error: ${record.error_message || 'None'}`);
          console.log(`      Timestamp: ${record.timestamp}`);
        });
      }
    }
    
    // 3. Check status counts
    console.log('\n3. Checking monitor status counts...\n');
    
    // Get monitor status counts
    const { data: statusCounts, error: statusError } = await supabase
      .rpc('get_monitor_status_counts');
      
    if (statusError) {
      console.error('  Error getting status counts:', statusError.message);
    } else {
      console.log('  Current monitor status:');
      console.log(`    Total monitors: ${statusCounts.total}`);
      console.log(`    Up: ${statusCounts.up}`);
      console.log(`    Down: ${statusCounts.down}`);
    }
    
    // Get active/inactive monitor counts
    const { data: monitorStatuses, error: monitorStatusesError } = await supabase
      .from('monitors')
      .select('active')
      .is('deleted', false);
      
    if (monitorStatusesError) {
      console.error('  Error counting monitors:', monitorStatusesError.message);
    } else {
      const total = monitorStatuses.length;
      const active = monitorStatuses.filter(m => m.active).length;
      const paused = monitorStatuses.filter(m => !m.active).length;
      
      console.log('\n  Monitor activity status:');
      console.log(`    Total monitors: ${total}`);
      console.log(`    Active: ${active}`);
      console.log(`    Paused: ${paused}`);
    }
    
    // 4. Check for monitors without history
    console.log('\n4. Checking for monitors without history...');
    
    // Get monitors without history
    const { data: noHistory, error: noHistoryError } = await supabase
      .rpc('execute_sql', {
        query_text: 'SELECT jsonb_agg(jsonb_build_object(\'id\', m.id, \'name\', m.name, \'active\', m.active)) FROM monitors m WHERE NOT EXISTS (SELECT 1 FROM monitor_history h WHERE h.monitor_id = m.id) AND m.deleted IS FALSE'
      });
    
    if (noHistoryError) {
      console.error('  Error checking for monitors without history:', noHistoryError.message);
    } else {
      const monitors = noHistory[0] || [];
      console.log(`  Found ${monitors.length} monitors without history records`);
      if (monitors.length > 0) {
        console.log('  Monitors without history:');
        monitors.forEach((monitor, i) => {
          console.log(`    ${i+1}. ${monitor.name} (${monitor.id}) - Active: ${monitor.active}`);
        });
      }
    }
    
    // Check for alerts
    await checkAndSendAlerts();
    
    // Log the results
    console.log('Monitor Check Complete!\n');
  } catch (error) {
    console.error('Error checking monitor system:', error.message);
  }
}

// Run the check
checkMonitorSystem();
