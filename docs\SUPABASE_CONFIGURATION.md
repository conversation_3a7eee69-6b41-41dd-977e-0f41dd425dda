# Supabase Configuration

This document describes how Supabase is configured in the Vurbis Uptime Monitor (VUM) application.

## IMPORTANT SECURITY NOTICE

**NEVER hardcode Supabase credentials (URL, API keys, etc.) in your code or commit them to the repository.**

Always use environment variables for Supabase credentials and ensure that files containing actual values (like `.env.local`) are listed in `.gitignore` to prevent them from being committed to the repository.

The example files (`.env.example` and `.env.local.example`) contain placeholders that should be replaced with actual values in your local environment.

## Overview

The application uses Supabase for:
- Authentication
- Database
- Storage
- Realtime subscriptions

## Client Configuration

The Supabase client is configured in `src/integrations/supabase/client.ts`. This is the only instance of the Supabase client that should be used throughout the application.

```typescript
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Access environment variables directly from import.meta.env
// No fallbacks - environment variables MUST be set
const url = import.meta.env.VITE_SUPABASE_URL;
const key = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if environment variables are set
if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
  console.error('ERROR: Supabase credentials are not set.');
  throw new Error('Supabase credentials are not set');
}

// Create the Supabase client
export const supabase = createClient<Database>(url, key);
```

### Development vs. Production

Environment variables **MUST** be set for both development and production environments. There are no fallback values.

For development, you can use the `.env.local` file to set these variables. For production, you should use the appropriate environment variable configuration for your hosting platform.

## Environment Variables

The Supabase client uses the following environment variables:

- `VITE_SUPABASE_URL`: The URL of your Supabase project
- `VITE_SUPABASE_ANON_KEY`: The anonymous key for your Supabase project

These variables are defined in the following files:

- `.env.local`: Local development environment variables (not committed to the repository)
- `.env`: Fallback environment variables (committed to the repository, but should not contain sensitive information)

## Database Types

The database schema is defined in `src/integrations/supabase/types.ts`. This file contains TypeScript types for the Supabase database schema.

## Usage

Import the Supabase client like this:

```typescript
import { supabase } from '@/integrations/supabase/client';
```

Import database types like this:

```typescript
import { Database } from '@/integrations/supabase/types';
```

## Context Provider

The application uses a Supabase context provider to make the Supabase client available throughout the application. This is defined in `src/contexts/SupabaseProvider.tsx`.

```typescript
import { supabase } from '@/integrations/supabase/client';

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  return (
    <SupabaseContext.Provider value={{ supabase }}>
      {children}
    </SupabaseContext.Provider>
  );
}
```

Use the Supabase client in components like this:

```typescript
import { useSupabase } from '@/contexts/SupabaseProvider';

function MyComponent() {
  const { supabase } = useSupabase();

  // Use supabase client
  const { data, error } = await supabase.from('monitors').select('*');
}
```

## Security Considerations

- Never commit sensitive information like API keys to the repository
- Use environment variables for all sensitive information
- Use different keys for development and production environments
- Rotate keys regularly using the key rotation service
- Use Row Level Security (RLS) policies to restrict access to data
- Use the service role key only in server-side code, never in client-side code
