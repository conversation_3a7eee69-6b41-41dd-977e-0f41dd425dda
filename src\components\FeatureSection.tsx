
import React from "react";
import { Bell, Clock, Shield, LineChart, CheckCircle, ServerCrash, Globe } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const features = [
  {
    icon: <Clock className="h-12 w-12 text-blue-500" />,
    title: "24/7 Monitoring",
    description: "We check your websites every minute to ensure they're always up and running."
  },
  {
    icon: <Bell className="h-12 w-12 text-blue-500" />,
    title: "Instant Alerts",
    description: "Get notified immediately via email, SMS, or push notifications when your site goes down."
  },
  {
    icon: <LineChart className="h-12 w-12 text-blue-500" />,
    title: "Detailed Reports",
    description: "View comprehensive uptime reports and analyze performance over time."
  },
  {
    icon: <Shield className="h-12 w-12 text-blue-500" />,
    title: "SSL Monitoring",
    description: "Monitor SSL certificates and get alerts before they expire."
  },
  {
    icon: <Globe className="h-12 w-12 text-blue-500" />,
    title: "Global Checks",
    description: "Monitor from multiple locations around the world for accurate results."
  },
  {
    icon: <CheckCircle className="h-12 w-12 text-blue-500" />,
    title: "Public Status Pages",
    description: "Create branded status pages to keep your users informed."
  }
];

const FeatureSection = () => {
  return (
    <div className="container mx-auto px-4 py-20">
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold mb-4">Why Choose UptimeMonitor?</h2>
        <p className="text-xl text-slate-600 dark:text-slate-300 max-w-2xl mx-auto">
          Our platform provides everything you need to ensure your websites are always available to your users.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {features.map((feature, index) => (
          <Card key={index} className="border border-slate-200 dark:border-slate-700">
            <CardHeader>
              <div className="mb-4">{feature.icon}</div>
              <CardTitle>{feature.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-base">{feature.description}</CardDescription>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default FeatureSection;
