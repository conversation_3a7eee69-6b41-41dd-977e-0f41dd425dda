// Script to test the "Check Now" functionality
const axios = require('axios');
require('dotenv').config();

// Configuration
const supabaseProjectRef = process.env.SUPABASE_PROJECT_REF;
const anonKey = process.env.SUPABASE_ANON_KEY;

// Check if required environment variables are set
if (!supabaseProjectRef || !anonKey) {
  console.error('ERROR: Required environment variables SUPABASE_PROJECT_REF and/or SUPABASE_ANON_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Function to test the "Check Now" functionality
async function testCheckNow(monitorId) {
  console.log('Testing "Check Now" functionality...');

  try {
    const url = `https://${supabaseProjectRef}.functions.supabase.co/monitor-checker`;
    const body = monitorId ? { monitorId, manual: true } : { manual: true };

    console.log(`Sending request to ${url}`);
    console.log(`Request body: ${JSON.stringify(body)}`);

    const response = await axios.post(url, body, {
      headers: {
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json'
      }
    });

    console.log(`Response status: ${response.status}`);
    console.log(`Checked ${response.data.checksRun} monitors`);

    if (response.data.results && response.data.results.length > 0) {
      console.log('Results:');
      response.data.results.forEach(result => {
        const status = result.status ? 'UP' : 'DOWN';
        console.log(`- Monitor '${result.name}' is ${status} (Response time: ${result.response_time}ms)`);
        if (result.error_message) {
          console.log(`  Error: ${result.error_message}`);
        }
      });
    } else {
      console.log('No results returned');
    }

    return response.data;
  } catch (error) {
    console.error('Error testing "Check Now" functionality:');
    if (error.response) {
      console.error(`Status: ${error.response.status}`);
      console.error(`Data: ${JSON.stringify(error.response.data)}`);
    } else {
      console.error(error.message);
    }
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

// Run the test
if (monitorId) {
  console.log(`Testing with monitor ID: ${monitorId}`);
  testCheckNow(monitorId)
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
} else {
  console.log('Testing with all monitors');
  testCheckNow()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
}
