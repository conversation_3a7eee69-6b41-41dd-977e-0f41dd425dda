// Check if a notification should be sent (status changed)
async function checkStatusChange(supabase, monitor, currentStatus) {
  // Get the previous check
  const { data: previousChecks } = await supabase.from('monitor_history').select('status').eq('monitor_id', monitor.id).order('timestamp', {
    ascending: false
  }).limit(2);
  // If this is the first check or status changed, return true
  if (!previousChecks || previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
    return true;
  }
  return false;
}

// Send a notification
async function sendNotification(supabase, monitor, status) {
  // In a real implementation, you'd send an email, SMS, webhook, etc.
  console.log(`Notification for monitor ${monitor.name}: Status is now ${status ? 'UP' : 'DOWN'}`);
  // For now, we'll just log the notification in a notifications table
  await supabase.from('notifications').insert({
    monitor_id: monitor.id,
    user_id: monitor.user_id,
    company_id: monitor.company_id,
    message: `Monitor ${monitor.name} is now ${status ? 'UP' : 'DOWN'}`,
    type: status ? 'up' : 'down',
    read: false,
    created_at: new Date().toISOString()
  });
}

serve(async (req)=>{
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: corsHeaders
    });
  }
  
  try {
    // Parse request body if present
    let requestBody = {};
    if (req.method === 'POST') {
      try {
        requestBody = await req.json();
      } catch (e) {
        // If parsing fails, assume empty body
        console.log('Failed to parse request body, assuming empty');
      }
    }
    
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Check if this is a port check request with a temporary monitor
    if (requestBody?.tempMonitor && requestBody.tempMonitor.type === 'port') {
      console.log('Port check request received for temporary monitor');
      try {
        const tempMonitor = requestBody.tempMonitor;
        const checkResult = await performCheck(tempMonitor);
        
        // For port checks, extract the detailed port results
        let portResults = [];
        if (tempMonitor.type === 'port' && tempMonitor.target.includes('|')) {
          const [host, portsStr] = tempMonitor.target.split('|');
          const ports = portsStr.split(',').map((p)=>parseInt(p.trim()));
          
          // Create a result for each port
          portResults = ports.map((port)=>{
            // Find this port in the detailed results if available
            const portDetail = checkResult._portDetails?.find((p)=>p.port === port);
            return {
              port,
              status: portDetail ? portDetail.status : false,
              error: portDetail?.error
            };
          });
        }
        
        return new Response(JSON.stringify({
          success: true,
          status: checkResult.status,
          portResults
        }), {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      } catch (error) {
        console.error('Error performing port check:', error);
        return new Response(JSON.stringify({
          success: false,
          error: error.message
        }), {
          status: 500,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
    }
    
    // Check if a specific monitor ID was provided
    const specificMonitorId = requestBody?.monitorId;
    console.log(`Request received. Specific monitor ID: ${specificMonitorId || 'none'}`);
    
    // Get monitors to check
    let monitorsQuery = supabase.from('monitors').select('*').eq('active', true);
    
    // If a specific monitor ID was provided, only check that one
    if (specificMonitorId) {
      monitorsQuery = monitorsQuery.eq('id', specificMonitorId);
    }
    
    const { data: monitors, error: monitorsError } = await monitorsQuery;
    
    if (monitorsError) {
      throw new Error(`Failed to fetch monitors: ${monitorsError.message}`);
    }
    
    // If no monitors found, handle appropriately
    if (!monitors || monitors.length === 0) {
      // If a specific monitor was requested but not found, return an error
      if (specificMonitorId) {
        return new Response(JSON.stringify({
          success: false,
          error: `Monitor with ID ${specificMonitorId} not found or not active`
        }), {
          status: 404,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        });
      }
      
      // Otherwise, just return success with no checks run
      return new Response(JSON.stringify({
        success: true,
        checksRun: 0,
        results: []
      }), {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      });
    }
