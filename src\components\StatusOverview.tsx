
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, AlertTriangle, XCircle, Clock } from 'lucide-react';
import { useMonitors } from '@/hooks/use-monitors';
import { useMonitorHistory } from '@/hooks/use-monitor-history';

const StatusOverview = () => {
  const { useGetMonitorsQuery } = useMonitors();
  const { data: monitors, isLoading: monitorsLoading } = useGetMonitorsQuery();

  // Get the latest history for all monitors
  const { useGetLatestHistoryForMonitors } = useMonitorHistory();
  const { data: monitorHistories = {}, isLoading: historiesLoading } = useGetLatestHistoryForMonitors(
    monitors?.map(m => m.id) || []
  );

  // Calculate stats based on real data
  const calculateStats = () => {
    if (!monitors || monitors.length === 0) {
      return [
        { title: "Up", value: 0, icon: <CheckCircle className="h-5 w-5 text-green-500" />, description: "Monitors running smoothly", bgColor: "bg-green-50 dark:bg-green-900/20", textColor: "text-green-700 dark:text-green-400" },
        { title: "Degraded", value: 0, icon: <AlertTriangle className="h-5 w-5 text-amber-500" />, description: "Monitors experiencing issues", bgColor: "bg-amber-50 dark:bg-amber-900/20", textColor: "text-amber-700 dark:text-amber-400" },
        { title: "Down", value: 0, icon: <XCircle className="h-5 w-5 text-red-500" />, description: "Monitors completely down", bgColor: "bg-red-50 dark:bg-red-900/20", textColor: "text-red-700 dark:text-red-400" },
        { title: "Paused", value: 0, icon: <Clock className="h-5 w-5 text-slate-500" />, description: "Monitors currently paused", bgColor: "bg-slate-50 dark:bg-slate-800", textColor: "text-slate-700 dark:text-slate-400" }
      ];
    }

    // Count monitors by status
    const paused = monitors.filter(m => !m.active).length;

    // Count up, degraded, and down monitors based on their latest history
    let up = 0;
    let degraded = 0;
    let down = 0;

    // Only count active monitors for up, degraded, and down
    const activeMonitors = monitors.filter(m => m.active);

    activeMonitors.forEach(monitor => {
      // Use the monitor's status from the monitors table instead of the history
      if (!monitor.status) {
        // If no status yet, consider it up
        up++;
      } else {
        // Check if status is a string (new format) or boolean (old format)
        if (typeof monitor.status === 'string') {
          const statusLower = monitor.status.toLowerCase();
          if (statusLower === 'up') up++;
          else if (statusLower === 'degraded') degraded++;
          else if (statusLower === 'down') down++;
          else up++; // Default to up for unknown status
        } else {
          // Handle legacy boolean status
          if (monitor.status) up++;
          else down++;
        }
      }
    });

    return [
    {
      title: "Up",
      value: up,
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
      description: "Monitors running smoothly",
      bgColor: "bg-green-50 dark:bg-green-900/20",
      textColor: "text-green-700 dark:text-green-400"
    },
    {
      title: "Degraded",
      value: degraded,
      icon: <AlertTriangle className="h-5 w-5 text-amber-500" />,
      description: "Monitors experiencing issues",
      bgColor: "bg-amber-50 dark:bg-amber-900/20",
      textColor: "text-amber-700 dark:text-amber-400"
    },
    {
      title: "Down",
      value: down,
      icon: <XCircle className="h-5 w-5 text-red-500" />,
      description: "Monitors completely down",
      bgColor: "bg-red-50 dark:bg-red-900/20",
      textColor: "text-red-700 dark:text-red-400"
    },
    {
      title: "Paused",
      value: paused,
      icon: <Clock className="h-5 w-5 text-slate-500" />,
      description: "Monitors currently paused",
      bgColor: "bg-slate-50 dark:bg-slate-800",
      textColor: "text-slate-700 dark:text-slate-400"
    }
  ];
  };

  const stats = calculateStats();

  const isLoading = monitorsLoading || historiesLoading;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="border-transparent animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-5 bg-slate-200 dark:bg-slate-700 rounded w-24"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-slate-200 dark:bg-slate-700 rounded w-12 mb-2"></div>
              <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-32"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => (
        <Card key={index} className={`${stat.bgColor} border-transparent`}>
          <CardHeader className="pb-2">
            <div className="flex justify-between items-start">
              <CardTitle className={`text-lg ${stat.textColor}`}>{stat.title}</CardTitle>
              {stat.icon}
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-1">{stat.value}</div>
            <CardDescription>{stat.description}</CardDescription>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default StatusOverview;
