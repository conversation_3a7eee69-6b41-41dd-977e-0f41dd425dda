-- Update the monitor_history table to ensure it can handle text status values
DO $$
BEGIN
    -- Check if the status column is boolean
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'public'
        AND table_name = 'monitor_history'
        AND column_name = 'status'
        AND data_type = 'boolean'
    ) THEN
        -- Create a temporary column
        ALTER TABLE public.monitor_history ADD COLUMN status_text TEXT;
        
        -- Update the temporary column based on the boolean value
        UPDATE public.monitor_history
        SET status_text = CASE WHEN status = true THEN 'up' ELSE 'down' END;
        
        -- Drop the old column
        ALTER TABLE public.monitor_history DROP COLUMN status;
        
        -- Rename the temporary column
        ALTER TABLE public.monitor_history RENAME COLUMN status_text TO status;
        
        -- Add a check constraint to ensure valid status values
        ALTER TABLE public.monitor_history ADD CONSTRAINT monitor_history_status_check
            CHECK (status IN ('up', 'down', 'degraded'));
    END IF;
END $$;
