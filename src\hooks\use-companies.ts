import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyRoles } from './use-company-roles';
import { logAction, getClientIp, getUserAgent } from '@/utils/auditLogger';
import {
  getUserCompanies,
  getCompanyMembers,
  createCompany,
  updateCompany,
  deleteCompany,
  addCompanyMember,
  updateCompanyMember,
  removeCompanyMember
} from '@/services/company-service';
import {
  Company,
  CompanyMember,
  CreateCompanyData,
  UpdateCompanyData,
  AddCompanyMemberData,
  UpdateCompanyMemberData
} from '@/types/company';

export function useCompanies() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const companyRoles = useCompanyRoles();

  // Get all companies for the current user
  const useGetCompaniesQuery = () => {
    return useQuery({
      queryKey: ['companies', user?.id],
      queryFn: () => getUserCompanies(user!.id),
      enabled: !!user,
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    });
  };

  // Get company members for a specific company
  const useGetCompanyMembersQuery = (companyId: string) => {
    return useQuery({
      queryKey: ['company-members', companyId],
      queryFn: () => getCompanyMembers(companyId),
      enabled: !!companyId && companyId !== 'all',
      staleTime: 2 * 60 * 1000, // 2 minutes
    });
  };

  // Create company mutation
  const useCreateCompanyMutation = () => {
    return useMutation({
      mutationFn: async (data: CreateCompanyData) => {
        if (!user) throw new Error('User not authenticated');
        
        // Check if the user is a superadmin
        const isSuperadmin = await companyRoles.isGlobalSuperadmin();
        if (!isSuperadmin) {
          throw new Error('Only superadmins can create companies');
        }

        const company = await createCompany(data, user.id);

        // Log the action
        await logAction(
          user.id,
          'create',
          'companies',
          company.id,
          null,
          company,
          getClientIp(),
          getUserAgent()
        );

        return company;
      },
      onSuccess: (newCompany) => {
        // Invalidate and refetch companies
        queryClient.invalidateQueries({ queryKey: ['companies'] });
        
        toast({
          title: 'Success',
          description: 'Company created successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to create company',
          variant: 'destructive',
        });
      },
    });
  };

  // Update company mutation
  const useUpdateCompanyMutation = () => {
    return useMutation({
      mutationFn: async ({ id, data }: { id: string; data: UpdateCompanyData }) => {
        if (!user) throw new Error('User not authenticated');

        // Get the company data before update for audit log
        const companies = queryClient.getQueryData<Company[]>(['companies', user.id]);
        const oldCompany = companies?.find(c => c.id === id);

        const updatedCompany = await updateCompany(id, data);

        // Log the action
        await logAction(
          user.id,
          'update',
          'companies',
          id,
          oldCompany,
          updatedCompany,
          getClientIp(),
          getUserAgent()
        );

        return updatedCompany;
      },
      onSuccess: (updatedCompany) => {
        // Update the companies cache
        queryClient.setQueryData<Company[]>(['companies', user?.id], (old) => {
          if (!old) return [updatedCompany];
          return old.map(company => 
            company.id === updatedCompany.id ? updatedCompany : company
          );
        });

        toast({
          title: 'Success',
          description: 'Company updated successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to update company',
          variant: 'destructive',
        });
      },
    });
  };

  // Delete company mutation
  const useDeleteCompanyMutation = () => {
    return useMutation({
      mutationFn: async (id: string) => {
        if (!user) throw new Error('User not authenticated');

        // Get the company data before deletion for audit log
        const companies = queryClient.getQueryData<Company[]>(['companies', user.id]);
        const companyToDelete = companies?.find(c => c.id === id);

        // Check if the user is a superadmin
        const isSuperadmin = await companyRoles.isGlobalSuperadmin();
        
        await deleteCompany(id, user.id, isSuperadmin);

        // Log the action
        const actionType = isSuperadmin ? 'delete' : 'soft_delete';
        await logAction(
          user.id,
          actionType,
          'companies',
          id,
          companyToDelete,
          null,
          getClientIp(),
          getUserAgent()
        );

        return id;
      },
      onSuccess: (deletedId) => {
        // Remove the company from the cache
        queryClient.setQueryData<Company[]>(['companies', user?.id], (old) => {
          if (!old) return [];
          return old.filter(company => company.id !== deletedId);
        });

        // Invalidate company members queries for the deleted company
        queryClient.invalidateQueries({ queryKey: ['company-members', deletedId] });

        toast({
          title: 'Success',
          description: 'Company deleted successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to delete company',
          variant: 'destructive',
        });
      },
    });
  };

  // Add company member mutation
  const useAddCompanyMemberMutation = () => {
    return useMutation({
      mutationFn: addCompanyMember,
      onSuccess: (newMember) => {
        // Update the company members cache
        queryClient.setQueryData<CompanyMember[]>(
          ['company-members', newMember.company_id], 
          (old) => {
            if (!old) return [newMember];
            return [...old, newMember];
          }
        );

        toast({
          title: 'Success',
          description: 'Member added successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to add member',
          variant: 'destructive',
        });
      },
    });
  };

  // Update company member mutation
  const useUpdateCompanyMemberMutation = () => {
    return useMutation({
      mutationFn: ({ id, data }: { id: string; data: UpdateCompanyMemberData }) => 
        updateCompanyMember(id, data),
      onSuccess: (updatedMember) => {
        // Update the company members cache
        queryClient.setQueryData<CompanyMember[]>(
          ['company-members', updatedMember.company_id], 
          (old) => {
            if (!old) return [updatedMember];
            return old.map(member => 
              member.id === updatedMember.id ? updatedMember : member
            );
          }
        );

        toast({
          title: 'Success',
          description: 'Member updated successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to update member',
          variant: 'destructive',
        });
      },
    });
  };

  // Remove company member mutation
  const useRemoveCompanyMemberMutation = () => {
    return useMutation({
      mutationFn: ({ id, companyId }: { id: string; companyId: string }) => {
        return removeCompanyMember(id).then(() => ({ id, companyId }));
      },
      onSuccess: ({ id, companyId }) => {
        // Remove the member from the cache
        queryClient.setQueryData<CompanyMember[]>(
          ['company-members', companyId], 
          (old) => {
            if (!old) return [];
            return old.filter(member => member.id !== id);
          }
        );

        toast({
          title: 'Success',
          description: 'Member removed successfully',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error',
          description: error.message || 'Failed to remove member',
          variant: 'destructive',
        });
      },
    });
  };

  return {
    useGetCompaniesQuery,
    useGetCompanyMembersQuery,
    useCreateCompanyMutation,
    useUpdateCompanyMutation,
    useDeleteCompanyMutation,
    useAddCompanyMemberMutation,
    useUpdateCompanyMemberMutation,
    useRemoveCompanyMemberMutation,
  };
}
