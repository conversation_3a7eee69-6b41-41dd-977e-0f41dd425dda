// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Content-Type': 'application/json'
}

// Enable debug logging
const DEBUG = true;

function log(...args: any[]) {
  if (DEBUG) {
    console.log('[Monitor Checker]', ...args);
  }
}

function error(...args: any[]) {
  console.error('[Monitor Checker]', ...args);
}

interface Monitor {
  id: string
  name: string
  target: string
  type: 'http' | 'ping' | 'port'
  interval: number
  timeout: number
  active: boolean
  company_id: string
  user_id: string
  created_at: string
}

interface CheckResult {
  monitor_id: string
  status: boolean
  response_time: number | null
  error_message: string | null
  timestamp: string
}

// Helper function to run tasks with concurrency limit
async function runWithConcurrency<T, R>(
  items: T[],
  taskFn: (item: T) => Promise<R>,
  concurrency = 5
): Promise<R[]> {
  const results: R[] = []
  const chunks: T[][] = []

  // Split items into chunks based on concurrency
  for (let i = 0; i < items.length; i += concurrency) {
    chunks.push(items.slice(i, i + concurrency))
  }

  // Process each chunk sequentially, but items within a chunk in parallel
  for (const chunk of chunks) {
    // Use Promise.allSettled to handle errors gracefully
    const chunkPromises = await Promise.allSettled(
      chunk.map(async (item) => {
        try {
          return await taskFn(item)
        } catch (error) {
          console.error(`Error processing item:`, error)
          // Return a default error result if possible
          if (typeof item === 'object' && item !== null && 'id' in item) {
            return {
              monitor_id: (item as any).id,
              name: (item as any).name || 'Unknown',
              status: false,
              response_time: 0,
              error_message: `Processing error: ${error.message}`
            } as unknown as R
          }
          throw error // Re-throw if we can't create a default result
        }
      })
    )

    // Filter out rejected promises and add fulfilled ones to results
    for (const result of chunkPromises) {
      if (result.status === 'fulfilled') {
        results.push(result.value)
      }
    }
  }

  return results
}

// Perform a check on a monitor
async function performCheck(monitor: Monitor): Promise<CheckResult & { _portDetails?: any[] }> {
  let status = false
  let responseTime: number | null = null
  let errorMessage: string | null = null
  let portDetails: any[] = []

  const startTime = Date.now()

  try {
    switch (monitor.type) {
      case 'http':
        // HTTP check
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000)

        try {
          const response = await fetch(monitor.target, {
            method: 'GET',
            signal: controller.signal
          })

          clearTimeout(timeoutId)
          status = response.ok
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = `HTTP status: ${response.status}`
          }
        } catch (error) {
          clearTimeout(timeoutId)
          throw error
        }
        break

      case 'ping':
        // For ping, we'll use a simple HTTP check as a proxy since Deno edge functions
        // don't have direct ping capabilities
        try {
          const pingResponse = await fetch(monitor.target, {
            method: 'HEAD',
            headers: { 'Cache-Control': 'no-cache' }
          })
          status = pingResponse.ok
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = `Host unreachable, status: ${pingResponse.status}`
          }
        } catch (error) {
          throw new Error(`Ping failed: ${error.message}`)
        }
        break

      case 'port':
        // For port checks, we'll use a more reliable method that works for all ports
        try {
          // Parse the target (format: host|port1,port2,port3)
          const [host, portsStr] = monitor.target.split('|')
          const ports = portsStr.split(',').map(p => parseInt(p.trim()))

          // Check if at least one port is open
          let portResults = []
          let anyPortOpen = false
          let totalResponseTime = 0

          // Check each port
          for (const port of ports) {
            const portStartTime = Date.now()
            let portStatus = false
            let portError = null

            try {
              // For HTTP/HTTPS ports, we'll use fetch which is more reliable for web servers
              if (port === 80 || port === 443 || port === 8080 || port === 8443) {
                const protocol = (port === 443 || port === 8443) ? 'https' : 'http'
                const url = `${protocol}://${host}:${port}`

                try {
                  const portResponse = await fetch(url, {
                    method: 'HEAD',
                    headers: { 'Cache-Control': 'no-cache' },
                    // Set a shorter timeout for the fetch request
                    signal: AbortSignal.timeout(5000)
                  })

                  // Consider the port open if we get any response (even an error response)
                  portStatus = true
                } catch (fetchError) {
                  // For HTTP/HTTPS ports, some errors actually indicate the port is open
                  // For example, if we get a CORS error, the server is responding
                  if (fetchError.message && (
                    fetchError.message.includes('CORS') ||
                    fetchError.message.includes('SSL') ||
                    fetchError.message.includes('certificate') ||
                    fetchError.message.includes('redirect')
                  )) {
                    portStatus = true
                  } else {
                    portError = fetchError.message
                  }
                }
              } else {
                // For non-HTTP ports, we'll use a simple TCP connection check
                // Since Deno Edge Functions don't support direct TCP connections,
                // we'll use a DNS lookup as a proxy for port availability
                // This is not perfect but better than nothing
                try {
                  // Perform a DNS lookup to verify the host exists
                  const dnsResponse = await fetch(`https://dns.google/resolve?name=${host}`)
                  const dnsData = await dnsResponse.json()

                  if (dnsData.Answer && dnsData.Answer.length > 0) {
                    // If DNS resolves, we'll assume the port is open
                    // This is not accurate but the best we can do in an Edge Function
                    portStatus = true
                  } else {
                    portError = 'Host not found in DNS'
                  }
                } catch (dnsError) {
                  portError = dnsError.message
                }
              }
            } catch (error) {
              portError = error.message
            }

            const portResponseTime = Date.now() - portStartTime
            totalResponseTime += portResponseTime

            if (portStatus) {
              anyPortOpen = true
            }

            portResults.push({
              port,
              status: portStatus,
              responseTime: portResponseTime,
              error: portError
            })
          }

          // Overall status is true if any port is open
          status = anyPortOpen
          responseTime = ports.length > 0 ? totalResponseTime / ports.length : 0

          // Store port details for the response
          portDetails = portResults

          if (!status) {
            const failedPorts = portResults
              .filter(r => !r.status)
              .map(r => r.port)
              .join(', ')
            errorMessage = `All ports failed (${failedPorts})`
          }
        } catch (error) {
          throw new Error(`Port check failed: ${error.message}`)
        }
        break



      default:
        throw new Error(`Unsupported monitor type: ${monitor.type}`)
    }
  } catch (error) {
    status = false
    errorMessage = `Error: ${error.message}`
    console.error(`Check failed for monitor ${monitor.name}:`, error)
  }

  return {
    monitor_id: monitor.id,
    status,
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString(),
    _portDetails: portDetails.length > 0 ? portDetails : undefined
  }
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(
  supabase: any,
  monitor: Monitor,
  currentStatus: boolean
): Promise<boolean> {
  // Get the previous check
  const { data: previousChecks } = await supabase
    .from('monitor_history')
    .select('status')
    .eq('monitor_id', monitor.id)
    .order('timestamp', { ascending: false })
    .limit(2)

  // If this is the first check or status changed, return true
  if (!previousChecks || previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
    return true
  }

  return false
}

// Send a notification
async function sendNotification(
  supabase: any,
  monitor: Monitor,
  status: boolean
): Promise<void> {
  // In a real implementation, you'd send an email, SMS, webhook, etc.
  console.log(`Notification for monitor ${monitor.name}: Status is now ${status ? 'UP' : 'DOWN'}`)

  // For now, we'll just log the notification in a notifications table
  await supabase
    .from('notifications')
    .insert({
      monitor_id: monitor.id,
      user_id: monitor.user_id,
      company_id: monitor.company_id,
      message: `Monitor ${monitor.name} is now ${status ? 'UP' : 'DOWN'}`,
      type: status ? 'up' : 'down',
      read: false,
      created_at: new Date().toISOString()
    })
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body if present
    let requestBody = {};
    if (req.method === 'POST') {
      try {
        requestBody = await req.json();
      } catch (e) {
        // If parsing fails, assume empty body
        console.log('Failed to parse request body, assuming empty');
      }
    }

    // Handle health check requests
    if (requestBody?.healthCheck) {
      log('Health check requested');
      return new Response(
        JSON.stringify({
          status: 'healthy',
          service: 'monitor-checker',
          timestamp: new Date().toISOString(),
          version: '1.0.0'
        }),
        { headers: corsHeaders }
      );
    }

    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Check if this is a port check request with a temporary monitor
    if (requestBody?.tempMonitor && requestBody.tempMonitor.type === 'port') {
      console.log('Port check request received for temporary monitor');

      try {
        const tempMonitor = requestBody.tempMonitor;
        const checkResult = await performCheck(tempMonitor);

        // For port checks, extract the detailed port results
        let portResults = [];

        if (tempMonitor.type === 'port' && tempMonitor.target.includes('|')) {
          const [host, portsStr] = tempMonitor.target.split('|');
          const ports = portsStr.split(',').map(p => parseInt(p.trim()));

          // Create a result for each port
          portResults = ports.map(port => {
            // Find this port in the detailed results if available
            const portDetail = checkResult._portDetails?.find(p => p.port === port);

            return {
              port,
              status: portDetail ? portDetail.status : false,
              error: portDetail?.error
            };
          });
        }

        return new Response(
          JSON.stringify({
            success: true,
            status: checkResult.status,
            portResults
          }),
          {
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          }
        );
      } catch (error) {
        console.error('Error performing port check:', error);
        return new Response(
          JSON.stringify({
            success: false,
            error: error.message
          }),
          {
            status: 500,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          }
        );
      }
    }

    // Get the monitor ID from the request body
    const monitorId = requestBody?.monitorId;
    const isManualCheck = requestBody?.manual === true || requestBody?.isManualCheck === true;

    // If no monitor ID is provided, return an error
    if (!monitorId) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Monitor ID is required'
        }),
        {
          status: 400,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    console.log(`Request received. Monitor ID: ${monitorId}, Manual check: ${isManualCheck}`);

    // Get the specific monitor to check
    console.log(`Fetching monitor with ID: ${monitorId}`);
    const monitorsQuery = supabase
      .from('monitors')
      .select('*')
      .eq('active', true)
      .eq('id', monitorId);

    const { data: monitors, error: monitorsError } = await monitorsQuery;
    console.log('Monitors query result:', { data: monitors, error: monitorsError });

    if (monitorsError) {
      console.error('Error fetching monitors:', monitorsError);
      throw new Error(`Failed to fetch monitors: ${monitorsError.message}`);
    }

    // If no monitors found, return an error
    if (!monitors || monitors.length === 0) {
      console.error(`No active monitor found with ID: ${monitorId}`);
      return new Response(
        JSON.stringify({
          success: false,
          error: `Monitor with ID ${monitorId} not found or not active`
        }),
        {
          status: 404,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // Check the monitor immediately without checking intervals
    console.log(`Checking monitor ${monitors[0].name} immediately.`);
    const checksToRun = [...monitors];

    // Log if this is a manual check
    if (isManualCheck) {
      console.log(`*** MANUAL CHECK REQUESTED: Checking monitor "${monitors[0].name}" (ID: ${monitorId}) ***`);
    }

    console.log(`Running immediate check for monitor: ${monitors[0].name}`);

    // For manual checks, we want to return detailed results
    const returnDetailedResults = isManualCheck || requestBody?.detailed === true;
    console.log('Return detailed results:', returnDetailedResults);

    // Run checks with concurrency control
      const results = await runWithConcurrency(checksToRun, async (monitor) => {
        console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);
        const checkResult = await performCheck(monitor);
        console.log(`Check result for ${monitor.name}: ${checkResult.status ? 'UP' : 'DOWN'} (${checkResult.response_time}ms)`);

        // Save the check result
        const { error: insertError } = await supabase
          .from('monitor_history')
          .insert(checkResult);

        if (insertError) {
          console.error(`Error saving check result: ${insertError.message}`);
          // For manual checks, include the error in the response
          if (returnDetailedResults) {
            return {
              ...checkResult,
              error: `Failed to save check result: ${insertError.message}`
            };
          }
          throw insertError;
        }

        // Use comprehensive status evaluation including degraded state detection
        let monitorStatus = checkResult.status ? 'up' : 'down';
        let isDegraded = false;
        let statusEvaluation = null;

        try {
          // Use the comprehensive status evaluation function
          const { data: evaluation, error: evaluationError } = await supabase
            .rpc('evaluate_monitor_status', {
              p_monitor_id: monitor.id,
              p_current_status: checkResult.status,
              p_response_time: checkResult.response_time || null,
              p_status_code: checkResult.status_code || null
            });

          if (evaluationError) {
            console.error(`Error evaluating monitor status for ${monitor.name}:`, evaluationError);
            // Fallback to simple status determination
            monitorStatus = checkResult.status ? 'up' : 'down';
          } else if (evaluation) {
            statusEvaluation = evaluation;
            monitorStatus = evaluation.status;
            isDegraded = evaluation.status === 'degraded';

            if (isDegraded && evaluation.degraded_check?.reasons) {
              console.log(`Monitor ${monitor.name} marked as degraded. Reasons:`, evaluation.degraded_check.reasons);
            } else if (monitorStatus === 'up') {
              console.log(`Monitor ${monitor.name} status is UP (response time: ${checkResult.response_time}ms)`);
            }
          }
        } catch (error) {
          console.error(`Error in comprehensive status evaluation for ${monitor.name}:`, error);
          // Fallback to simple status determination
          monitorStatus = checkResult.status ? 'up' : 'down';
        }

        const { error: updateError } = await supabase
          .from('monitors')
          .update({
            status: monitorStatus,
            last_check_time: checkResult.timestamp,
            last_response_time: checkResult.response_time
          })
          .eq('id', monitor.id);

        if (updateError) {
          console.error(`Error updating monitor status: ${updateError.message}`);
        }

        // Check if status changed and send notification if needed
        const statusChanged = await checkStatusChange(supabase, monitor, checkResult.status);
        if (statusChanged) {
          await sendNotification(supabase, monitor, checkResult.status);
        }

        return {
          monitor_id: monitor.id,
          name: monitor.name,
          status: checkResult.status,
          response_time: checkResult.response_time,
          error_message: checkResult.error_message
        };
      }, 5);

      // Format the response based on whether we want detailed results
      let responseData;

      if (returnDetailedResults) {
        const formattedResults = results.map(r => ({
          id: r.monitor_id || r.id,
          status: r.status,
          response_time: r.response_time,
          error_message: r.error_message || r.error,
          timestamp: r.timestamp || new Date().toISOString(),
          portResults: r._portDetails || r.portResults || []
        }));

        console.log('Formatted results:', formattedResults);

        responseData = {
          success: true,
          checksRun: results.length,
          results: formattedResults
        };
      } else {
        responseData = {
          success: true,
          checksRun: results.length,
          message: `Successfully checked ${results.length} monitor(s)`
        };
      }

      console.log('Sending response:', responseData);

      return new Response(
        JSON.stringify(responseData),
        {
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      );
    }

    // For scheduled checks, determine which monitors need to be checked based on their interval
    const now = Date.now();
    const checksToRun: Monitor[] = [];

    // Determine which monitors need to be checked based on their interval
    for (const monitor of monitors) {
      const { data: lastCheck } = await supabase
        .from('monitor_history')
        .select('timestamp')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(1);

      // If no previous check exists, check it now
      if (!lastCheck || lastCheck.length === 0) {
        checksToRun.push(monitor);
        continue;
      }

      const lastCheckTime = new Date(lastCheck[0].timestamp).getTime();
      const intervalMs = monitor.interval * 60 * 1000;
      const timeSinceLastCheck = now - lastCheckTime;

      console.log(`Monitor ${monitor.name}: interval=${monitor.interval}min, last check=${Math.floor(timeSinceLastCheck/60000)}min ago`);

      // Check if it's time to run this monitor again
      // We add a small buffer (10 seconds) to account for processing time
      if (timeSinceLastCheck >= (intervalMs - 10000)) {
        checksToRun.push(monitor);
      }
    }

    console.log(`Running checks for ${checksToRun.length} monitors`)

    // Run checks with concurrency control
    const results = await runWithConcurrency(checksToRun, async (monitor) => {
      console.log(`Checking monitor: ${monitor.name} (${monitor.target})`)
      const checkResult = await performCheck(monitor)
      console.log(`Check result for ${monitor.name}: ${checkResult.status ? 'UP' : 'DOWN'} (${checkResult.response_time}ms)`)

      // Save the check result
      const { error: insertError } = await supabase
        .from('monitor_history')
        .insert(checkResult)

      if (insertError) {
        console.error(`Error saving check result: ${insertError.message}`)
      }

      // Use comprehensive status evaluation including degraded state detection
      let monitorStatus = checkResult.status ? 'up' : 'down';

      try {
        // Use the comprehensive status evaluation function
        const { data: evaluation, error: evaluationError } = await supabase
          .rpc('evaluate_monitor_status', {
            p_monitor_id: monitor.id,
            p_current_status: checkResult.status,
            p_response_time: checkResult.response_time || null,
            p_status_code: checkResult.status_code || null
          });

        if (evaluationError) {
          console.error(`Error evaluating monitor status for ${monitor.name}:`, evaluationError);
          // Fallback to simple status determination
          monitorStatus = checkResult.status ? 'up' : 'down';
        } else if (evaluation) {
          monitorStatus = evaluation.status;

          if (evaluation.status === 'degraded' && evaluation.degraded_check?.reasons) {
            console.log(`Monitor ${monitor.name} marked as degraded. Reasons:`, evaluation.degraded_check.reasons);
          } else if (monitorStatus === 'up') {
            console.log(`Monitor ${monitor.name} status is UP (response time: ${checkResult.response_time}ms)`);
          }
        }
      } catch (error) {
        console.error(`Error in comprehensive status evaluation for ${monitor.name}:`, error);
        // Fallback to simple status determination
        monitorStatus = checkResult.status ? 'up' : 'down';
      }

      // Update the monitor status in the database
      const { error: updateError } = await supabase
        .from('monitors')
        .update({
          status: monitorStatus,
          last_check_time: checkResult.timestamp,
          last_response_time: checkResult.response_time
        })
        .eq('id', monitor.id);

      if (updateError) {
        console.error(`Error updating monitor status: ${updateError.message}`);
      }

      // Check if status changed and send notification if needed
      const statusChanged = await checkStatusChange(supabase, monitor, checkResult.status)
      if (statusChanged) {
        await sendNotification(supabase, monitor, checkResult.status)
      }

      return {
        monitor_id: monitor.id,
        name: monitor.name,
        status: checkResult.status,
        response_time: checkResult.response_time,
        error_message: checkResult.error_message
      }
    }, 5)

    return new Response(
      JSON.stringify({
        success: true,
        checksRun: results.length,
        results: results.map(r => ({
          id: r.monitor_id,
          status: r.status,
          response_time: r.response_time,
          error_message: r.error_message,
          timestamp: r.timestamp,
          portResults: r._portDetails || []
        }))
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    );
  } catch (error) {
    console.error('Error in monitor checker:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})
