-- This script sets up the final policies for the company-related tables
-- Run this in the Supabase SQL Editor after running the fix_policies.sql and create_user_company.sql scripts

-- Drop the temporary policies
DROP POLICY IF EXISTS "Temp: Allow all operations for authenticated users" ON public.companies;
DROP POLICY IF EXISTS "Temp: Allow all operations for authenticated users" ON public.company_members;
DROP POLICY IF EXISTS "Temp: Allow all operations for authenticated users" ON public.monitors;

-- Create policies for companies table
CREATE POLICY "Users can view companies they are members of"
ON public.companies
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can update their companies"
ON public.companies
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Company admins can delete their companies"
ON public.companies
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Authenticated users can create companies"
ON public.companies
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Create policies for company_members table
CREATE POLICY "Users can view company members for their companies"
ON public.company_members
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members AS cm
        WHERE cm.company_id = company_members.company_id
        AND cm.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can manage company members"
ON public.company_members
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.company_members AS cm
        WHERE cm.company_id = company_members.company_id
        AND cm.user_id = auth.uid()
        AND cm.role = 'admin'
    )
);

-- Create policies for monitors table
CREATE POLICY "Users can view monitors in their companies"
ON public.monitors
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
    )
    OR
    -- Allow users to view their own monitors that don't have a company_id yet
    (monitors.user_id = auth.uid() AND (monitors.company_id IS NULL OR monitors.company_id::text = ''))
);

CREATE POLICY "Company admins can create monitors"
ON public.monitors
FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
    OR
    -- Allow users to create monitors without a company_id
    (monitors.company_id IS NULL OR monitors.company_id::text = '')
);

CREATE POLICY "Company admins can update monitors"
ON public.monitors
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
    OR
    -- Allow users to update their own monitors that don't have a company_id yet
    (monitors.user_id = auth.uid() AND (monitors.company_id IS NULL OR monitors.company_id::text = ''))
);

CREATE POLICY "Company admins can delete monitors"
ON public.monitors
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
    OR
    -- Allow users to delete their own monitors that don't have a company_id yet
    (monitors.user_id = auth.uid() AND (monitors.company_id IS NULL OR monitors.company_id::text = ''))
);
