import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { TableCell, TableRow } from '@/components/ui/table';
import { Building, Edit, Shield, ShieldOff, Trash2, User, UserCog, X } from 'lucide-react';
import { UserWithRoles } from '@/hooks/use-users';
import { getUserInitials, isCurrentUser } from '../utils/userUtils';

interface UserTableRowProps {
  user: UserWithRoles;
  currentUserId: string | undefined;
  setEditUserFormValues: (user: UserWithRoles) => void;
  handleToggleSuperadminStatus: (userId: string, isSuperadmin: boolean) => Promise<void>;
  handleRemoveUserFromCompany: (userId: string, companyId: string) => Promise<void>;
  setSelectedUser: (user: UserWithRoles) => void;
  setDeleteUserDialogOpen: (open: boolean) => void;
  setAssignCompanyDialogOpen: (open: boolean) => void;
}

const UserTableRow: React.FC<UserTableRowProps> = ({
  user,
  currentUserId,
  setEditUserFormValues,
  handleToggleSuperadminStatus,
  handleRemoveUserFromCompany,
  setSelectedUser,
  setDeleteUserDialogOpen,
  setAssignCompanyDialogOpen,
}) => {
  const isCurrentUserRow = isCurrentUser(currentUserId, user.id);

  return (
    <TableRow key={user.id}>
      <TableCell>
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            {user.avatar_url ? (
              <AvatarImage src={user.avatar_url} alt={user.full_name || user.email} />
            ) : null}
            <AvatarFallback>
              {getUserInitials(user.full_name || '', user.email)}
            </AvatarFallback>
          </Avatar>
          <div>
            {user.full_name && <div className="font-medium">{user.full_name}</div>}
            <div className="text-sm text-slate-500">{user.email}</div>
            {isCurrentUserRow && (
              <Badge variant="outline" className="mt-1 text-xs">You</Badge>
            )}
          </div>
        </div>
      </TableCell>
      <TableCell>
        {user.companies.length === 0 ? (
          <span className="text-slate-500 text-sm">No companies</span>
        ) : (
          <div className="flex flex-wrap gap-1">
            {user.companies.map((company) => (
              <Badge
                key={company.company_id}
                variant="outline"
                className="flex items-center gap-1"
              >
                {company.role_type === 'admin' ? (
                  <UserCog className="h-3 w-3 mr-1" />
                ) : (
                  <User className="h-3 w-3 mr-1" />
                )}
                {company.company_name}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                  onClick={() => handleRemoveUserFromCompany(user.id, company.company_id)}
                  title="Remove from company"
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            ))}
          </div>
        )}
      </TableCell>
      <TableCell>
        {user.is_superadmin ? (
          <Badge className="bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
            Superadmin
          </Badge>
        ) : (
          <span className="text-slate-500 text-sm">Regular User</span>
        )}
      </TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSelectedUser(user);
              setAssignCompanyDialogOpen(true);
            }}
            title="Assign to company"
          >
            <Building className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setEditUserFormValues(user)}
            title="Edit user"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleToggleSuperadminStatus(user.id, user.is_superadmin)}
            title={user.is_superadmin ? "Revoke superadmin" : "Grant superadmin"}
            disabled={isCurrentUserRow}
          >
            {user.is_superadmin ? (
              <ShieldOff className="h-4 w-4 text-red-500" />
            ) : (
              <Shield className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setSelectedUser(user);
              setDeleteUserDialogOpen(true);
            }}
            disabled={isCurrentUserRow}
            title="Delete user"
          >
            <Trash2 className="h-4 w-4 text-red-500" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
};

export default UserTableRow;
