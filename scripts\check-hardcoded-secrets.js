/**
 * <PERSON><PERSON><PERSON> to check for hardcoded Supabase secrets in the codebase
 *
 * This script scans the codebase for potentially hardcoded Supabase secrets
 * and warns about files that might contain sensitive information.
 *
 * Usage: node scripts/check-hardcoded-secrets.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get the current directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const projectRoot = path.resolve(__dirname, '..');
dotenv.config({ path: path.join(projectRoot, '.env') });

// Function to build patterns from environment variables
function buildSecretPatterns() {
  const patterns = [];

  // Generic URL pattern for Supabase
  patterns.push(/https:\/\/[a-zA-Z0-9-]+\.supabase\.co/i);

  // Generic patterns for sensitive variable assignments
  patterns.push(/SUPABASE_KEY\s*=\s*['"][^'"]+['"]/i);
  patterns.push(/SUPABASE_ANON_KEY\s*=\s*['"][^'"]+['"]/i);
  patterns.push(/SUPABASE_SERVICE_KEY\s*=\s*['"][^'"]+['"]/i);
  patterns.push(/RESEND_API_KEY\s*=\s*['"][^'"]+['"]/i);
  patterns.push(/DB_PASSWORD\s*=\s*['"][^'"]+['"]/i);

  // Add patterns for actual environment variable values if they exist
  // This helps catch instances where the actual secrets are hardcoded
  const sensitiveEnvVars = [
    'SUPABASE_URL',
    'SUPABASE_KEY',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_KEY',
    'RESEND_API_KEY',
    'DB_PASSWORD'
  ];

  for (const varName of sensitiveEnvVars) {
    const value = process.env[varName];
    if (value && value.length > 8) { // Only use values that are reasonably long
      // Escape special regex characters and create a pattern
      const escapedValue = value.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const pattern = new RegExp(escapedValue, 'i');
      patterns.push(pattern);
    }
  }

  return patterns;
}

// Get patterns based on environment variables
const SECRET_PATTERNS = buildSecretPatterns();

// Directories to exclude
const EXCLUDED_DIRS = [
  'node_modules',
  '.git',
  'dist',
  'build',
  'logs',
  'docs',
  'sql'
];

// Files to exclude
const EXCLUDED_FILES = [
  '.env',
  '.env.example',
  '.env.local.example',
  'SECURITY_BEST_PRACTICES.md',
  'MANUAL_DEPLOYMENT_INSTRUCTIONS.md',
  'RUNNING-WITH-NPM.md',
  'VUM_Project_Analysis.md',
  'README.md'
];

// File extensions to check
const FILE_EXTENSIONS = [
  '.js',
  '.ts',
  '.jsx',
  '.tsx',
  '.vue',
  '.json',
  '.md',
  '.html',
  '.css',
  '.scss',
  '.less'
];

// Function to check if a file should be scanned
function shouldScanFile(filePath) {
  const fileName = path.basename(filePath);
  const ext = path.extname(filePath);

  // Check if file is in excluded list
  if (EXCLUDED_FILES.includes(fileName)) {
    return false;
  }

  // Check if file has an extension we want to scan
  if (!FILE_EXTENSIONS.includes(ext)) {
    return false;
  }

  return true;
}

// Function to check if a directory should be scanned
function shouldScanDir(dirPath) {
  const dirName = path.basename(dirPath);

  // Check if directory is in excluded list
  if (EXCLUDED_DIRS.includes(dirName)) {
    return false;
  }

  return true;
}

// Function to scan a file for hardcoded secrets
function scanFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const matches = [];

    // Check each pattern
    for (const pattern of SECRET_PATTERNS) {
      const patternMatches = content.match(pattern);

      if (patternMatches) {
        matches.push({
          pattern: pattern.toString(),
          matches: patternMatches
        });
      }
    }

    return matches;
  } catch (error) {
    console.error(`Error reading file ${filePath}: ${error.message}`);
    return [];
  }
}

// Function to scan a directory recursively
function scanDir(dirPath, results = {}) {
  try {
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);

      if (entry.isDirectory()) {
        // Scan subdirectory if not excluded
        if (shouldScanDir(fullPath)) {
          scanDir(fullPath, results);
        }
      } else if (entry.isFile()) {
        // Scan file if not excluded
        if (shouldScanFile(fullPath)) {
          const matches = scanFile(fullPath);

          if (matches.length > 0) {
            results[fullPath] = matches;
          }
        }
      }
    }

    return results;
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}: ${error.message}`);
    return results;
  }
}

// Main function
function main() {
  console.log('Scanning for hardcoded Supabase secrets...');

  // Get the project root directory (parent of scripts directory)
  console.log(`Scanning directory: ${projectRoot}`);

  // Start scan from project root
  const results = scanDir(projectRoot);

  // Print results
  const fileCount = Object.keys(results).length;

  if (fileCount === 0) {
    console.log('\nNo hardcoded secrets found! 🎉');
    return 0; // Success exit code
  } else {
    console.log(`\nFound potential hardcoded secrets in ${fileCount} files:`);

    for (const [filePath, matches] of Object.entries(results)) {
      const relativePath = path.relative(projectRoot, filePath);
      console.log(`\n📁 ${relativePath}`);

      for (const match of matches) {
        console.log(`  - Pattern: ${match.pattern}`);
        console.log(`    Matches: ${match.matches.length}`);
      }
    }

    console.log('\n⚠️ WARNING: The files above may contain hardcoded secrets!');
    console.log('Please review these files and ensure all secrets are moved to environment variables.');

    // Check if we should ignore certain files in pre-commit hook
    // This allows documentation files to be committed even with examples
    const ignoredPatterns = [
      /\.md$/,                // Markdown files
      /\.example$/,           // Example files
      /scripts\/check-hardcoded-secrets\.js$/, // This script itself
      /scripts\/check-credentials\.js$/       // Credentials check script
    ];

    // Filter out ignored files
    const criticalFiles = Object.keys(results).filter(filePath => {
      const relativePath = path.relative(projectRoot, filePath);
      return !ignoredPatterns.some(pattern => pattern.test(relativePath));
    });

    if (criticalFiles.length > 0) {
      console.log('\n❌ ERROR: Hardcoded secrets found in critical files!');
      console.log('Please fix these issues before committing.');
      return 1; // Error exit code
    } else {
      console.log('\n⚠️ WARNING: Hardcoded secrets found only in documentation or example files.');
      console.log('You may proceed with the commit, but consider removing these examples in the future.');
      return 0; // Allow commit to proceed
    }
  }
}

// Run the script
const exitCode = main();
process.exit(exitCode);
