/**
 * VUM - Vurbis Uptime Monitor
 * API Integration
 */

// Supabase configuration
// These values should be set in the .env file and loaded by the build process
// For development, you can set them in the dashboard/.env file
let SUPABASE_URL;
let SUPABASE_KEY;

// Try to get from environment variables if available (when built with a bundler)
if (typeof process !== 'undefined' && process.env) {
    SUPABASE_URL = process.env.SUPABASE_URL;
    SUPABASE_KEY = process.env.SUPABASE_ANON_KEY;
}

// For direct browser usage, try to get from a config object that should be set by the HTML page
if ((!SUPABASE_URL || !SUPABASE_KEY) && typeof window !== 'undefined' && window.VUM_CONFIG) {
    SUPABASE_URL = window.VUM_CONFIG.SUPABASE_URL;
    SUPABASE_KEY = window.VUM_CONFIG.SUPABASE_ANON_KEY;
}

// If still not set, show an error
if (!SUPABASE_URL || !SUPABASE_KEY) {
    console.error('ERROR: Supabase configuration is missing. Please set SUPABASE_URL and SUPABASE_ANON_KEY in your environment or in the VUM_CONFIG object.');
}

// Initialize Supabase client
let supabase = null;

/**
 * Initialize the API
 */
async function initializeAPI() {
    // Load Supabase from CDN if not already loaded
    if (typeof supabaseJs === 'undefined') {
        await loadScript('https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2');
    }

    // Initialize Supabase client
    supabase = supabaseJs.createClient(SUPABASE_URL, SUPABASE_KEY);

    console.log('API initialized');
    return supabase;
}

/**
 * Load a script dynamically
 */
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
}

/**
 * Get all monitors
 */
async function getMonitors() {
    if (!supabase) await initializeAPI();

    const { data, error } = await supabase
        .from('monitors')
        .select('*');

    if (error) {
        console.error('Error fetching monitors:', error);
        return [];
    }

    return data || [];
}

/**
 * Get monitor history
 */
async function getMonitorHistory(monitorId, limit = 100) {
    if (!supabase) await initializeAPI();

    const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitorId)
        .order('timestamp', { ascending: false })
        .limit(limit);

    if (error) {
        console.error('Error fetching monitor history:', error);
        return [];
    }

    return data || [];
}

/**
 * Get all monitor history for the last 24 hours
 */
async function getRecentHistory(hours = 24) {
    if (!supabase) await initializeAPI();

    const startTime = new Date();
    startTime.setHours(startTime.getHours() - hours);

    const { data, error } = await supabase
        .from('monitor_history')
        .select('*, monitors(name)')
        .gte('timestamp', startTime.toISOString())
        .order('timestamp', { ascending: false });

    if (error) {
        console.error('Error fetching recent history:', error);
        return [];
    }

    return data || [];
}

/**
 * Get notifications
 */
async function getNotifications(limit = 10) {
    if (!supabase) await initializeAPI();

    const { data, error } = await supabase
        .from('notifications')
        .select('*, monitors(name)')
        .order('created_at', { ascending: false })
        .limit(limit);

    if (error) {
        console.error('Error fetching notifications:', error);
        return [];
    }

    return data || [];
}

/**
 * Create a new monitor
 */
async function createMonitor(monitor) {
    if (!supabase) await initializeAPI();

    const { data, error } = await supabase
        .from('monitors')
        .insert(monitor)
        .select();

    if (error) {
        console.error('Error creating monitor:', error);
        return null;
    }

    return data[0] || null;
}

/**
 * Update a monitor
 */
async function updateMonitor(id, updates) {
    if (!supabase) await initializeAPI();

    const { data, error } = await supabase
        .from('monitors')
        .update(updates)
        .eq('id', id)
        .select();

    if (error) {
        console.error('Error updating monitor:', error);
        return null;
    }

    return data[0] || null;
}

/**
 * Delete a monitor
 */
async function deleteMonitor(id) {
    if (!supabase) await initializeAPI();

    const { error } = await supabase
        .from('monitors')
        .delete()
        .eq('id', id);

    if (error) {
        console.error('Error deleting monitor:', error);
        return false;
    }

    return true;
}

/**
 * Get monitor statistics
 */
async function getMonitorStats() {
    if (!supabase) await initializeAPI();

    // Get all monitors
    const { data: monitors, error: monitorsError } = await supabase
        .from('monitors')
        .select('*');

    if (monitorsError) {
        console.error('Error fetching monitors:', monitorsError);
        return {
            total: 0,
            up: 0,
            down: 0,
            uptime: 0,
            avgResponseTime: 0
        };
    }

    // Get latest status for each monitor
    const monitorPromises = monitors.map(async (monitor) => {
        const { data, error } = await supabase
            .from('monitor_history')
            .select('*')
            .eq('monitor_id', monitor.id)
            .order('timestamp', { ascending: false })
            .limit(1)
            .single();

        if (error && error.code !== 'PGRST116') {
            console.error(`Error fetching status for monitor ${monitor.id}:`, error);
            return { ...monitor, status: null };
        }

        return { ...monitor, status: data ? data.status : null };
    });

    const monitorsWithStatus = await Promise.all(monitorPromises);

    // Calculate statistics
    const total = monitorsWithStatus.length;
    const up = monitorsWithStatus.filter(m => m.status === true).length;
    const down = monitorsWithStatus.filter(m => m.status === false).length;

    // Get average uptime and response time
    const { data: historyData, error: historyError } = await supabase
        .from('monitor_history')
        .select('status, response_time')
        .gte('timestamp', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString());

    if (historyError) {
        console.error('Error fetching history data:', historyError);
        return {
            total,
            up,
            down,
            uptime: 0,
            avgResponseTime: 0
        };
    }

    const uptime = historyData.length > 0
        ? (historyData.filter(h => h.status).length / historyData.length) * 100
        : 0;

    const responseTimes = historyData
        .filter(h => h.response_time !== null)
        .map(h => h.response_time);

    const avgResponseTime = responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

    return {
        total,
        up,
        down,
        uptime: parseFloat(uptime.toFixed(1)),
        avgResponseTime: Math.round(avgResponseTime)
    };
}

// Export API functions
window.vumAPI = {
    initializeAPI,
    getMonitors,
    getMonitorHistory,
    getRecentHistory,
    getNotifications,
    createMonitor,
    updateMonitor,
    deleteMonitor,
    getMonitorStats
};
