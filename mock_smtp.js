import { SMTPServer } from 'smtp-server';

const mockServer = new SMTPServer({
    authOptional: true,
    onConnect(session, callback) {
        console.log('Client connected');
        callback();
    },
    onMailFrom(address, session, callback) {
        console.log('Mail from:', address.address);
        callback();
    },
    onRcptTo(address, session, callback) {
        console.log('Mail to:', address.address);
        callback();
    },
    onData(stream, session, callback) {
        let message = '';
        stream.on('data', chunk => {
            message += chunk;
        });
        stream.on('end', () => {
            console.log('\n=== Email Content ===');
            console.log('From:', session.envelope.mailFrom.address);
            console.log('To:', session.envelope.rcptTo.map(r => r.address).join(', '));
            console.log('Content:', message);
            console.log('===================\n');
            callback();
        });
    }
});

const port = 2525; // Using port 2525 for testing
mockServer.listen(port, () => {
    console.log(`Mock SMTP server running on port ${port}`);
});
