// This is a test script to check if we can upload and retrieve files from the avatars bucket
// You can run this in the browser console to test the avatar upload functionality

// Function to test avatar upload
async function testAvatarUpload() {
  console.log('Starting avatar upload test...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('User is logged in:', user);
  
  // Check if the avatars bucket exists
  console.log('Checking if avatars bucket exists...');
  const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
  
  if (bucketsError) {
    console.error('Error listing buckets:', bucketsError);
    return;
  }
  
  const avatarBucket = buckets.find(b => b.name === 'avatars');
  if (!avatarBucket) {
    console.error('Avatars bucket does not exist! Please run the supabase_storage_setup.sql script.');
    return;
  }
  
  console.log('Avatars bucket exists:', avatarBucket);
  
  // Create a simple test image (1x1 pixel transparent PNG)
  const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
  const byteCharacters = atob(base64Image);
  const byteNumbers = new Array(byteCharacters.length);
  
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  
  const byteArray = new Uint8Array(byteNumbers);
  const blob = new Blob([byteArray], { type: 'image/png' });
  const file = new File([blob], 'test-avatar.png', { type: 'image/png' });
  
  // Try to upload the test image
  console.log('Uploading test image...');
  const fileName = `${user.id}-test.png`;
  
  const { data: uploadData, error: uploadError } = await supabase.storage
    .from('avatars')
    .upload(fileName, file, { upsert: true });
  
  if (uploadError) {
    console.error('Error uploading test image:', uploadError);
    return;
  }
  
  console.log('Test image uploaded successfully:', uploadData);
  
  // Try to get the public URL
  console.log('Getting public URL...');
  const { data: urlData } = supabase.storage
    .from('avatars')
    .getPublicUrl(fileName);
  
  const publicUrl = urlData.publicUrl;
  console.log('Public URL:', publicUrl);
  
  // Try to load the image
  console.log('Testing if image is accessible...');
  const img = new Image();
  
  img.onload = () => {
    console.log('Image loaded successfully!');
    console.log('Avatar upload test PASSED ✅');
  };
  
  img.onerror = () => {
    console.error('Image failed to load. The URL might not be publicly accessible.');
    console.log('Avatar upload test FAILED ❌');
  };
  
  img.src = publicUrl;
  
  // Return the test results
  return {
    user,
    avatarBucket,
    uploadData,
    publicUrl,
    testImage: img
  };
}

// Run the test
testAvatarUpload().then(results => {
  console.log('Test completed. Results:', results);
});
