// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Helper function to run tasks with concurrency limit
async function runWithConcurrency(items, taskFn, concurrency = 5) {
  const results = [];
  const chunks = [];
  
  // Split items into chunks based on concurrency
  for(let i = 0; i < items.length; i += concurrency){
    chunks.push(items.slice(i, i + concurrency));
  }
  
  // Process each chunk sequentially, but items within a chunk in parallel
  for (const chunk of chunks){
    // Use Promise.allSettled to handle errors gracefully
    const chunkPromises = await Promise.allSettled(chunk.map(async (item)=>{
      try {
        return await taskFn(item);
      } catch (error) {
        console.error(`Error processing item:`, error);
        // Return a default error result if possible
        if (typeof item === 'object' && item !== null && 'id' in item) {
          return {
            monitor_id: item.id,
            name: item.name || 'Unknown',
            status: false,
            response_time: 0,
            error_message: `Processing error: ${error.message}`
          };
        }
        throw error // Re-throw if we can't create a default result
        ;
      }
    }));
    
    // Filter out rejected promises and add fulfilled ones to results
    for (const result of chunkPromises){
      if (result.status === 'fulfilled') {
        results.push(result.value);
      }
    }
  }
  
  return results;
}

// Perform a check on a monitor
async function performCheck(monitor) {
  let status = false;
  let responseTime = null;
  let errorMessage = null;
  let portDetails = [];
  const startTime = Date.now();
  
  try {
    switch(monitor.type){
      case 'http':
        // HTTP check
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), monitor.timeout * 1000);
        try {
          const response = await fetch(monitor.target, {
            method: 'GET',
            signal: controller.signal
          });
          clearTimeout(timeoutId);
          status = response.ok;
          responseTime = Date.now() - startTime;
          if (!status) {
            errorMessage = `HTTP status: ${response.status}`;
          }
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
        break;
      case 'ping':
        // For ping, we'll use a simple HTTP check as a proxy since Deno edge functions
        // don't have direct ping capabilities
        try {
          const pingResponse = await fetch(monitor.target, {
            method: 'HEAD',
            headers: {
              'Cache-Control': 'no-cache'
            }
          });
          status = pingResponse.ok;
          responseTime = Date.now() - startTime;
          if (!status) {
            errorMessage = `Host unreachable, status: ${pingResponse.status}`;
          }
        } catch (error) {
          throw new Error(`Ping failed: ${error.message}`);
        }
        break;
      case 'port':
        // For port checks, we'll use a more reliable method that works for all ports
        try {
          // Parse the target (format: host|port1,port2,port3)
          const [host, portsStr] = monitor.target.split('|');
          const ports = portsStr.split(',').map((p)=>parseInt(p.trim()));
          // Check if at least one port is open
          let portResults = [];
          let anyPortOpen = false;
          let totalResponseTime = 0;
          // Check each port
          for (const port of ports){
            const portStartTime = Date.now();
            let portStatus = false;
            let portError = null;
            try {
              // For HTTP/HTTPS ports, we'll use fetch which is more reliable for web servers
              if (port === 80 || port === 443 || port === 8080 || port === 8443) {
                const protocol = port === 443 || port === 8443 ? 'https' : 'http';
                const url = `${protocol}://${host}:${port}`;
                try {
                  const portResponse = await fetch(url, {
                    method: 'HEAD',
                    headers: {
                      'Cache-Control': 'no-cache'
                    },
                    // Set a shorter timeout for the fetch request
                    signal: AbortSignal.timeout(5000)
                  });
                  // Consider the port open if we get any response (even an error response)
                  portStatus = true;
                } catch (fetchError) {
                  // For HTTP/HTTPS ports, some errors actually indicate the port is open
                  // For example, if we get a CORS error, the server is responding
                  if (fetchError.message && (fetchError.message.includes('CORS') || fetchError.message.includes('SSL') || fetchError.message.includes('certificate') || fetchError.message.includes('redirect'))) {
                    portStatus = true;
                  } else {
                    portError = fetchError.message;
                  }
                }
              } else {
                // For non-HTTP ports, we'll use a simple TCP connection check
                // Since Deno Edge Functions don't support direct TCP connections,
                // we'll use a DNS lookup as a proxy for port availability
                try {
                  // Perform a DNS lookup to verify the host exists
                  const dnsResponse = await fetch(`https://dns.google/resolve?name=${host}`);
                  const dnsData = await dnsResponse.json();
                  if (dnsData.Answer && dnsData.Answer.length > 0) {
                    // If DNS resolves, we'll assume the port is open
                    // This is not accurate but the best we can do in an Edge Function
                    portStatus = true;
                  } else {
                    portError = 'Host not found in DNS';
                  }
                } catch (dnsError) {
                  portError = dnsError.message;
                }
              }
            } catch (error) {
              portError = error.message;
            }
            const portResponseTime = Date.now() - portStartTime;
            totalResponseTime += portResponseTime;
            if (portStatus) {
              anyPortOpen = true;
            }
            portResults.push({
              port,
              status: portStatus,
              responseTime: portResponseTime,
              error: portError
            });
          }
          // Overall status is true if any port is open
          status = anyPortOpen;
          responseTime = ports.length > 0 ? totalResponseTime / ports.length : 0;
          // Store port details for the response
          portDetails = portResults;
          if (!status) {
            const failedPorts = portResults.filter((r)=>!r.status).map((r)=>r.port).join(', ');
            errorMessage = `All ports failed (${failedPorts})`;
          }
        } catch (error) {
          throw new Error(`Port check failed: ${error.message}`);
        }
        break;
      default:
        throw new Error(`Unsupported monitor type: ${monitor.type}`);
    }
  } catch (error) {
    status = false;
    errorMessage = `Error: ${error.message}`;
    console.error(`Check failed for monitor ${monitor.name}:`, error);
  }
  
  return {
    monitor_id: monitor.id,
    status,
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString(),
    _portDetails: portDetails.length > 0 ? portDetails : undefined
  };
}
