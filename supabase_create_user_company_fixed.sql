-- This script creates a company for a specific user
-- Run this in the Supabase SQL Editor

-- This script uses your specific user ID

-- Check if the user already has a company
DO $$
DECLARE
    user_company_count INTEGER;
    specific_user_id UUID := '3a19a1f7-3f61-48a8-949c-caa3fba04924'; -- Your user ID
    new_company_id UUID;
    user_email TEXT;
BEGIN
    -- Get the user's email
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = specific_user_id;

    RAISE NOTICE 'User ID: %, Email: %', specific_user_id, user_email;

    -- Check if the user already has a company
    SELECT COUNT(*) INTO user_company_count
    FROM public.company_members
    WHERE user_id = specific_user_id;

    RAISE NOTICE 'User company count: %', user_company_count;

    -- If the user doesn't have a company, create one
    IF user_company_count = 0 THEN
        -- Create a default company for the user
        INSERT INTO public.companies (name, description)
        VALUES (CONCAT(user_email, '''s Company'), CONCAT('Default company for ', user_email))
        RETURNING id INTO new_company_id;

        RAISE NOTICE 'Created new company with ID: %', new_company_id;

        -- Add the user as an admin to the company
        INSERT INTO public.company_members (company_id, user_id, role)
        VALUES (new_company_id, specific_user_id, 'admin');

        RAISE NOTICE 'Added user % as admin to company %', specific_user_id, new_company_id;

        -- Update existing monitors to be associated with the company
        UPDATE public.monitors
        SET company_id = new_company_id
        WHERE user_id = specific_user_id AND (company_id IS NULL OR company_id::text = '');

        RAISE NOTICE 'Updated monitors for user % to company %', specific_user_id, new_company_id;
    ELSE
        RAISE NOTICE 'User % already has % companies', specific_user_id, user_company_count;
    END IF;
END $$;
