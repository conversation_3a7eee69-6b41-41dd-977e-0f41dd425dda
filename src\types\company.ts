// Company and company member types

// Role types that match the database enum
export type UserRole = 'user' | 'admin' | 'superadmin';

// For backward compatibility in code
export type CompanyRole = UserRole | 'member' | 'viewer';

// Global user role (only for superadmin)
export interface GlobalUserRole {
  id: string;
  user_id: string;
  role_type: UserRole;
  created_at: string;
  updated_at: string;
}

export interface Company {
  id: string;
  name: string;
  description: string | null;
  logo_url: string | null;
  created_at: string;
  updated_at: string;
}

export interface CompanyMember {
  id: string;
  company_id: string;
  user_id: string;
  role_type: UserRole;
  created_at: string;
  updated_at: string;
  // These fields are joined from the users table
  email?: string;
  full_name?: string;
  avatar_url?: string;
}

export interface CompanyWithMembers extends Company {
  members: CompanyMember[];
}

export interface CreateCompanyData {
  name: string;
  description?: string;
  logo_url?: string;
}

export interface UpdateCompanyData {
  name?: string;
  description?: string;
  logo_url?: string;
}

export interface AddCompanyMemberData {
  company_id: string;
  user_id: string;
  role_type: UserRole;
}

export interface UpdateCompanyMemberData {
  role_type: UserRole;
}
