import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { AlertCircle, CheckCircle, Clock, RefreshCw, ChevronDown, ChevronRight } from 'lucide-react';
import { performSystemHealthCheck, getHealthStatusDisplay, type SystemHealthStatus } from '@/services/health-check-service';

interface SystemHealthStatusProps {
  compact?: boolean;
  showDetails?: boolean;
}

export const SystemHealthStatus: React.FC<SystemHealthStatusProps> = ({ 
  compact = false, 
  showDetails = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const { 
    data: healthStatus, 
    isLoading, 
    error, 
    refetch,
    dataUpdatedAt 
  } = useQuery({
    queryKey: ['system-health'],
    queryFn: performSystemHealthCheck,
    refetchInterval: 60000, // Check every minute
    staleTime: 30000, // Consider data stale after 30 seconds
  });

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading && !healthStatus) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <RefreshCw className="h-4 w-4 animate-spin" />
        Checking system health...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 text-sm text-red-600">
        <AlertCircle className="h-4 w-4" />
        Health check failed
        <Button variant="ghost" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (!healthStatus) return null;

  const display = getHealthStatusDisplay(healthStatus);
  const lastUpdated = new Date(dataUpdatedAt).toLocaleTimeString();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'degraded':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'unhealthy':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {getStatusIcon(healthStatus.overall)}
        <Badge variant="outline" className={display.color}>
          {display.text}
        </Badge>
        {showDetails && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm">
                {isExpanded ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="absolute z-50 mt-2 p-4 bg-white border rounded-lg shadow-lg min-w-80">
              <div className="space-y-2">
                {healthStatus.services.map((service, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(service.status)}
                      <span>{service.service}</span>
                    </div>
                    <div className="text-right">
                      <div className={`text-xs px-2 py-1 rounded ${
                        service.status === 'healthy' ? 'bg-green-100 text-green-700' :
                        service.status === 'degraded' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-red-100 text-red-700'
                      }`}>
                        {service.status}
                      </div>
                      {service.responseTime && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {service.responseTime}ms
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                <div className="pt-2 border-t text-xs text-muted-foreground">
                  Last updated: {lastUpdated}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
        <Button variant="ghost" size="sm" onClick={handleRefresh} disabled={isLoading}>
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
        </Button>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            {getStatusIcon(healthStatus.overall)}
            System Health
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={display.color}>
              {display.text}
            </Badge>
            <Button variant="ghost" size="sm" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {healthStatus.services.map((service, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(service.status)}
                <div>
                  <div className="font-medium">{service.service}</div>
                  {service.error && (
                    <div className="text-sm text-red-600 mt-1">{service.error}</div>
                  )}
                  {service.details && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {typeof service.details === 'object' ? 
                        Object.entries(service.details).map(([key, value]) => (
                          <div key={key}>{key}: {String(value)}</div>
                        )) : 
                        String(service.details)
                      }
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm px-3 py-1 rounded-full ${
                  service.status === 'healthy' ? 'bg-green-100 text-green-700' :
                  service.status === 'degraded' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {service.status}
                </div>
                {service.responseTime && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {service.responseTime}ms
                  </div>
                )}
                {service.lastCheck && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {new Date(service.lastCheck).toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>
          ))}
          <div className="text-sm text-muted-foreground text-center pt-2 border-t">
            Last updated: {lastUpdated} • Auto-refresh every minute
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemHealthStatus;
