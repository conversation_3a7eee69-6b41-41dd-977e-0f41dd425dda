import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { AlertCircle, CheckCircle, Clock, RefreshCw, ChevronDown, ChevronRight } from 'lucide-react';
import { performSystemHealthCheck, getHealthStatusDisplay, type SystemHealthStatus } from '@/services/health-check-service';

interface SystemHealthStatusProps {
  compact?: boolean;
  showDetails?: boolean;
}

export const SystemHealthStatus: React.FC<SystemHealthStatusProps> = ({ 
  compact = false, 
  showDetails = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const { 
    data: healthStatus, 
    isLoading, 
    error, 
    refetch,
    dataUpdatedAt 
  } = useQuery({
    queryKey: ['system-health'],
    queryFn: performSystemHealthCheck,
    refetchInterval: 60000, // Check every minute
    staleTime: 30000, // Consider data stale after 30 seconds
  });

  const handleRefresh = () => {
    refetch();
  };

  if (isLoading && !healthStatus) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <RefreshCw className="h-4 w-4 animate-spin" />
        Checking system health...
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 text-sm text-red-600">
        <AlertCircle className="h-4 w-4" />
        Health check failed
        <Button variant="ghost" size="sm" onClick={handleRefresh}>
          <RefreshCw className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  if (!healthStatus) return null;

  const display = getHealthStatusDisplay(healthStatus);
  const lastUpdated = new Date(dataUpdatedAt).toLocaleTimeString();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'degraded':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'unhealthy':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  if (compact) {
    // Only show issues in compact mode
    if (healthStatus.overall === 'healthy') {
      // Temporarily show a debug message when healthy
      return (
        <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
          System Healthy (Debug)
        </div>
      );
    }

    // Get the most critical issue for the message
    const unhealthyServices = healthStatus.services?.filter(s => s.status === 'unhealthy') || [];
    const degradedServices = healthStatus.services?.filter(s => s.status === 'degraded') || [];

    let message = '';
    let linkText = '';

    if (unhealthyServices.length > 0) {
      const serviceNames = unhealthyServices.map(s => s.service).join(', ');
      message = `System Issues: ${serviceNames} unavailable`;
      linkText = 'View Details & Troubleshooting';
    } else if (degradedServices.length > 0) {
      const serviceNames = degradedServices.map(s => s.service).join(', ');
      message = `System Degraded: ${serviceNames} experiencing issues`;
      linkText = 'View Details';
    } else {
      message = 'System Issues Detected';
      linkText = 'View Details';
    }

    const statusColor = healthStatus.overall === 'degraded' ? 'text-yellow-700' : 'text-red-700';
    const bgColor = healthStatus.overall === 'degraded' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200';

    return (
      <div className={`flex items-center gap-3 px-3 py-2 rounded-lg border ${bgColor}`}>
        <span className={`${statusColor} text-sm font-medium`}>
          ⚠️ {message}
        </span>
        {showDetails && (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="text-xs">
                {linkText}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="absolute z-50 mt-2 p-4 bg-white dark:bg-gray-800 border rounded-lg shadow-lg min-w-96 right-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">System Health Details</h3>
                  <Button
                    onClick={handleRefresh}
                    variant="ghost"
                    size="sm"
                    className="h-6 px-2"
                    disabled={isLoading}
                  >
                    <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
                  </Button>
                </div>

                {/* Service Status */}
                <div className="space-y-2">
                  {healthStatus.services.map((service, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="font-medium">{service.service}</span>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          service.status === 'healthy' ? 'bg-green-100 text-green-700' :
                          service.status === 'degraded' ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'
                        }`}>
                          {service.status}
                        </span>
                        {service.responseTime && (
                          <span className="text-muted-foreground text-xs">
                            {service.responseTime}ms
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>

                {/* Issue Explanations */}
                {(unhealthyServices.length > 0 || degradedServices.length > 0) && (
                  <div className="space-y-3 pt-3 border-t">
                    <h4 className="font-medium text-sm">What this means:</h4>
                    {unhealthyServices.map((service) => (
                      <div key={service.service} className="text-sm space-y-1">
                        <div className="font-medium text-red-700">• {service.service} is unavailable</div>
                        <div className="text-gray-600 ml-4">
                          {service.service === 'Monitor Checker' && 'Monitor checks may not be running. New monitor data may be stale.'}
                          {service.service === 'Database' && 'Cannot connect to database. Monitor data may not be accessible.'}
                          {service.service === 'Monitor Activity' && 'No recent monitor checks detected. Monitoring may have stopped.'}
                          {service.error && <div className="text-red-600 text-xs mt-1">Error: {service.error}</div>}
                        </div>
                      </div>
                    ))}
                    {degradedServices.map((service) => (
                      <div key={service.service} className="text-sm space-y-1">
                        <div className="font-medium text-yellow-700">• {service.service} is slow</div>
                        <div className="text-gray-600 ml-4">
                          {service.service === 'Monitor Activity' && 'Monitor checks are running but less frequently than expected.'}
                          Response time: {service.responseTime}ms
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="text-xs text-muted-foreground pt-2 border-t">
                  Last updated: {lastUpdated}
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            {getStatusIcon(healthStatus.overall)}
            System Health
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className={display.color}>
              {display.text}
            </Badge>
            <Button variant="ghost" size="sm" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {healthStatus.services.map((service, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-3">
                {getStatusIcon(service.status)}
                <div>
                  <div className="font-medium">{service.service}</div>
                  {service.error && (
                    <div className="text-sm text-red-600 mt-1">{service.error}</div>
                  )}
                  {service.details && (
                    <div className="text-sm text-muted-foreground mt-1">
                      {typeof service.details === 'object' ? 
                        Object.entries(service.details).map(([key, value]) => (
                          <div key={key}>{key}: {String(value)}</div>
                        )) : 
                        String(service.details)
                      }
                    </div>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm px-3 py-1 rounded-full ${
                  service.status === 'healthy' ? 'bg-green-100 text-green-700' :
                  service.status === 'degraded' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {service.status}
                </div>
                {service.responseTime && (
                  <div className="text-sm text-muted-foreground mt-1">
                    {service.responseTime}ms
                  </div>
                )}
                {service.lastCheck && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {new Date(service.lastCheck).toLocaleTimeString()}
                  </div>
                )}
              </div>
            </div>
          ))}
          <div className="text-sm text-muted-foreground text-center pt-2 border-t">
            Last updated: {lastUpdated} • Auto-refresh every minute
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemHealthStatus;
