import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';

interface ErrorDisplayProps {
  error: any;
  refetch: () => void;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ error, refetch }) => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-6">User Management</h1>

      <Card>
        <CardHeader>
          <CardTitle className="text-red-500">Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Failed to load users. This might be due to permission issues.</p>
          <p className="text-sm text-slate-500 mt-2">
            {error instanceof Error ? error.message : 'Unknown error'}
          </p>
          <div className="mt-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
            <p className="text-amber-800 text-sm">
              <strong>Troubleshooting:</strong> If you're seeing a permission error, it might be because:
            </p>
            <ul className="list-disc ml-5 mt-2 text-sm text-amber-700">
              <li>Your superadmin role is not properly set up</li>
              <li>There's an issue with the database functions</li>
              <li>The RLS policies need to be updated</li>
            </ul>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={() => refetch()} className="mr-2">
            <Loader2 className="h-4 w-4 mr-2" />
            Retry
          </Button>
          <Button variant="outline" onClick={() => navigate('/dashboard')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ErrorDisplay;
