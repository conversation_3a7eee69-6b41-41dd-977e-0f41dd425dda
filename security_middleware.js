import rateLimit from 'express-rate-limit';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import fs from 'fs/promises';
import path from 'path';

dotenv.config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Custom rate limiter implementation
const ipRequestCounts = new Map();
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes
const MAX_REQUESTS = 100;

export const rateLimiter = (req, res, next) => {
    const ip = req.ip;
    const now = Date.now();
    
    // Initialize or clean up old entries
    if (!ipRequestCounts.has(ip)) {
        ipRequestCounts.set(ip, []);
    }
    
    // Clean up requests outside the window
    const requests = ipRequestCounts.get(ip);
    const validRequests = requests.filter(timestamp => now - timestamp < WINDOW_MS);
    
    if (validRequests.length >= MAX_REQUESTS) {
        res.status(429).json({
            message: 'Too many requests from this IP, please try again later',
            retryAfter: Math.ceil((WINDOW_MS - (now - validRequests[0])) / 1000)
        });
        return;
    }
    
    // Add current request
    validRequests.push(now);
    ipRequestCounts.set(ip, validRequests);
    
    next();
};

// Cleanup old entries periodically
setInterval(() => {
    const now = Date.now();
    for (const [ip, requests] of ipRequestCounts.entries()) {
        const validRequests = requests.filter(timestamp => now - timestamp < WINDOW_MS);
        if (validRequests.length === 0) {
            ipRequestCounts.delete(ip);
        } else {
            ipRequestCounts.set(ip, validRequests);
        }
    }
}, WINDOW_MS);

// IP Whitelist management
const WHITELIST_FILE = path.join(process.cwd(), 'ip_whitelist.json');

async function loadWhitelist() {
  try {
    const content = await fs.readFile(WHITELIST_FILE, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    // If file doesn't exist, create it with empty whitelist
    await fs.writeFile(WHITELIST_FILE, JSON.stringify({ ips: [] }));
    return { ips: [] };
  }
}

async function saveWhitelist(whitelist) {
  await fs.writeFile(WHITELIST_FILE, JSON.stringify(whitelist, null, 2));
}

export async function addToWhitelist(ip) {
  const whitelist = await loadWhitelist();
  if (!whitelist.ips.includes(ip)) {
    whitelist.ips.push(ip);
    await saveWhitelist(whitelist);
  }
}

export async function removeFromWhitelist(ip) {
  const whitelist = await loadWhitelist();
  whitelist.ips = whitelist.ips.filter(whitelistedIp => whitelistedIp !== ip);
  await saveWhitelist(whitelist);
}

export async function isIpWhitelisted(ip) {
  const whitelist = await loadWhitelist();
  return whitelist.ips.includes(ip);
}

// Request logging middleware
export async function requestLogger(req, res, next) {
  const startTime = Date.now();
  
  // Capture the original end function
  const originalEnd = res.end;
  
  // Override the end function to log the response
  res.end = async function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Log the request details
    const logEntry = {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.url,
      ip: req.ip,
      statusCode: res.statusCode,
      responseTime,
      userAgent: req.headers['user-agent'],
      referrer: req.headers.referer || req.headers.referrer,
      authenticated: !!req.headers['authorization']
    };

    try {
      // Log to Supabase
      const { error } = await supabase
        .from('request_logs')
        .insert([logEntry]);

      if (error) {
        console.error('Error logging request:', error);
      }

      // Also log to file as backup
      const logFile = path.join(process.cwd(), 'requests.log');
      await fs.appendFile(logFile, JSON.stringify(logEntry) + '\n');
    } catch (error) {
      console.error('Error in request logging:', error);
    }

    // Call the original end function
    originalEnd.apply(res, arguments);
  };

  next();
}

// SQL function to clean up old logs
export async function setupLogCleanup() {
  try {
    const { error } = await supabase.rpc('execute_sql', {
      query_text: `
        CREATE OR REPLACE FUNCTION cleanup_old_logs()
        RETURNS void
        LANGUAGE plpgsql
        SECURITY DEFINER
        AS $$
        BEGIN
          DELETE FROM request_logs
          WHERE timestamp < NOW() - INTERVAL '30 days';
        END;
        $$;

        -- Create a cron job to run cleanup daily
        SELECT cron.schedule(
          'cleanup-old-logs',
          '0 0 * * *',
          $$SELECT cleanup_old_logs()$$
        );
      `
    });

    if (error) {
      console.error('Error setting up log cleanup:', error);
    }
  } catch (error) {
    console.error('Error in setupLogCleanup:', error);
  }
}
