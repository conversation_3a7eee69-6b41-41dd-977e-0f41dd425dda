import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { useQueryClient } from '@tanstack/react-query';
import { useCompanies } from '@/hooks/use-companies';
import {
  Company,
  CompanyMember,
  CompanyRole
} from '@/types/company';

interface CompanyContextType {
  currentCompany: Company | null;
  setCurrentCompany: (company: Company | null) => void;
  isAdmin: boolean;
  getUserRole: (companyId: string) => CompanyRole | null;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const CompanyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const companyRoles = useCompanyRoles();
  const { useGlobalSuperadminQuery } = companyRoles;
  const { data: isGlobalSuperadmin = false } = useGlobalSuperadminQuery();
  const queryClient = useQueryClient();
  const { useGetCompaniesQuery, useGetCompanyMembersQuery } = useCompanies();

  // Get companies and company members using React Query
  const { data: companies = [] } = useGetCompaniesQuery();
  const [currentCompany, setCurrentCompanyState] = useState<Company | null>(null);
  const { data: companyMembers = [] } = useGetCompanyMembersQuery(currentCompany?.id || '');

  // Wrapper for setCurrentCompany that also updates localStorage and invalidates queries
  const setCurrentCompany = (company: Company | null) => {
    // Only proceed if the company is actually changing
    if (currentCompany?.id !== company?.id) {
      console.log(`Changing company from ${currentCompany?.name || 'null'} to ${company?.name || 'null'}`);

      // Update state
      setCurrentCompanyState(company);

      // Store the selected company ID in localStorage for persistence
      if (company) {
        localStorage.setItem('lastSelectedCompanyId', company.id);
      } else {
        localStorage.removeItem('lastSelectedCompanyId');
      }

      // Invalidate all monitor-related queries to force a refresh
      console.log('Invalidating monitor queries due to company change');
      queryClient.invalidateQueries({ queryKey: ['monitors'] });

      // Also invalidate any other queries that might depend on the company
      queryClient.invalidateQueries({ queryKey: ['monitor-history'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });

      // Check if we need to navigate to the dashboard
      // We'll store a flag in localStorage to indicate that we need to navigate
      localStorage.setItem('company_changed', 'true');
    }
  };

  // Set the company as current if none is selected
  useEffect(() => {
    if (companies.length > 0 && !currentCompany) {
      // Check if there's a last selected company ID in localStorage
      const lastSelectedCompanyId = localStorage.getItem('lastSelectedCompanyId');

      if (lastSelectedCompanyId) {
        // Find the company with the matching ID
        const lastCompany = companies.find(company => company.id === lastSelectedCompanyId);

        if (lastCompany) {
          console.log(`Restoring last selected company: ${lastCompany.name} (${lastCompany.id})`);
          setCurrentCompany(lastCompany);
          return;
        }
      }

      // If no last selected company or it wasn't found, use the first company
      setCurrentCompany(companies[0]);
    }
  }, [companies, currentCompany]);

  // Check if the current user is an admin of the current company
  // If there are no company members yet (tables not created), default to true to allow initial setup
  // Also, if "All Companies" is selected and the user is a superadmin, they are an admin
  const isAdmin = companyMembers.length === 0 ? true :
    (currentCompany?.isAllCompanies && isGlobalSuperadmin) ? true :
    companyMembers.some(
      member => member.user_id === user?.id &&
      (member.role_type === 'admin' || member.role_type === 'superadmin')
    );

  // Get the user's role in a specific company
  const getUserRole = (companyId: string): CompanyRole | null => {
    // If there are no company members yet (tables not created), default to admin to allow initial setup
    if (companyMembers.length === 0) return 'admin';

    // Check if the user is a global superadmin (cached value from props)
    if (isGlobalSuperadmin) {
      return 'admin'; // Global superadmins have admin access to all companies
    }

    const membership = companyMembers.find(
      member => member.company_id === companyId && member.user_id === user?.id
    );

    if (!membership) return null;

    // Map role_type to CompanyRole for UI display
    if (membership.role_type === 'admin') return 'admin';
    if (membership.role_type === 'superadmin') return 'admin'; // Treat superadmin as admin in company context
    return 'member'; // Default for 'user' role_type
  };

  const value = {
    currentCompany,
    setCurrentCompany,
    isAdmin,
    getUserRole
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
};



export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
};
