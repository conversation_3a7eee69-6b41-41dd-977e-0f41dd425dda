-- Create the users table (if it doesn't exist already)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create the monitors table
CREATE TABLE IF NOT EXISTS public.monitors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    target TEXT NOT NULL,
    type TEXT NOT NULL,
    interval INTEGER NOT NULL DEFAULT 5,
    timeout INTEGER NOT NULL DEFAULT 30,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create the monitor_history table
CREATE TABLE IF NOT EXISTS public.monitor_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    monitor_id UUID NOT NULL REFERENCES public.monitors(id) ON DELETE CASCADE,
    status BOOLEAN NOT NULL,
    response_time INTEGER,
    error_message TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_monitors_user_id ON public.monitors(user_id);
CREATE INDEX IF NOT EXISTS idx_monitor_history_monitor_id ON public.monitor_history(monitor_id);
CREATE INDEX IF NOT EXISTS idx_monitor_history_timestamp ON public.monitor_history(timestamp);

-- Set up Row Level Security (RLS) policies
-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.monitors ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.monitor_history ENABLE ROW LEVEL SECURITY;

-- Create policies for users table
CREATE POLICY "Users can view their own data" 
    ON public.users FOR SELECT 
    USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" 
    ON public.users FOR UPDATE 
    USING (auth.uid() = id);

-- Create policies for monitors table
CREATE POLICY "Users can view their own monitors" 
    ON public.monitors FOR SELECT 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own monitors" 
    ON public.monitors FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own monitors" 
    ON public.monitors FOR UPDATE 
    USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own monitors" 
    ON public.monitors FOR DELETE 
    USING (auth.uid() = user_id);

-- Create policies for monitor_history table
CREATE POLICY "Users can view history of their own monitors" 
    ON public.monitor_history FOR SELECT 
    USING (EXISTS (
        SELECT 1 FROM public.monitors 
        WHERE monitors.id = monitor_history.monitor_id 
        AND monitors.user_id = auth.uid()
    ));

CREATE POLICY "Users can insert history for their own monitors" 
    ON public.monitor_history FOR INSERT 
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.monitors 
        WHERE monitors.id = monitor_history.monitor_id 
        AND monitors.user_id = auth.uid()
    ));

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.users TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.monitors TO authenticated;
GRANT SELECT, INSERT ON public.monitor_history TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
