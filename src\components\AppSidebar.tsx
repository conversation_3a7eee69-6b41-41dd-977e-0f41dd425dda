import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import {
  LayoutDashboard,
  Settings,
  Users,
  AlertTriangle,
  Globe,
  LogOut,
  Building,
  CreditCard,
  Layers
} from 'lucide-react';
import { cn } from '@/lib/utils';
import VumLogo from './VumLogo';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { useSidebar } from '@/contexts/SidebarContext';
import { Button } from './ui/button';
import { Separator } from './ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
// Keyboard shortcut functionality now handled in the header
// Toggle functionality now handled in the header

interface AppSidebarProps {
  hideHeader?: boolean;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ hideHeader = false }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, signOut } = useAuth();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin } = useGlobalSuperadminQuery();
  const { toggleSidebar } = useSidebar();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    const fullName = user?.user_metadata?.full_name || '';
    const email = user?.email || '';

    if (fullName) {
      const nameParts = fullName.split(' ');
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return fullName[0].toUpperCase();
    }

    return email ? email[0].toUpperCase() : 'U';
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <div className={`${!hideHeader ? 'w-64 h-screen fixed left-0 top-0' : 'w-full h-full'} bg-white dark:bg-slate-800 flex flex-col`}>
      {/* Logo - only show if header is not hidden */}
      {!hideHeader && (
        <div className="py-4 px-6 border-b border-slate-200 dark:border-slate-700">
          <VumLogo linkTo="/dashboard" />
        </div>
      )}

      {/* Navigation header */}
      <div className="px-4 py-2 text-xs font-semibold text-slate-500 dark:text-slate-400 border-b border-slate-200 dark:border-slate-700">
        Navigation
      </div>

      {/* Navigation */}
      <div className="flex-1 py-4 overflow-y-auto">
        <nav className="px-2 space-y-1">
          <Link
            to="/dashboard"
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
              isActive('/dashboard') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
            )}
          >
            <LayoutDashboard className="h-5 w-5" />
            <span>Dashboard</span>
          </Link>

          {/* Companies - for all users */}
          <Link
            to="/companies"
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
              isActive('/companies') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
            )}
          >
            <Building className="h-5 w-5" />
            <span>Companies</span>
          </Link>

          {/* Separator */}
          <Separator className="my-2" />

          {/* Admin section - visible to all, but with different items based on role */}
          <div className="px-3 py-2 text-xs font-semibold text-slate-500 dark:text-slate-400">
            Administration
          </div>

          {/* Subscription - for all users */}
          <Link
            to="/subscription"
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
              isActive('/subscription') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
            )}
          >
            <CreditCard className="h-5 w-5" />
            <span>Subscription</span>
          </Link>

          {/* Subscription Tiers - only for superadmins */}
          {isSuperadmin && (
            <Link
              to="/subscription-tiers"
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
                isActive('/subscription-tiers') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
              )}
            >
              <Layers className="h-5 w-5" />
              <span>Manage Tiers</span>
            </Link>
          )}

          {/* User Management - only for superadmins */}
          {isSuperadmin && (
            <Link
              to="/users"
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
                isActive('/users') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
              )}
            >
              <Users className="h-5 w-5" />
              <span>User Management</span>
            </Link>
          )}

          {/* Global Settings - only for superadmins */}
          {isSuperadmin && (
            <Link
              to="/global-settings"
              className={cn(
                "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
                isActive('/global-settings') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
              )}
            >
              <Globe className="h-5 w-5" />
              <span>Global Settings</span>
            </Link>
          )}

          {/* Profile Settings - for all users */}
          <Link
            to="/profile"
            className={cn(
              "flex items-center space-x-2 px-3 py-2 rounded-md text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700/50",
              isActive('/profile') && "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400"
            )}
          >
            <Settings className="h-5 w-5" />
            <span>Profile Settings</span>
          </Link>

        </nav>
      </div>

      {/* User profile section */}
      <div className="p-4 border-t border-slate-200 dark:border-slate-700">
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            {user?.user_metadata?.avatar_url ? (
              <AvatarImage src={user.user_metadata.avatar_url} alt="Profile" />
            ) : null}
            <AvatarFallback>{getUserInitials()}</AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">
              {user?.user_metadata?.full_name || user?.email}
            </p>
            <p className="text-xs text-slate-500 dark:text-slate-400 truncate">
              {user?.email}
            </p>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleSignOut}
            className="h-8 w-8 text-slate-500 hover:text-red-600"
          >
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AppSidebar;
