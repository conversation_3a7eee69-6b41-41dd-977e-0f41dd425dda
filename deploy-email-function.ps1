# PowerShell script to deploy the send-status-email function

# Navigate to the project root
$projectRoot = Split-Path -Parent $MyInvocation.MyCommand.Path

# Deploy the function
Write-Host "Deploying send-status-email function..." -ForegroundColor Yellow
Set-Location $projectRoot
npx supabase functions deploy send-status-email --project-ref axcfqil<PERSON>ombkbzebeym

# Check if deployment was successful
if ($LASTEXITCODE -eq 0) {
    Write-Host "Deployment completed successfully!" -ForegroundColor Green
} else {
    Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
