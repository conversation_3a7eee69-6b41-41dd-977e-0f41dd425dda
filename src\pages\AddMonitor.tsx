
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/components/ui/use-toast";
import { ArrowLeft } from "lucide-react";
import AppLayout from "@/components/AppLayout";
import UnifiedHeader from "@/components/UnifiedHeader";
import DocumentTitle from "@/components/DocumentTitle";
import { useAuth } from "@/contexts/AuthContext";
import { useMonitors } from "@/hooks/use-monitors";
import { useCompany } from "@/contexts/CompanyContext";
import { useEffect } from "react";
import MonitorCompanySelector from "@/components/MonitorCompanySelector";
import { CreateMonitorData, DegradedThresholds } from "@/types/monitor";
import MonitorDegradedSettings from "@/components/MonitorDegradedSettings";
import { useCompanyRoles } from "@/hooks/use-company-roles";
import { useSubscription } from "@/hooks/use-subscription";
import { MonitorLimitWarning } from "@/components/MonitorLimitWarning";
import PortSelector from "@/components/PortSelector";
import { checkPorts } from "@/utils/portChecker";

const AddMonitor = () => {
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    type: "http",
    interval: "5",
    timeout: "30",
    retries: "1",
    alertWhenDown: true,
    alertWhenUp: true,
    alertMethod: "email",
  });

  // State for port monitoring
  const [portHost, setPortHost] = useState("");
  const [selectedPorts, setSelectedPorts] = useState<number[]>([80, 443]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for selected companies
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<string[]>([]);
  // State for degraded settings
  const [degradedSettings, setDegradedSettings] = useState<DegradedThresholds | null>(null);

  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentCompany, isAdmin } = useCompany();
  const { isGlobalSuperadmin } = useCompanyRoles();
  const { useCreateMonitorMutation } = useMonitors();
  const { useCanAddMonitor } = useSubscription();
  const [isSuperadmin, setIsSuperadmin] = useState(false);

  // Check if the company can add more monitors
  const { data: canAddMonitor } = useCanAddMonitor();

  // Redirect if not an admin (but only if a company is selected)
  useEffect(() => {
    if (currentCompany && !isAdmin) {
      toast({
        title: "Access Denied",
        description: "You do not have permission to create monitors",
        variant: "destructive",
      });
      navigate("/dashboard");
    }

    // Initialize selected companies with current company if available
    if (currentCompany) {
      setSelectedCompanyIds([currentCompany.id]);
    }

    // Check if user is a superadmin
    const checkSuperadmin = async () => {
      const superadmin = await isGlobalSuperadmin();
      setIsSuperadmin(superadmin);
    };

    checkSuperadmin();
  }, [currentCompany, isAdmin, navigate, isGlobalSuperadmin]);

  const createMonitor = useCreateMonitorMutation();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a monitor.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // If a company is selected, check if the user is an admin
    if (currentCompany && !isAdmin) {
      toast({
        title: "Access Denied",
        description: "You do not have permission to create monitors in this company",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Validate that at least one company is selected
    if (selectedCompanyIds.length === 0) {
      toast({
        title: "Company Required",
        description: "Please select at least one company for this monitor.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Check if the company can add more monitors
    if (canAddMonitor === false) {
      toast({
        title: "Monitor Limit Reached",
        description: "You have reached the maximum number of monitors allowed for your subscription tier.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // For port monitoring, check if the host exists before creating the monitor
    if (formData.type === 'port') {
      try {
        // Check if the host exists
        const results = await checkPorts(portHost, selectedPorts);
        const hostExists = results.some(result => result.status);

        if (!hostExists) {
          // Ask for confirmation before creating a monitor for a host that doesn't exist
          if (!window.confirm(
            "The host could not be found in DNS lookups. The monitor will likely show as DOWN. Do you still want to create this monitor?"
          )) {
            setIsSubmitting(false);
            return;
          }
        }
      } catch (error) {
        console.error('Error checking host:', error);
        // Continue with monitor creation even if host checking fails
      }
    }

    // Create the monitor object from form data
    let target = formData.url;

    // For port monitoring, format the target as host|port1,port2,port3
    if (formData.type === 'port') {
      if (!portHost) {
        toast({
          title: "Host Required",
          description: "Please enter a host for port monitoring.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (selectedPorts.length === 0) {
        toast({
          title: "Ports Required",
          description: "Please select at least one port to monitor.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      target = `${portHost}|${selectedPorts.join(',')}`;
    }

    const newMonitor: CreateMonitorData = {
      name: formData.name,
      target: target,
      type: formData.type as any, // Cast to the correct type
      interval: parseInt(formData.interval),
      timeout: parseInt(formData.timeout),
      company_ids: selectedCompanyIds,
      degraded_settings: degradedSettings, // Include degraded settings if set
    };

    createMonitor.mutate(newMonitor, {
      onSuccess: async (createdMonitor) => {
        // Monitor created successfully
        toast({
          title: "Monitor Created",
          description: "Your monitor has been created successfully.",
        });

        // Set a timeout for the initial check to prevent UI from getting stuck
        const initialCheckTimeout = 5000; // 5 seconds timeout
        let initialCheckCompleted = false;

        // Perform an immediate check of the new monitor in the background
        const checkPromise = (async () => {
          try {
            // Call the monitor-checker edge function with the new monitor ID
            const { data, error } = await supabase.functions.invoke('monitor-checker', {
              body: { monitorId: createdMonitor.id },
            });

            if (error) throw error;

            if (data && data.results && data.results.length > 0) {
              console.log("Initial check complete:", data.results);
              initialCheckCompleted = true;
            } else {
              throw new Error('No check results returned');
            }
          } catch (checkError) {
            console.error("Error performing initial check:", checkError);
            // Only show toast if we're still on this page
            if (!initialCheckCompleted) {
              toast({
                title: "Note",
                description: "Monitor created, but initial check is still pending. It will be checked according to its schedule.",
                variant: "default",
              });
            }
          }
        })();

        // Set a timeout to ensure we don't wait too long for the initial check
        const timeoutPromise = new Promise(resolve => setTimeout(() => {
          if (!initialCheckCompleted) {
            console.log("Initial check timed out after", initialCheckTimeout, "ms");
            resolve(null);
          }
        }, initialCheckTimeout));

        // Wait for either the check to complete or the timeout to expire
        await Promise.race([checkPromise, timeoutPromise]);

        // Force a refresh of the dashboard data by adding a timestamp parameter
        // This ensures the dashboard will reload the monitors when navigating
        console.log('Monitor created successfully, navigating to dashboard with refresh parameter');
        navigate("/dashboard?refresh=" + Date.now());
      },
      onError: (error) => {
        console.error("Error creating monitor:", error);

        // Check for specific error messages
        let errorMessage = "There was an error creating your monitor. Please try again.";

        if (error.message) {
          if (error.message.includes("Monitor limit reached")) {
            errorMessage = "Monitor limit reached for this company. Please upgrade your subscription to add more monitors.";
          } else {
            // Use the actual error message from the server
            errorMessage = error.message;
          }
        }

        toast({
          title: "Error creating monitor",
          description: errorMessage,
          variant: "destructive",
        });
        setIsSubmitting(false);
      },
      // Always reset the submitting state when the mutation settles
      onSettled: () => {
        setIsSubmitting(false);
      }
    });
  };

  const header = (
    <UnifiedHeader
      title="Add New Monitor"
      showCompanySelector={true}
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Add New Monitor" />
      <div className="container mx-auto py-8 px-4">
        {/* Show warning if monitor limit is reached */}
        {canAddMonitor === false && (
          <div className="max-w-3xl mx-auto mb-6">
            <MonitorLimitWarning />
          </div>
        )}

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">Add New Monitor</CardTitle>
            <CardDescription>
              Set up a new monitor to keep track of your website's uptime.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Basic Information</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Monitor Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="My Website"
                      required
                      value={formData.name}
                      onChange={handleChange}
                    />
                  </div>
                  {formData.type !== 'port' ? (
                    <div className="space-y-2">
                      <Label htmlFor="url">URL</Label>
                      <Input
                        id="url"
                        name="url"
                        placeholder="https://example.com"
                        required
                        value={formData.url}
                        onChange={handleChange}
                      />
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Include the full URL with http:// or https://
                      </p>
                    </div>
                  ) : (
                    <PortSelector
                      host={portHost}
                      onHostChange={setPortHost}
                      selectedPorts={selectedPorts}
                      onPortsChange={setSelectedPorts}
                    />
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="type">Monitor Type</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("type", value)}
                      defaultValue={formData.type}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="http">HTTP(S)</SelectItem>
                        <SelectItem value="ping">Ping</SelectItem>
                        <SelectItem value="port">Port</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companies">Assign to Companies</Label>
                    <MonitorCompanySelector
                      selectedCompanyIds={selectedCompanyIds}
                      onChange={setSelectedCompanyIds}
                    />
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      Select one or more companies to assign this monitor to
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Monitor Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="interval">Check Interval</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("interval", value)}
                      defaultValue={formData.interval}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 minute</SelectItem>
                        <SelectItem value="5">5 minutes</SelectItem>
                        <SelectItem value="10">10 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">60 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("timeout", value)}
                      defaultValue={formData.timeout}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select timeout" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10 seconds</SelectItem>
                        <SelectItem value="30">30 seconds</SelectItem>
                        <SelectItem value="60">60 seconds</SelectItem>
                        <SelectItem value="120">120 seconds</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="retries">Retries</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("retries", value)}
                      defaultValue={formData.retries}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select retries" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="0">0</SelectItem>
                        <SelectItem value="1">1</SelectItem>
                        <SelectItem value="2">2</SelectItem>
                        <SelectItem value="3">3</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Degraded Settings - Only visible to superadmins */}
              <MonitorDegradedSettings
                monitorId="new"
                initialSettings={degradedSettings}
                onSettingsChange={setDegradedSettings}
                isSuperadmin={isSuperadmin}
              />

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Alert Settings</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="alertWhenDown">Alert when down</Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Receive alerts when the monitor goes down
                      </p>
                    </div>
                    <Switch
                      id="alertWhenDown"
                      checked={formData.alertWhenDown}
                      onCheckedChange={(checked) => handleSwitchChange("alertWhenDown", checked)}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label htmlFor="alertWhenUp">Alert when back up</Label>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Receive alerts when the monitor comes back up
                      </p>
                    </div>
                    <Switch
                      id="alertWhenUp"
                      checked={formData.alertWhenUp}
                      onCheckedChange={(checked) => handleSwitchChange("alertWhenUp", checked)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Alert Method</Label>
                    <RadioGroup
                      defaultValue={formData.alertMethod}
                      onValueChange={(value) => handleSelectChange("alertMethod", value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="email" id="email" />
                        <Label htmlFor="email">Email</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="sms" id="sms" />
                        <Label htmlFor="sms">SMS</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="webhook" id="webhook" />
                        <Label htmlFor="webhook">Webhook</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button variant="outline" type="button" onClick={() => navigate(-1)}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={isSubmitting || createMonitor.isPending || canAddMonitor === false}
                >
                  {isSubmitting || createMonitor.isPending ? "Creating..." : "Create Monitor"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default AddMonitor;
