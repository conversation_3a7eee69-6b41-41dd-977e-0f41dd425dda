// This is a patch for the monitor service to fix the notifications issue
// Replace the sendNotification function in your monitor service with this one

// Send a notification
async function sendNotification(monitor, status, statusText = null) {
  try {
    const statusMessage = statusText || (status ? 'UP' : 'DOWN');
    logger.info(`Sending notification for monitor ${monitor.name}: Status is now ${statusMessage}`);

    // Create notification object with both timestamp and created_at for compatibility
    // Make sure user_id is set - this is required by the not-null constraint
    if (!monitor.user_id) {
      // Get the monitor owner from the database if not provided
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('user_id')
        .eq('id', monitor.id)
        .single();

      if (monitorError) {
        logger.error(`Error getting monitor owner for ${monitor.id}: ${monitorError.message}`);
        throw monitorError;
      }

      if (!monitorData || !monitorData.user_id) {
        logger.error(`No user_id found for monitor ${monitor.id}`);
        throw new Error(`No user_id found for monitor ${monitor.id}`);
      }

      monitor.user_id = monitorData.user_id;
    }

    const notification = {
      monitor_id: monitor.id,
      user_id: monitor.user_id, // Now we ensure this is set
      company_id: monitor.company_id,
      message: `Monitor ${monitor.name} is now ${statusMessage}`,
      type: status ? 'up' : 'down',
      read: false,
      created_at: new Date().toISOString()
      // Note: We don't need to set timestamp explicitly as it's a generated column from created_at
    };

    // Insert notification into the database
    const { data, error } = await supabase
      .from('notifications')
      .insert(notification)
      .select();

    if (error) {
      logger.error(`Error creating notification for company ${monitor.company_id}: ${error.message}`);
      throw error;
    }

    logger.info(`Notification sent for ${monitor.name}`);
    return data;
  } catch (error) {
    logger.error(`Failed to send notification: ${error.message}`);
    return null;
  }
}

// Function to create notifications for all companies associated with a monitor
async function createNotificationsForMonitor(monitor, status, statusText = null) {
  try {
    // Make sure we have the monitor's user_id
    if (!monitor.user_id) {
      // Get the monitor details including user_id
      const { data: monitorData, error: monitorError } = await supabase
        .from('monitors')
        .select('user_id, name')
        .eq('id', monitor.id)
        .single();

      if (monitorError) {
        logger.error(`Error getting monitor details for ${monitor.id}: ${monitorError.message}`);
        return false;
      }

      if (!monitorData) {
        logger.error(`Monitor not found: ${monitor.id}`);
        return false;
      }

      // Update the monitor object with the user_id
      monitor.user_id = monitorData.user_id;
      if (!monitor.name) {
        monitor.name = monitorData.name;
      }
    }

    // Get all companies associated with this monitor
    const { data: companies, error: companiesError } = await supabase
      .from('monitor_companies')
      .select('company_id')
      .eq('monitor_id', monitor.id);

    if (companiesError) {
      logger.error(`Error getting companies for monitor ${monitor.id}: ${companiesError.message}`);
      return false;
    }

    if (!companies || companies.length === 0) {
      logger.warn(`No companies found for monitor ${monitor.id}`);
      return false;
    }

    // Create a notification for each company
    let successCount = 0;
    for (const company of companies) {
      try {
        const monitorWithCompany = {
          ...monitor,
          company_id: company.company_id
        };

        await sendNotification(monitorWithCompany, status, statusText);
        successCount++;
      } catch (error) {
        logger.error(`Error creating notification for company ${company.company_id}: ${error.message}`);
      }
    }

    logger.info(`Created notifications for ${monitor.name} across ${successCount} companies`);
    return successCount > 0;
  } catch (error) {
    logger.error(`Error creating notifications for monitor ${monitor.id}: ${error.message}`);
    return false;
  }
}
