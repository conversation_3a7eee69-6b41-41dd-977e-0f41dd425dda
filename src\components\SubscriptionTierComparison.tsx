import { useSubscription } from '@/hooks/use-subscription';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { CheckIcon, XIcon, MinusIcon } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { SubscriptionTierDefaults } from './SubscriptionTierDefaults';

interface FeatureComparisonProps {
  showTitle?: boolean;
}

export function SubscriptionTierComparison({ showTitle = true }: FeatureComparisonProps) {
  const { useSubscriptionTiers } = useSubscription();
  const { data: tiers, isLoading } = useSubscriptionTiers();

  // Define features to compare
  const features = [
    { name: 'Monitors', key: 'max_monitors', type: 'value' },
    { name: 'History Retention', key: 'history_retention_days', type: 'value', suffix: ' days' },
    { name: 'Email Notifications', key: 'email_notifications', type: 'boolean', tiers: { 'Free': false, 'Premium': true, 'Professional': true, 'Enterprise': true } },
    { name: 'SMS Notifications', key: 'sms_notifications', type: 'boolean', tiers: { 'Free': false, 'Premium': false, 'Professional': true, 'Enterprise': true } },
    { name: 'Webhook Notifications', key: 'webhook_notifications', type: 'boolean', tiers: { 'Free': false, 'Premium': true, 'Professional': true, 'Enterprise': true } },
    { name: 'API Access', key: 'api_access', type: 'boolean', tiers: { 'Free': false, 'Premium': false, 'Professional': true, 'Enterprise': true } },
    { name: 'Custom Degraded Settings', key: 'custom_degraded_settings', type: 'boolean', tiers: { 'Free': false, 'Premium': false, 'Professional': true, 'Enterprise': true } },
    { name: 'Priority Support', key: 'priority_support', type: 'boolean', tiers: { 'Free': false, 'Premium': false, 'Professional': false, 'Enterprise': true } },
  ];

  if (isLoading) {
    return (
      <div className="space-y-4">
        {showTitle && (
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-full" />
          </div>
        )}
        <div className="grid grid-cols-5 gap-4">
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
          <Skeleton className="h-10" />
        </div>
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="grid grid-cols-5 gap-4">
            <Skeleton className="h-12" />
            <Skeleton className="h-12" />
            <Skeleton className="h-12" />
            <Skeleton className="h-12" />
            <Skeleton className="h-12" />
          </div>
        ))}
      </div>
    );
  }

  if (!tiers || tiers.length === 0) {
    return <div>No subscription tiers available.</div>;
  }

  // Sort tiers by max_monitors
  const sortedTiers = [...tiers].sort((a, b) => a.max_monitors - b.max_monitors);

  return (
    <SubscriptionTierDefaults tiers={sortedTiers} showTitle={showTitle} />
  );
}
