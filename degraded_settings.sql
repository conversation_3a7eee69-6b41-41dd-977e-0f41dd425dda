-- This script creates tables and functions for global and monitor-specific degraded settings
-- Run this in the Supabase SQL Editor

-- Make sure the uuid-ossp extension is available for gen_random_uuid()
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the global degraded settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.degraded_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    response_time INTEGER NOT NULL DEFAULT 1000,
    error_rate INTEGER NOT NULL DEFAULT 10,
    status_codes INTEGER[] NOT NULL DEFAULT '{429, 503}'::INTEGER[],
    consecutive_failures INTEGER NOT NULL DEFAULT 2,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create the monitor-specific degraded settings table
CREATE TABLE IF NOT EXISTS public.monitor_degraded_settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    monitor_id UUID NOT NULL REFERENCES public.monitors(id) ON DELETE CASCADE,
    response_time INTEGER,
    error_rate INTEGER,
    status_codes INTEGER[],
    consecutive_failures INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(monitor_id)
);

-- Insert default global settings if none exist
INSERT INTO public.degraded_settings (id, response_time, error_rate, status_codes, consecutive_failures)
SELECT 
    gen_random_uuid(),
    1000,
    10,
    '{429, 503}'::INTEGER[],
    2
WHERE NOT EXISTS (SELECT 1 FROM public.degraded_settings LIMIT 1);

-- Create a function to get degraded settings for a monitor (with fallback to global)
CREATE OR REPLACE FUNCTION get_monitor_degraded_settings(monitor_id UUID)
RETURNS JSONB AS $$
DECLARE
    monitor_settings JSONB;
    global_settings JSONB;
    result JSONB;
BEGIN
    -- Get monitor-specific settings
    SELECT jsonb_build_object(
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures
    )
    INTO monitor_settings
    FROM public.monitor_degraded_settings
    WHERE monitor_degraded_settings.monitor_id = get_monitor_degraded_settings.monitor_id;
    
    -- Get global settings
    SELECT jsonb_build_object(
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures
    )
    INTO global_settings
    FROM public.degraded_settings
    LIMIT 1;
    
    -- Combine settings, using monitor-specific when available, falling back to global
    result = global_settings;
    
    IF monitor_settings IS NOT NULL THEN
        -- Only override fields that are not null in monitor settings
        IF monitor_settings->>'response_time' IS NOT NULL THEN
            result = jsonb_set(result, '{response_time}', monitor_settings->'response_time');
        END IF;
        
        IF monitor_settings->>'error_rate' IS NOT NULL THEN
            result = jsonb_set(result, '{error_rate}', monitor_settings->'error_rate');
        END IF;
        
        IF monitor_settings->>'status_codes' IS NOT NULL THEN
            result = jsonb_set(result, '{status_codes}', monitor_settings->'status_codes');
        END IF;
        
        IF monitor_settings->>'consecutive_failures' IS NOT NULL THEN
            result = jsonb_set(result, '{consecutive_failures}', monitor_settings->'consecutive_failures');
        END IF;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create a function to update or create monitor-specific degraded settings
CREATE OR REPLACE FUNCTION upsert_monitor_degraded_settings(
    p_monitor_id UUID,
    p_response_time INTEGER DEFAULT NULL,
    p_error_rate INTEGER DEFAULT NULL,
    p_status_codes INTEGER[] DEFAULT NULL,
    p_consecutive_failures INTEGER DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    -- Insert or update the monitor settings
    INSERT INTO public.monitor_degraded_settings (
        monitor_id,
        response_time,
        error_rate,
        status_codes,
        consecutive_failures
    ) VALUES (
        p_monitor_id,
        p_response_time,
        p_error_rate,
        p_status_codes,
        p_consecutive_failures
    )
    ON CONFLICT (monitor_id) DO UPDATE SET
        response_time = EXCLUDED.response_time,
        error_rate = EXCLUDED.error_rate,
        status_codes = EXCLUDED.status_codes,
        consecutive_failures = EXCLUDED.consecutive_failures,
        updated_at = now()
    RETURNING jsonb_build_object(
        'id', id,
        'monitor_id', monitor_id,
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures,
        'created_at', created_at,
        'updated_at', updated_at
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create a function to delete monitor-specific degraded settings
CREATE OR REPLACE FUNCTION delete_monitor_degraded_settings(
    p_monitor_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    deleted BOOLEAN;
BEGIN
    DELETE FROM public.monitor_degraded_settings
    WHERE monitor_id = p_monitor_id
    RETURNING TRUE INTO deleted;
    
    RETURN COALESCE(deleted, FALSE);
END;
$$ LANGUAGE plpgsql;

-- Set up RLS policies
ALTER TABLE public.degraded_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.monitor_degraded_settings ENABLE ROW LEVEL SECURITY;

-- Only superadmins can manage global degraded settings
CREATE POLICY "Superadmins can manage global degraded settings"
ON public.degraded_settings
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- Company admins can manage monitor-specific degraded settings for their companies
CREATE POLICY "Company admins can manage monitor degraded settings"
ON public.monitor_degraded_settings
USING (
    EXISTS (
        SELECT 1 FROM public.monitor_companies mc
        JOIN public.company_members cm ON mc.company_id = cm.company_id
        WHERE mc.monitor_id = monitor_degraded_settings.monitor_id
        AND cm.user_id = auth.uid()
        AND cm.role = 'admin'
    )
    OR
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- Grant necessary permissions
GRANT ALL ON public.degraded_settings TO authenticated;
GRANT ALL ON public.monitor_degraded_settings TO authenticated;
