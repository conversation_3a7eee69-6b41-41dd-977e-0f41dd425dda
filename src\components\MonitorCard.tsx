import React, { useState, useEffect, ReactNode } from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  CheckCircle,
  AlertTriangle,
  XCircle,
  Clock,
  ExternalLink,
  Settings,
  MoreVertical,
  Play,
  Pause,
  Trash,
  Timer,
  Mail,
  RefreshCw,
  Loader2
} from 'lucide-react';
import SendTestEmailButton from "@/components/SendTestEmailButton";
import ManualCheckButton from "@/components/ManualCheckButton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "@/components/ui/use-toast";
import { ToastAction } from "@/components/ui/toast";
import { supabase } from "@/integrations/supabase/client";
import { useMonitorHistory } from "@/hooks/use-monitor-history";
import { useQueryClient } from "@tanstack/react-query";
import { useMonitorStatusUpdate } from "@/hooks/use-monitor-status-update";
import { format } from 'date-fns';
import { formatDate, formatTime } from '@/utils/dateFormat';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { Skeleton } from "@/components/ui/skeleton";

// Define the monitor type
type Monitor = {
  id: string;
  name: string;
  target: string;
  type: string;
  active: boolean;
  interval: number;
  timeout: number;
  status?: 'up' | 'down' | 'degraded' | 'paused';
  uptime?: string;
  lastChecked?: string;
  responseTime?: string;
  hasCustomDegradedSettings?: boolean;
  companies?: Array<{
    company_id: string;
    company_name?: string;
  }>;
};

// Status icon component
const getStatusIcon = (status?: string) => {
  if (!status) return null;

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'up':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'degraded':
      return <AlertTriangle className="h-5 w-5 text-amber-500" />;
    case 'down':
      return <XCircle className="h-5 w-5 text-red-500" />;
    case 'paused':
      return <Clock className="h-5 w-5 text-slate-500" />;
    default:
      return <CheckCircle className="h-5 w-5 text-green-500" />; // Default to operational
  }
};

// Helper function to get detailed error explanation
const getErrorExplanation = (errorMessage: string): string => {
  // HTTP Status Code explanations
  if (errorMessage.includes('503')) {
    return "Service Unavailable - The server is temporarily unable to handle requests, usually due to maintenance or overload.";
  }
  if (errorMessage.includes('429')) {
    return "Too Many Requests - The server is rate limiting requests. Too many requests were sent in a short time period.";
  }
  if (errorMessage.includes('500')) {
    return "Internal Server Error - The server encountered an unexpected condition that prevented it from fulfilling the request.";
  }
  if (errorMessage.includes('502')) {
    return "Bad Gateway - The server received an invalid response from an upstream server.";
  }
  if (errorMessage.includes('504')) {
    return "Gateway Timeout - The server did not receive a timely response from an upstream server.";
  }

  // Response time explanations
  if (errorMessage.includes('Slow response time:')) {
    const match = errorMessage.match(/(\d+)ms.*threshold:\s*(\d+)ms/);
    if (match) {
      return `Response time of ${match[1]}ms exceeds the configured threshold of ${match[2]}ms, indicating performance issues.`;
    }
    return "Response time exceeds the configured threshold, indicating performance issues.";
  }

  // Generic degraded explanation
  return "Service is experiencing issues that may affect functionality.";
};

// Status badge component
const getStatusBadge = (status?: string, errorMessage?: string) => {
  if (!status) return null;

  const statusLower = status.toLowerCase();
  switch (statusLower) {
    case 'up':
      return <Badge className="bg-green-500">Up</Badge>;
    case 'degraded':
      // Show degraded reason if available
      const degradedReason = errorMessage ? (() => {
        // Create compact error messages for tiles
        if (errorMessage.includes('HTTP status indicates degraded service:')) {
          return errorMessage.replace('HTTP status indicates degraded service: ', 'Error ');
        }
        if (errorMessage.includes('Slow response time:')) {
          const match = errorMessage.match(/(\d+)ms.*threshold:\s*(\d+)ms/);
          return match ? `Slow: ${match[1]}ms` : errorMessage.replace('Slow response time: ', 'Slow: ');
        }
        // Truncate long error messages for tiles
        return errorMessage.length > 20 ? errorMessage.substring(0, 20) + '...' : errorMessage;
      })() : '';

      const errorExplanation = errorMessage ? getErrorExplanation(errorMessage) : '';

      return (
        <div className="flex flex-col items-end">
          <Badge className="bg-amber-500">Degraded</Badge>
          {degradedReason && (
            <div className="mt-1 text-right">
              <span className="text-xs text-amber-600 dark:text-amber-400 font-medium truncate max-w-[120px] block">
                {degradedReason}
              </span>
              {errorExplanation && (
                <span className="text-xs text-slate-600 dark:text-slate-400 mt-1 max-w-[200px] block leading-tight">
                  {errorExplanation.length > 60 ? errorExplanation.substring(0, 60) + '...' : errorExplanation}
                </span>
              )}
            </div>
          )}
        </div>
      );
    case 'down':
      return <Badge variant="destructive">Down</Badge>;
    case 'paused':
      return <Badge variant="outline">Paused</Badge>;
    default:
      return <Badge className="bg-green-500">Up</Badge>; // Default to up
  }
};

interface MonitorCardProps {
  monitor: Monitor;
  isSuperadmin: boolean;
  onToggleStatus: (id: string, active: boolean) => void;
  onDelete: (id: string, name: string) => void;
}

const MonitorCard: React.FC<MonitorCardProps> = ({
  monitor,
  isSuperadmin,
  onToggleStatus,
  onDelete
}) => {
  // Get the last 4 hours of history for the monitor
  const [chartData, setChartData] = useState<any[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(true);
  const [isTestingMonitor, setIsTestingMonitor] = useState(false);
  const [latestErrorMessage, setLatestErrorMessage] = useState<string | null>(null);

  // Get the query client for invalidating queries
  const queryClient = useQueryClient();

  // Get the monitor status update hook
  const { updateMonitorStatus, getMonitorStatus, hasStatusUpdate, clearStatusUpdate } = useMonitorStatusUpdate();

  // Define the fetchHistory function outside of useEffect
  const fetchHistory = async () => {
    setIsLoadingHistory(true);
    try {
      // Calculate date range (4 hours ago)
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 4 * 60 * 60 * 1000);

      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitor.id)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: true });

      if (error) throw error;

      // Format data for the chart
      const formattedData = (data || []).map(item => ({
        time: format(new Date(item.timestamp), 'HH:mm'),
        value: item.response_time || 0,
        status: item.status
      }));

      setChartData(formattedData);

      // Get the latest error message for degraded status display
      if (data && data.length > 0) {
        const latestEntry = data[data.length - 1]; // Last entry (most recent)
        if (latestEntry.status === 'degraded' && latestEntry.error_message) {
          setLatestErrorMessage(latestEntry.error_message);
        } else {
          setLatestErrorMessage(null);
        }
      }
    } catch (err) {
      console.error('Error fetching 4-hour monitor history:', err);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  // Fetch the last 4 hours of history when component mounts or monitor.id changes
  useEffect(() => {
    fetchHistory();
  }, [monitor.id]);

  // Get the current status (either from the hook or from the monitor prop)
  const currentStatus = hasStatusUpdate(monitor.id)
    ? getMonitorStatus(monitor.id)?.status
    : monitor.status;

  // Determine card background color based on status
  const getCardBackgroundColor = () => {
    if (!currentStatus) return 'bg-green-50 dark:bg-green-900/20'; // Default to operational

    const statusLower = typeof currentStatus === 'string' ? currentStatus.toLowerCase() : '';
    switch (statusLower) {
      case 'down':
        return 'bg-red-50 dark:bg-red-900/20';
      case 'degraded':
        return 'bg-amber-50 dark:bg-amber-900/20';
      case 'up':
        return 'bg-green-50 dark:bg-green-900/20';
      case 'paused':
        return 'bg-slate-50 dark:bg-slate-800/50';
      default:
        return 'bg-green-50 dark:bg-green-900/20'; // Default to operational
    }
  };

  return (
    <Link
      to={`/monitor/${monitor.id}`}
      state={{
        degradedStatus: currentStatus === 'degraded' ? currentStatus : null,
        errorMessage: currentStatus === 'degraded' ? latestErrorMessage : null,
        fromDashboard: true
      }}
      onClick={() => {
        // Clear any company_changed flag to prevent navigation issues
        localStorage.removeItem('company_changed');
      }}
    >
      <Card className={`overflow-hidden ${getCardBackgroundColor()} hover:shadow-md transition-shadow cursor-pointer`}>
        <CardContent className="p-0">
          <div className="flex flex-col md:flex-row md:items-center justify-between p-4 border-b border-slate-100 dark:border-slate-700">
            <div className="flex items-center space-x-4">
              {getStatusIcon(currentStatus)}
              <div>
                <h3 className="font-medium">{monitor.name}</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400">{monitor.target}</p>
                {/* Always render the companies section, even if empty */}
                <div className="mt-1 flex flex-wrap gap-1">
                  {monitor.companies && monitor.companies.length > 0 ? (
                    monitor.companies.map((company, index) => (
                      company.company_name && (
                        <Badge key={company.company_id || index} variant="outline" className="text-xs">
                          {company.company_name}
                        </Badge>
                      )
                    )).filter(Boolean)
                  ) : (
                    <Badge variant="outline" className="text-xs text-slate-400 border-slate-300">
                      No company assigned
                    </Badge>
                  )}
                </div>
              </div>
            </div>
            <div className="mt-2 md:mt-0 flex items-center space-x-2">
              {getStatusBadge(currentStatus, latestErrorMessage)}
              <Badge variant="outline" className="flex items-center">
                <Timer className="h-3 w-3 mr-1" />
                {monitor.interval} min
              </Badge>
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.preventDefault()}>
                  <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" onClick={(e) => e.preventDefault()}>
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Link to={`/edit-monitor/${monitor.id}`} className="flex items-center w-full" onClick={(e) => e.stopPropagation()}>
                      <Settings className="h-4 w-4 mr-2" />
                      Edit Monitor
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      // Don't allow multiple simultaneous tests
                      if (isTestingMonitor) return;

                      // Use the ManualCheckButton's functionality but with custom UI
                      const triggerCheck = async () => {
                        setIsTestingMonitor(true);
                        try {
                          toast({
                            title: 'Testing Monitor',
                            description: `Running check for ${monitor.name}...`,
                          });

                          // Call the monitor-checker edge function
                          const { data, error } = await supabase.functions.invoke('monitor-checker', {
                            body: { monitorId: monitor.id },
                          });

                          if (error) throw error;

                          if (data && data.results && data.results.length > 0) {
                            const result = data.results[0];
                            const status = result.status ? 'UP' : 'DOWN';
                            // Format the timestamp
                            const timestamp = new Date().toLocaleTimeString();

                            // Update the monitor status in our store for immediate UI update
                            updateMonitorStatus(
                              monitor.id,
                              result.status ? 'up' : 'down',
                              result.response_time,
                              monitor.type,
                              data.portResults // Include port results for port monitors
                            );

                            // Invalidate queries to refresh the data
                            queryClient.invalidateQueries({ queryKey: ['monitors'] });
                            queryClient.invalidateQueries({ queryKey: ['monitor-history-4h', monitor.id] });
                            queryClient.invalidateQueries({ queryKey: ['monitorHistory', 'latest'] });

                            // Force a refresh of all monitor-related queries
                            queryClient.refetchQueries({ queryKey: ['monitors'] });
                            queryClient.refetchQueries({ queryKey: ['monitor-history-4h', monitor.id] });
                            queryClient.refetchQueries({ queryKey: ['monitorHistory', 'latest'] });

                            // Refresh the chart data
                            fetchHistory();

                            toast({
                              title: `Monitor is ${status}`,
                              description: (
                                <div className="space-y-1">
                                  <p><strong>Time:</strong> {timestamp}</p>
                                  <p><strong>Response time:</strong> {result.response_time}ms</p>
                                  {result.error_message && (
                                    <p className="text-red-500"><strong>Error:</strong> {result.error_message}</p>
                                  )}

                                  {/* Show port-specific information for port monitors */}
                                  {monitor.type === 'port' && data.portResults && (
                                    <div className="mt-2 space-y-1">
                                      <p><strong>Port Details:</strong></p>
                                      <div className="max-h-32 overflow-y-auto">
                                        {data.portResults.map((port) => (
                                          <div key={port.port} className="flex items-center space-x-2">
                                            <span className={`inline-block w-2 h-2 rounded-full ${port.status ? 'bg-green-500' : 'bg-red-500'}`}></span>
                                            <span>Port {port.port}: {port.status ? 'Open' : 'Closed'}</span>
                                            {port.error && <span className="text-xs text-red-500">({port.error})</span>}
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              ),
                              variant: result.status ? 'default' : 'destructive',
                              action: (
                                <ToastAction altText="View details" onClick={() => {
                                  // Navigate to monitor details page
                                  window.location.href = `/monitor/${monitor.id}`;
                                }}>
                                  View Details
                                </ToastAction>
                              ),
                            });
                          } else {
                            throw new Error('No check results returned');
                          }
                        } catch (err) {
                          console.error('Error triggering monitor check:', err);

                          // Show a more detailed error message
                          let errorMessage = 'Failed to test monitor. Please try again.';
                          if (err instanceof Error) {
                            errorMessage = `Error: ${err.message}`;
                          }

                          toast({
                            title: 'Error Testing Monitor',
                            description: errorMessage,
                            variant: 'destructive',
                          });
                        } finally {
                          setIsTestingMonitor(false);
                        }
                      };

                      triggerCheck();
                    }}
                    disabled={isTestingMonitor}
                  >
                    {isTestingMonitor ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Test Now
                      </>
                    )}
                  </DropdownMenuItem>
                  {monitor.active ? (
                    <DropdownMenuItem onClick={(e) => { e.preventDefault(); onToggleStatus(monitor.id, false); }}>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause Monitor
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem onClick={(e) => { e.preventDefault(); onToggleStatus(monitor.id, true); }}>
                      <Play className="h-4 w-4 mr-2" />
                      Activate Monitor
                    </DropdownMenuItem>
                  )}
                  {isSuperadmin && (
                    <DropdownMenuItem>
                      <div className="flex items-center w-full" onClick={(e) => e.preventDefault()}>
                        <Mail className="h-4 w-4 mr-2" />
                        <span onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          // Open a dialog to send test email
                          const email = window.prompt('Enter email address for test notification:');
                          if (email) {
                            supabase.rpc('send_test_monitor_email', {
                              p_monitor_id: monitor.id,
                              p_email: email,
                              p_status: 'down'
                            }).then(({ error }) => {
                              if (error) {
                                toast({
                                  title: 'Error',
                                  description: 'Failed to request test email. Please try again.',
                                  variant: 'destructive',
                                });
                              }
                            });
                          }
                        }}>Test Email</span>
                      </div>
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={(e) => { e.preventDefault(); onDelete(monitor.id, monitor.name); }}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete Monitor
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {isSuperadmin && monitor.hasCustomDegradedSettings && (
                <Link to={`/edit-monitor/${monitor.id}?focus=degraded`} onClick={(e) => e.stopPropagation()}>
                  <Badge
                    variant="outline"
                    className="text-blue-600 border-blue-600 dark:text-blue-400 dark:border-blue-400 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                  >
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Custom Degraded Settings
                  </Badge>
                </Link>
              )}
            </div>
          </div>

        {/* Response Time Chart */}
        <div className="p-4 border-b border-slate-100 dark:border-slate-700">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-2">
            <h4 className="text-sm font-medium mb-1 md:mb-0">Response Time (Last 4 Hours)</h4>
            <div className="flex flex-wrap items-center gap-x-4 gap-y-1 text-sm">
              <div className="flex items-center">
                <span className="text-slate-500 dark:text-slate-400 mr-1">Uptime:</span>
                <span className="font-medium">{monitor.uptime}</span>
              </div>
              <div className="flex items-center">
                <span className="text-slate-500 dark:text-slate-400 mr-1">Response:</span>
                <span className="font-medium">{monitor.responseTime}</span>
              </div>
              <div className="flex items-center">
                <span className="text-slate-500 dark:text-slate-400 mr-1">Last Check:</span>
                <span className="font-medium">{monitor.lastChecked}</span>
              </div>
            </div>
          </div>
          <div className="h-[120px] w-full">
            {isLoadingHistory ? (
              <Skeleton className="h-full w-full" />
            ) : chartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={chartData}
                  margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
                >
                  {/* Removed CartesianGrid to eliminate dots behind the graph */}
                  <XAxis
                    dataKey="time"
                    tick={{ fontSize: 10 }}
                    tickMargin={5}
                  />
                  <YAxis
                    unit="ms"
                    tick={{ fontSize: 10 }}
                    tickMargin={5}
                    width={40}
                  />
                  <Tooltip
                    formatter={(value) => [`${value} ms`, 'Response Time']}
                    labelFormatter={(label) => `Time: ${label}`}
                    contentStyle={{ fontSize: '12px' }}
                  />
                  <Line
                    type="monotone"
                    dataKey="value"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    dot={false} // Remove circles from data points
                    activeDot={{ r: 4 }} // Keep active dot for hover state
                  />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <p className="text-sm text-slate-500 dark:text-slate-400">No data available</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
    </Link>
  );
};

export default MonitorCard;
