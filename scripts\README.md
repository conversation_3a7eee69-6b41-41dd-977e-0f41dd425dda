# Security Scripts

This directory contains scripts for ensuring the security of the VUM application.

## Available Scripts

### check-hardcoded-secrets.js

This script scans the codebase for hardcoded secrets and sensitive information.

```bash
# Run the script
npm run check-secrets
```

Features:
- Scans for hardcoded Supabase URLs, API keys, and other sensitive information
- Dynamically builds patterns based on environment variables
- Ignores documentation and example files
- Returns a non-zero exit code if secrets are found in critical files

### check-credentials.js

This script performs a broader scan for potential credential patterns in the codebase.

```bash
# Run the script
npm run check-credentials
```

Features:
- Scans for common credential patterns like "password", "api_key", etc.
- Provides warnings about files that might contain sensitive information
- More general than check-hardcoded-secrets.js, with more potential false positives

### security-check

This is a combined script that runs both check-credentials.js and check-hardcoded-secrets.js.

```bash
# Run both security checks
npm run security-check
```

## Pre-commit Hook

A pre-commit hook is configured to run the security-check script before each commit. This helps prevent accidentally committing sensitive information.

If the security check fails, the commit will be blocked, and you'll need to fix the issues before committing.

## Best Practices

1. **Never hardcode sensitive information** in your code. Always use environment variables.
2. **Keep .env files out of version control**. They should be listed in .gitignore.
3. **Use .env.example files** as templates with placeholder values.
4. **Rotate credentials regularly**, especially if you suspect they've been compromised.
5. **Run security checks regularly**, not just when committing.

## Adding New Patterns

If you need to add new patterns to check for, update the `buildSecretPatterns()` function in check-hardcoded-secrets.js or the `buildCredentialPatterns()` function in check-credentials.js.

## Troubleshooting

If you're getting false positives:
- Update the EXCLUDED_DIRS or EXCLUDED_FILES arrays in the scripts
- Modify the ignoredPatterns array in the main function

If you need to bypass the pre-commit hook in an emergency:
```bash
git commit --no-verify -m "Your commit message"
```
(Use this only in exceptional circumstances!)
