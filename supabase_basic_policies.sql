-- This script sets up basic policies for the avatars bucket
-- Run this in the Supabase SQL Editor

-- First, drop any existing policies for the avatars bucket
DROP POLICY IF EXISTS "Allow public access to avatars" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to upload avatars" ON storage.objects;

-- Create a simple policy to allow public access to the avatars bucket
CREATE POLICY "Allow public access to avatars"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'avatars');

-- Create a simple policy to allow authenticated users to upload to the avatars bucket
CREATE POLICY "Allow authenticated users to upload avatars"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'avatars');

-- Create a simple policy to allow authenticated users to update files in the avatars bucket
CREATE POLICY "Allow authenticated users to update avatars"
ON storage.objects
FOR UPDATE
TO authenticated
USING (bucket_id = 'avatars');

-- Create a simple policy to allow authenticated users to delete files in the avatars bucket
CREATE POLICY "Allow authenticated users to delete avatars"
ON storage.objects
FOR DELETE
TO authenticated
USING (bucket_id = 'avatars');

-- Make sure the avatars bucket is public
UPDATE storage.buckets
SET public = true
WHERE id = 'avatars';
