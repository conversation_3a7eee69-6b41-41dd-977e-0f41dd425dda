-- This script fixes the infinite recursion in the user_roles policy
-- Run this in the Supabase SQL Editor

-- First, disable <PERSON><PERSON> on the user_roles table temporarily
ALTER TABLE public.user_roles DISABLE ROW LEVEL SECURITY;

-- Drop all existing policies on the user_roles table
DROP POLICY IF EXISTS "Users can view their own global roles" ON public.user_roles;
DROP POLICY IF EXISTS "Only superadmins can manage global roles" ON public.user_roles;
DROP POLICY IF EXISTS "Service role can manage global roles" ON public.user_roles;
DROP POLICY IF EXISTS "First superadmin can be created" ON public.user_roles;
DROP POLICY IF EXISTS "Superadmins can manage global roles" ON public.user_roles;

-- Make <EMAIL> a superadmin while <PERSON><PERSON> is disabled
DO $$
DECLARE
    target_user_id UUID;
BEGIN
    -- Get the user ID from auth.users
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    -- Check if the user exists
    IF target_user_id IS NULL THEN
        RAISE NOTICE 'User <NAME_EMAIL> not found';
        RETURN;
    END IF;
    
    -- Delete any existing roles for this user to avoid conflicts
    DELETE FROM public.user_roles WHERE user_id = target_user_id;
    
    -- Insert the user as a global superadmin
    INSERT INTO public.user_roles (user_id, role_type)
    VALUES (target_user_id, 'superadmin');
    
    RAISE NOTICE 'User <EMAIL> (ID: %) has been made a global superadmin', target_user_id;
END $$;

-- Create a function to check superadmin status without using RLS
CREATE OR REPLACE FUNCTION is_global_superadmin_bypass(
  check_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_to_check UUID;
  is_superadmin BOOLEAN;
BEGIN
  -- If no user_id is provided, use the authenticated user
  user_to_check := COALESCE(check_user_id, auth.uid());
  
  -- Check if the user has the superadmin role
  -- This function bypasses RLS by using SECURITY DEFINER
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = user_to_check
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Now re-enable RLS
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create a simple policy for viewing own roles
CREATE POLICY "Users can view their own global roles"
ON public.user_roles
FOR SELECT
USING (
    user_id = auth.uid()
);

-- Create a policy for the service role
CREATE POLICY "Service role can manage global roles"
ON public.user_roles
FOR ALL
USING (
    current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
);

-- Create a policy for superadmins using the bypass function
CREATE POLICY "Superadmins can manage global roles"
ON public.user_roles
FOR ALL
USING (
    is_global_superadmin_bypass()
);

-- Update all functions that check for superadmin status to use the bypass function
CREATE OR REPLACE FUNCTION assign_global_superadmin(
  target_user_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user is a superadmin using the bypass function
  SELECT is_global_superadmin_bypass(admin_user_id) INTO is_admin_superadmin;
  
  -- Only allow superadmins to assign superadmin roles
  IF NOT is_admin_superadmin THEN
    RAISE EXCEPTION 'Only superadmins can assign superadmin roles';
  END IF;
  
  -- Insert or update the user role
  INSERT INTO public.user_roles (user_id, role_type)
  VALUES (target_user_id, 'superadmin')
  ON CONFLICT (user_id, role_type) 
  DO UPDATE SET updated_at = NOW()
  RETURNING jsonb_build_object(
    'id', id,
    'user_id', user_id,
    'role_type', role_type,
    'created_at', created_at,
    'updated_at', updated_at
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the monitor_companies policy to use the bypass function
DROP POLICY IF EXISTS "Global superadmins can manage all monitor-company relationships" ON public.monitor_companies;

CREATE POLICY "Global superadmins can manage all monitor-company relationships"
ON public.monitor_companies
FOR ALL
USING (
    is_global_superadmin_bypass()
);

-- Update the monitors policy to use the bypass function
DROP POLICY IF EXISTS "Global superadmins can hard delete monitors" ON public.monitors;

CREATE POLICY "Global superadmins can hard delete monitors"
ON public.monitors
FOR DELETE
USING (
    is_global_superadmin_bypass()
);

-- Verify the user is now a superadmin
SELECT 
    u.id,
    u.email,
    ur.role_type AS global_role,
    ur.created_at,
    ur.updated_at
FROM 
    auth.users u
JOIN 
    public.user_roles ur ON u.id = ur.user_id
WHERE 
    u.email = '<EMAIL>'
    AND ur.role_type = 'superadmin';
