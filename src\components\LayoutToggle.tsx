import React from 'react';
import { Button } from '@/components/ui/button';
import { LayoutGrid, List } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

export type LayoutType = 'standard' | 'condensed';

interface LayoutToggleProps {
  currentLayout: LayoutType;
  onLayoutChange: (layout: LayoutType) => void;
}

export function LayoutToggle({ currentLayout, onLayoutChange }: LayoutToggleProps) {
  return (
    <TooltipProvider>
      <div className="flex items-center space-x-1 bg-muted rounded-md p-1">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`px-2 ${currentLayout === 'standard' ? 'bg-background shadow-sm' : ''}`}
              onClick={() => onLayoutChange('standard')}
            >
              <List className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Standard View</p>
          </TooltipContent>
        </Tooltip>
        
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className={`px-2 ${currentLayout === 'condensed' ? 'bg-background shadow-sm' : ''}`}
              onClick={() => onLayoutChange('condensed')}
            >
              <LayoutGrid className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Condensed View</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}

export default LayoutToggle;
