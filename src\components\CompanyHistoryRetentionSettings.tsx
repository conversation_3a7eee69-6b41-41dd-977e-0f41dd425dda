import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Save, RefreshCw, Info } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';

interface CompanyHistoryRetentionSettingsProps {
  companyId: string;
  companyName?: string;
  isSuperadmin: boolean;
}

const CompanyHistoryRetentionSettings: React.FC<CompanyHistoryRetentionSettingsProps> = ({
  companyId,
  companyName,
  isSuperadmin
}) => {
  const [globalRetentionDays, setGlobalRetentionDays] = useState<number>(7);
  const [companyRetentionDays, setCompanyRetentionDays] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        // Fetch global settings
        const { data: globalData, error: globalError } = await supabase
          .from('app_settings')
          .select('value')
          .eq('key', 'global_history_retention')
          .single();

        if (globalError && globalError.code !== 'PGRST116') {
          throw globalError;
        }

        if (globalData) {
          setGlobalRetentionDays(parseInt(globalData.value?.days) || 7);
        }

        // Fetch company-specific settings
        const { data: companyData, error: companyError } = await supabase
          .from('companies')
          .select('history_retention_days')
          .eq('id', companyId)
          .single();

        if (companyError) throw companyError;

        setCompanyRetentionDays(companyData.history_retention_days);
      } catch (error) {
        console.error('Error fetching history retention settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load history retention settings.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isSuperadmin && companyId) {
      fetchSettings();
    }
  }, [companyId, isSuperadmin]);

  // Save company settings
  const saveCompanySettings = async () => {
    if (!isSuperadmin || !companyId) return;

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          history_retention_days: companyRetentionDays,
        })
        .eq('id', companyId);

      if (error) throw error;

      toast({
        title: 'Settings Saved',
        description: `History retention settings for ${companyName} have been updated.`,
      });
    } catch (error) {
      console.error('Error saving company history retention settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save company history retention settings.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Reset company settings to use global default
  const resetToGlobal = () => {
    setCompanyRetentionDays(null);
  };

  if (!isSuperadmin) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Info className="h-5 w-5 mr-2 text-blue-500" />
          Company History Retention
          <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
            Superadmin Only
          </Badge>
        </CardTitle>
        <CardDescription>
          Configure how long monitor history data is retained for this company.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-sm text-slate-500 dark:text-slate-400">
              Override the global default for {companyName}.
            </p>
            <div className="flex items-end gap-4">
              <div className="flex-1">
                <Label htmlFor="company-retention">
                  {companyName} History Retention (days)
                </Label>
                <Input
                  id="company-retention"
                  type="number"
                  min="1"
                  max="365"
                  value={companyRetentionDays === null ? '' : companyRetentionDays}
                  onChange={(e) => setCompanyRetentionDays(e.target.value === '' ? null : parseInt(e.target.value))}
                  placeholder={`Using global default (${globalRetentionDays} days)`}
                  className="mt-1"
                />
              </div>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={resetToGlobal} disabled={isSaving || companyRetentionDays === null}>
                  Use Global Default
                </Button>
                <Button onClick={saveCompanySettings} disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Setting
                    </>
                  )}
                </Button>
              </div>
            </div>
            {companyRetentionDays === null && (
              <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                This company is using the global default of {globalRetentionDays} days.
              </p>
            )}
            <p className="text-sm text-slate-500 dark:text-slate-400 mt-4 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 rounded-md">
              <strong>Note:</strong> Global retention settings can only be modified in the Global Settings page.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CompanyHistoryRetentionSettings;
