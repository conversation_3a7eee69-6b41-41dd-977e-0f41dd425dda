import React from 'react';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, Shield } from 'lucide-react';

interface UserSearchProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showOnlySuperadmins: boolean;
  setShowOnlySuperadmins: (show: boolean) => void;
}

const UserSearch: React.FC<UserSearchProps> = ({
  searchQuery,
  setSearchQuery,
  showOnlySuperadmins,
  setShowOnlySuperadmins,
}) => {
  return (
    <div className="flex items-center gap-4">
      <div className="relative w-full max-w-sm">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-500" />
        <Input
          placeholder="Search users..."
          className="pl-8"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>

      {/* Only show this checkbox to superadmins */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="superadmin-filter"
          checked={showOnlySuperadmins}
          onCheckedChange={(checked) => setShowOnlySuperadmins(checked === true)}
        />
        <label
          htmlFor="superadmin-filter"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center"
        >
          <Shield className="h-3.5 w-3.5 mr-1 text-red-500" />
          Show only superadmins
        </label>
      </div>
    </div>
  );
};

export default UserSearch;
