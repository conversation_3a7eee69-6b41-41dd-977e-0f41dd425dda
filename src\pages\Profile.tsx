import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, Upload, User, Lock, Mail, Shield } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import AppLayout from "@/components/AppLayout";
import UnifiedHeader from "@/components/UnifiedHeader";
import DocumentTitle from "@/components/DocumentTitle";
import DateTimeFormatSettings from "@/components/DateTimeFormatSettings";
import { supabase } from "@/integrations/supabase/client";

// Make supabase available in the window object for testing
if (typeof window !== 'undefined') {
  (window as any).supabase = supabase;
}

const Profile = () => {
  const navigate = useNavigate();
  const { user, signOut, updateProfile, updatePassword } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [avatarUrl, setAvatarUrl] = useState<string | null>(null);

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    fullName: "",
    email: "",
  });

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Function to check if an image URL is valid
  const checkImageUrl = async (url: string) => {
    console.log('Checking image URL:', url);
    try {
      // Create a new image element
      const img = new Image();

      // Create a promise that resolves when the image loads or rejects when it fails
      const promise = new Promise((resolve, reject) => {
        img.onload = () => {
          console.log('Image loaded successfully:', url);
          resolve(true);
        };
        img.onerror = () => {
          console.error('Image failed to load:', url);
          reject(new Error('Image failed to load'));
        };
      });

      // Set the source to start loading
      img.src = url;

      // Wait for the image to load or fail
      await promise;
      return true;
    } catch (error) {
      console.error('Error checking image URL:', error);
      return false;
    }
  };

  // Load user data and ensure user exists in the users table
  useEffect(() => {
    if (user) {
      console.log('User metadata:', user.user_metadata);

      setProfileForm({
        fullName: user.user_metadata?.full_name || "",
        email: user.email || "",
      });

      // Check if user has an avatar
      if (user.user_metadata?.avatar_url) {
        console.log('Found avatar URL in metadata:', user.user_metadata.avatar_url);

        // Check if the avatar URL is valid
        const avatarUrl = user.user_metadata.avatar_url;
        setAvatarUrl(avatarUrl);

        // Add a timestamp to force refresh
        const refreshedUrl = `${avatarUrl}?t=${new Date().getTime()}`;
        setAvatarUrl(refreshedUrl);

        // Check if the image is accessible
        checkImageUrl(refreshedUrl).then(isValid => {
          if (!isValid) {
            console.warn('Avatar image is not accessible, might need to check storage permissions');
            toast({
              title: "Avatar image not accessible",
              description: "Your avatar image could not be loaded. You may need to upload a new one.",
              variant: "destructive",
            });
          }
        });
      } else {
        console.log('No avatar URL found in user metadata');
      }

      // Check if user exists in the users table and create if not
      const checkAndCreateUserProfile = async () => {
        try {
          // First check if user exists in the users table
          const { data, error } = await supabase
            .from('users')
            .select('id')
            .eq('id', user.id)
            .single();

          if (error || !data) {
            console.log('User not found in users table, creating profile...');

            // Create user in the users table
            const { error: insertError } = await supabase
              .from('users')
              .insert({
                id: user.id,
                email: user.email || '',
                full_name: user.user_metadata?.full_name || '',
              });

            if (insertError) {
              console.error('Error creating user profile:', insertError);
            } else {
              console.log('User profile created successfully');
            }
          } else {
            console.log('User profile exists in database');
          }
        } catch (error) {
          console.error('Error checking/creating user profile:', error);
        }
      };

      checkAndCreateUserProfile();
    }
  }, [user]);

  // Handle profile form changes
  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm((prev) => ({ ...prev, [name]: value }));
  };

  // Handle password form changes
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm((prev) => ({ ...prev, [name]: value }));
  };

  // Update profile
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to update your profile.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const { error } = await updateProfile({
        fullName: profileForm.fullName,
      });

      if (error) {
        throw error;
      }

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully.",
      });
    } catch (error) {
      console.error("Error updating profile:", error);
      toast({
        title: "Error updating profile",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Change password
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to change your password.",
        variant: "destructive",
      });
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation must match.",
        variant: "destructive",
      });
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      toast({
        title: "Password too short",
        description: "Password must be at least 6 characters long.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      // First verify the current password by trying to sign in
      const { error: signInError } = await supabase.auth.signInWithPassword({
        email: user.email!,
        password: passwordForm.currentPassword,
      });

      if (signInError) {
        toast({
          title: "Current password is incorrect",
          description: "Please enter your current password correctly.",
          variant: "destructive",
        });
        setIsLoading(false);
        return;
      }

      // Update the password using our context method
      const { error } = await updatePassword(passwordForm.newPassword);

      if (error) {
        throw error;
      }

      // Clear the password form
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    } catch (error) {
      console.error("Error changing password:", error);
      toast({
        title: "Error changing password",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Upload avatar
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }

    const file = e.target.files[0];
    const fileExt = file.name.split('.').pop();
    // Try different file paths to accommodate different policy setups
    // 1. Direct file name (no folders)
    const fileName = `${user?.id}.${fileExt}`;
    console.log('Generated file name:', fileName);

    setIsLoading(true);

    try {
      console.log('Uploading file to path:', fileName);

      // First check if we can list files in the avatars bucket
      try {
        const { data: files, error: listError } = await supabase.storage
          .from('avatars')
          .list();

        if (listError) {
          console.warn('Could not list files in avatars bucket:', listError);
          // Continue anyway, as we might still be able to upload
        } else {
          console.log('Files in avatars bucket:', files);
        }
      } catch (error) {
        console.warn('Error checking avatars bucket:', error);
        // Continue anyway, as we might still be able to upload
      }

      // Try to upload the file to Supabase Storage
      let uploadData;
      let uploadError;

      // First try with the simple file name
      console.log('Attempting upload with file name:', fileName);
      const uploadResult = await supabase.storage
        .from('avatars')
        .upload(fileName, file, { upsert: true });

      uploadData = uploadResult.data;
      uploadError = uploadResult.error;

      if (uploadError) {
        console.warn('First upload attempt failed:', uploadError);

        // Try with a different path format
        const altFileName = `avatar-${user?.id}.${fileExt}`;
        console.log('Trying alternative file name:', altFileName);

        const altUploadResult = await supabase.storage
          .from('avatars')
          .upload(altFileName, file, { upsert: true });

        if (altUploadResult.error) {
          console.error('All upload attempts failed:', altUploadResult.error);
          throw uploadError; // Throw the original error
        } else {
          console.log('Alternative upload succeeded:', altUploadResult.data);
          uploadData = altUploadResult.data;
          uploadError = null;
        }
      } else {
        console.log('Upload succeeded on first attempt:', uploadData);
      }

      console.log('Upload successful:', uploadData);

      // Get the public URL for the file that was successfully uploaded
      let publicUrlData;
      if (uploadData) {
        // Use the path from the upload data if available
        const path = uploadData.path || fileName;
        console.log('Getting public URL for path:', path);

        publicUrlData = supabase.storage
          .from('avatars')
          .getPublicUrl(path).data;
      } else {
        // Fallback to the original file name
        console.log('Using original file name for public URL:', fileName);
        publicUrlData = supabase.storage
          .from('avatars')
          .getPublicUrl(fileName).data;
      }

      const avatarUrl = publicUrlData.publicUrl;
      console.log('Generated public URL for avatar:', avatarUrl);

      // Update the user's metadata with the avatar URL
      console.log('Updating user metadata with avatar URL:', avatarUrl);

      // First try using the updateProfile method from AuthContext
      const { error: updateError } = await updateProfile({
        avatarUrl: avatarUrl,
      });

      if (updateError) {
        console.error('Error updating profile with AuthContext:', updateError);

        // If that fails, try updating directly with Supabase
        console.log('Trying direct update with Supabase...');
        const { data: userData, error: directError } = await supabase.auth.updateUser({
          data: {
            avatar_url: avatarUrl,
          },
        });

        if (directError) {
          console.error('Error with direct update:', directError);
          throw directError;
        }

        console.log('Direct update successful:', userData);
      }

      // Set the avatar URL in the component state
      console.log('Setting avatar URL in component state:', avatarUrl);
      setAvatarUrl(avatarUrl);

      // Force a refresh of the avatar image by creating a new URL with a timestamp
      const refreshedUrl = `${avatarUrl}?t=${new Date().getTime()}`;
      console.log('Refreshed URL with timestamp:', refreshedUrl);
      setAvatarUrl(refreshedUrl);

      // Verify that the avatar URL was saved correctly
      setTimeout(async () => {
        console.log('Verifying avatar URL was saved correctly...');
        const { data: { user: updatedUser } } = await supabase.auth.getUser();

        if (updatedUser && updatedUser.user_metadata?.avatar_url) {
          console.log('Avatar URL verified in user metadata:', updatedUser.user_metadata.avatar_url);
        } else {
          console.warn('Avatar URL not found in user metadata after update!');
          console.log('Current user metadata:', updatedUser?.user_metadata);

          // Try one more direct update
          console.log('Attempting one more direct update...');
          const { error: finalError } = await supabase.auth.updateUser({
            data: {
              avatar_url: avatarUrl,
            },
          });

          if (finalError) {
            console.error('Final update attempt failed:', finalError);
          } else {
            console.log('Final update attempt successful');
          }
        }
      }, 1000); // Wait 1 second before verifying

      // Check if the image is accessible
      checkImageUrl(refreshedUrl).then(isValid => {
        if (isValid) {
          console.log('Avatar image is accessible!');
          // Create an img element to display the image
          const img = document.createElement('img');
          img.src = refreshedUrl;
          img.style.width = '100px';
          img.style.height = '100px';
          img.style.borderRadius = '50%';
          img.style.objectFit = 'cover';

          // Add a message to the console with the image
          console.log('Avatar image preview:', img);
        } else {
          console.warn('Avatar image is not accessible, might need to check storage permissions');
          toast({
            title: "Avatar image not accessible",
            description: "Your avatar image could not be loaded. You may need to check storage permissions.",
            variant: "destructive",
          });
        }
      });

      toast({
        title: "Avatar updated",
        description: "Your profile picture has been updated successfully.",
      });
    } catch (error) {
      console.error("Error uploading avatar:", error);

      let errorMessage = "An unexpected error occurred";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error);
      }

      toast({
        title: "Error uploading avatar",
        description: errorMessage,
        variant: "destructive",
      });

      // Let's check if the bucket exists
      const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
      if (bucketsError) {
        console.error('Error listing buckets:', bucketsError);
      } else {
        console.log('Available buckets:', buckets);
        const avatarBucket = buckets.find(b => b.name === 'avatars');
        if (!avatarBucket) {
          console.error('Avatars bucket does not exist! Please run the supabase_storage_setup.sql script.');
          toast({
            title: "Storage not configured",
            description: "The avatars storage bucket has not been set up. Please contact the administrator.",
            variant: "destructive",
          });
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (!user) return "U";

    const name = profileForm.fullName || user.email || "";

    if (name) {
      const nameParts = name.split(" ");
      if (nameParts.length > 1) {
        return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
      }
      return name[0].toUpperCase();
    }

    return "U";
  };

  const header = (
    <UnifiedHeader
      title="Profile Settings"
      icon={User}
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Profile Settings" />
      <div className="container mx-auto py-8 px-4">

        <div className="max-w-3xl mx-auto">

          <div className="mb-8 flex flex-col items-center">
            <div className="relative mb-4">
              {/* Use a direct img tag for better debugging */}
              {avatarUrl ? (
                <div className="relative">
                  <img
                    src={avatarUrl}
                    alt="Profile"
                    className="h-24 w-24 rounded-full object-cover border-2 border-blue-500"
                    onLoad={() => console.log('Avatar image loaded successfully')}
                    onError={(e) => {
                      console.error('Error loading avatar image:', e);
                      // Try to load the image directly to see if it's accessible
                      window.open(avatarUrl, '_blank');
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-full opacity-0 hover:opacity-100 transition-opacity">
                    <span className="text-white text-xs">Avatar</span>
                  </div>
                </div>
              ) : (
                <Avatar className="h-24 w-24">
                  <AvatarFallback className="text-2xl bg-blue-500 text-white">
                    {getUserInitials()}
                  </AvatarFallback>
                </Avatar>
              )}
              {avatarUrl && (
                <div className="mt-2 text-xs text-slate-500 space-y-1">
                  <div>
                    <a href={avatarUrl} target="_blank" rel="noopener noreferrer" className="underline">
                      View avatar image
                    </a>
                  </div>
                  <div>
                    <button
                      onClick={() => window.location.reload()}
                      className="text-blue-500 underline"
                    >
                      Reload page
                    </button>
                  </div>
                </div>
              )}
              <label htmlFor="avatar-upload" className="absolute bottom-0 right-0 bg-blue-500 text-white p-1 rounded-full cursor-pointer hover:bg-blue-600">
                <Upload className="h-4 w-4" />
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleAvatarUpload}
                  disabled={isLoading}
                />
              </label>
            </div>
            <h2 className="text-xl font-semibold">{profileForm.fullName || user?.email}</h2>
            <p className="text-slate-500 dark:text-slate-400">{user?.email}</p>
          </div>

          <Tabs defaultValue="profile" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="profile">
                <User className="h-4 w-4 mr-2" />
                Profile
              </TabsTrigger>
              <TabsTrigger value="security">
                <Lock className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
            </TabsList>

            <TabsContent value="profile">
              <Card>
                <CardHeader>
                  <CardTitle>Profile Information</CardTitle>
                  <CardDescription>
                    Update your personal information and how others see you on the platform.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleUpdateProfile} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        name="fullName"
                        placeholder="Your full name"
                        value={profileForm.fullName}
                        onChange={handleProfileChange}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="Your email address"
                        value={profileForm.email}
                        disabled
                      />
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Email address cannot be changed.
                      </p>
                    </div>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? "Saving..." : "Save Changes"}
                    </Button>
                  </form>
                </CardContent>
              </Card>

              <div className="mt-6">
                <DateTimeFormatSettings />
              </div>
            </TabsContent>

            <TabsContent value="security">
              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                  <CardDescription>
                    Update your password to keep your account secure.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleChangePassword} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="currentPassword">Current Password</Label>
                      <Input
                        id="currentPassword"
                        name="currentPassword"
                        type="password"
                        placeholder="Your current password"
                        value={passwordForm.currentPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <Input
                        id="newPassword"
                        name="newPassword"
                        type="password"
                        placeholder="Your new password"
                        value={passwordForm.newPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm your new password"
                        value={passwordForm.confirmPassword}
                        onChange={handlePasswordChange}
                        required
                      />
                    </div>
                    <Button type="submit" disabled={isLoading}>
                      {isLoading ? "Updating..." : "Change Password"}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </AppLayout>
  );
};

export default Profile;
