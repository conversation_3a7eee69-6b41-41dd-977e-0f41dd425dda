// Monitor Service - A background process that checks monitors at their configured intervals
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;
const LOG_DIR = path.join(__dirname, 'logs');
const LOG_FILE = path.join(LOG_DIR, 'monitor-service.log');
const CHECK_INTERVAL = 10000; // Check every 10 seconds if any monitors need to be checked

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Create log directory if it doesn't exist
if (!fs.existsSync(LOG_DIR)) {
  fs.mkdirSync(LOG_DIR, { recursive: true });
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

// Store monitor schedules
const monitorSchedules = new Map();

// Logging function
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp} [${level}] ${message}`;

  console.log(logMessage);
  fs.appendFileSync(LOG_FILE, logMessage + '\n');
}

// Function to perform a check on a monitor
async function performCheck(monitor) {
  log(`Checking monitor: ${monitor.name} (${monitor.id})`);

  const startTime = Date.now();
  let status = false;
  let responseTime = null;
  let errorMessage = null;

  try {
    switch (monitor.type) {
      case 'http':
        // HTTP check
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);

        try {
          const response = await axios.get(monitor.target, {
            signal: controller.signal,
            timeout: monitor.timeout * 1000,
            validateStatus: null // Don't throw on non-2xx responses
          });

          clearTimeout(timeoutId);
          status = response.status >= 200 && response.status < 300;
          responseTime = Date.now() - startTime;

          if (!status) {
            errorMessage = `HTTP status: ${response.status}`;
          }
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
        break;

      case 'ping':
        // Ping check (implemented as a simple HTTP HEAD request)
        try {
          log(`Sending ping request to ${monitor.target}...`);

          // Use a HEAD request for faster response
          const pingOptions = {
            method: 'HEAD',
            timeout: monitor.timeout * 1000,
            validateStatus: null, // Don't throw on non-2xx responses
            headers: {
              'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
              'Cache-Control': 'no-cache'
            },
            maxRedirects: 5
          };

          log(`Ping options: ${JSON.stringify(pingOptions)}`);

          const pingResponse = await axios(monitor.target, pingOptions);

          // Consider any response as successful for ping
          status = true;
          responseTime = Date.now() - startTime;

          log(`Ping response received: Status ${pingResponse.status}, Time ${responseTime}ms`);
        } catch (error) {
          log(`Ping request error: ${error.message}`, 'ERROR');
          throw new Error(`Ping failed: ${error.message}`);
        }
        break;

      case 'port':
        // Port check
        try {
          log(`Checking port(s) for ${monitor.target}...`);

          // Parse the target (format: host|port1,port2,port3)
          const [host, portsStr] = monitor.target.split('|');
          const ports = portsStr.split(',').map(p => parseInt(p.trim()));

          log(`Host: ${host}, Ports: ${ports.join(', ')}`);

          // Check if at least one port is open
          let anyPortOpen = false;
          let portResults = [];

          // For now, we'll just do a DNS check since we can't do proper port checks in Node.js without additional modules
          try {
            // Use DNS resolution as a basic check
            const { lookup } = require('dns');
            const util = require('util');
            const lookupPromise = util.promisify(lookup);

            await lookupPromise(host);

            // If DNS resolves, we'll assume at least one port is open
            // This is not accurate but better than nothing for now
            anyPortOpen = true;
            responseTime = Date.now() - startTime;

            log(`Host ${host} resolved successfully. Assuming ports are accessible.`);
          } catch (dnsError) {
            log(`DNS resolution failed for ${host}: ${dnsError.message}`, 'ERROR');
            throw new Error(`Host not found: ${dnsError.message}`);
          }

          status = anyPortOpen;
        } catch (error) {
          log(`Port check error: ${error.message}`, 'ERROR');
          throw new Error(`Port check failed: ${error.message}`);
        }
        break;

      default:
        throw new Error(`Unsupported monitor type: ${monitor.type}`);
    }
  } catch (error) {
    status = false;
    errorMessage = `Error: ${error.message}`;
    log(`Check failed for monitor ${monitor.name}: ${error.message}`, 'ERROR');
  }

  // Ensure status is a boolean
  const booleanStatus = status === true || status === false ? status : !!status;
  log(`Saving check result for monitor ${monitor.name} with status: ${booleanStatus} (${typeof booleanStatus})`);

  const checkResult = {
    monitor_id: monitor.id,
    status: booleanStatus, // Ensure this is a boolean
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString()
  };

  // Save the check result to the database
  try {
    const { error } = await supabase
      .from('monitor_history')
      .insert(checkResult);

    if (error) {
      throw error;
    }

    log(`Saved check result for ${monitor.name}: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);

    // Check if status changed and send notification if needed
    await checkStatusChange(monitor, status);

  } catch (error) {
    log(`Failed to save check result: ${error.message}`, 'ERROR');
  }

  return {
    monitor_id: monitor.id,
    name: monitor.name,
    status,
    response_time: responseTime
  };
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(monitor, currentStatus) {
  try {
    // Ensure currentStatus is a boolean
    const booleanStatus = currentStatus === true || currentStatus === false ? currentStatus : !!currentStatus;
    log(`Checking status change for monitor ${monitor.name} with current status: ${booleanStatus} (${typeof booleanStatus})`);

    // Get the previous check
    const { data: previousChecks, error } = await supabase
      .from('monitor_history')
      .select('status')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .limit(2);

    if (error) {
      throw error;
    }

    // If this is the first check or status changed, send notification
    if (previousChecks.length < 2 || previousChecks[1].status !== booleanStatus) {
      log(`Status changed for monitor ${monitor.name}: ${booleanStatus ? 'UP' : 'DOWN'}`);
      await sendNotification(monitor, booleanStatus);
      return true;
    }

    return false;
  } catch (error) {
    log(`Failed to check status change: ${error.message}`, 'ERROR');
    return false;
  }
}

// Send a notification
async function sendNotification(monitor, status) {
  try {
    // Ensure status is a boolean
    const booleanStatus = status === true || status === false ? status : !!status;
    log(`Sending notification for monitor ${monitor.name}: Status is now ${booleanStatus ? 'UP' : 'DOWN'}`);

    // Insert notification into the database
    const { error } = await supabase
      .from('notifications')
      .insert({
        monitor_id: monitor.id,
        company_id: monitor.company_id,
        message: `Monitor ${monitor.name} is now ${booleanStatus ? 'UP' : 'DOWN'}`,
        type: booleanStatus ? 'up' : 'down',
        read: false,
        created_at: new Date().toISOString()
      });

    if (error) {
      throw error;
    }

    log(`Notification sent for ${monitor.name}`);
  } catch (error) {
    log(`Failed to send notification: ${error.message}`, 'ERROR');
  }
}

// Schedule a monitor check
function scheduleMonitor(monitor) {
  // Cancel existing schedule if it exists
  if (monitorSchedules.has(monitor.id)) {
    clearTimeout(monitorSchedules.get(monitor.id));
  }

  // Calculate when this monitor should next be checked
  getNextCheckTime(monitor).then(nextCheckTime => {
    const now = Date.now();
    const delay = Math.max(0, nextCheckTime - now);

    log(`Scheduling ${monitor.name} to be checked in ${Math.round(delay / 1000)} seconds`);

    // Schedule the check
    const timeoutId = setTimeout(async () => {
      try {
        await performCheck(monitor);
      } finally {
        // Re-schedule after check completes (success or failure)
        scheduleMonitor(monitor);
      }
    }, delay);

    monitorSchedules.set(monitor.id, timeoutId);
  }).catch(error => {
    log(`Failed to schedule monitor ${monitor.name}: ${error.message}`, 'ERROR');

    // Retry scheduling after a delay
    const timeoutId = setTimeout(() => {
      scheduleMonitor(monitor);
    }, 60000); // Retry after 1 minute

    monitorSchedules.set(monitor.id, timeoutId);
  });
}

// Calculate when a monitor should next be checked
async function getNextCheckTime(monitor) {
  try {
    // Get the most recent check for this monitor
    const { data, error } = await supabase
      .from('monitor_history')
      .select('timestamp')
      .eq('monitor_id', monitor.id)
      .order('timestamp', { ascending: false })
      .limit(1);

    if (error) {
      throw error;
    }

    const now = Date.now();

    // If no previous check, check immediately
    if (!data || data.length === 0) {
      return now;
    }

    const lastCheckTime = new Date(data[0].timestamp).getTime();
    const intervalMs = monitor.interval * 60 * 1000;
    const nextCheckTime = lastCheckTime + intervalMs;

    // If next check time is in the past, check immediately
    return Math.max(now, nextCheckTime);
  } catch (error) {
    log(`Failed to get next check time for ${monitor.name}: ${error.message}`, 'ERROR');
    return Date.now(); // Check immediately on error
  }
}

// Load all active monitors and schedule them
async function loadAndScheduleMonitors() {
  try {
    log('Loading monitors from database...');

    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    log(`Loaded ${monitors.length} active monitors`);

    // Schedule each monitor
    monitors.forEach(monitor => {
      scheduleMonitor(monitor);
    });
  } catch (error) {
    log(`Failed to load monitors: ${error.message}`, 'ERROR');

    // Retry after a delay
    setTimeout(loadAndScheduleMonitors, 60000); // Retry after 1 minute
  }
}

// Periodically check for new or updated monitors
async function checkForMonitorUpdates() {
  try {
    const { data: monitors, error } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    if (error) {
      throw error;
    }

    // Get current monitor IDs
    const currentMonitorIds = new Set(monitorSchedules.keys());
    const newMonitorIds = new Set(monitors.map(m => m.id));

    // Find monitors to add and remove
    const monitorsToAdd = monitors.filter(m => !currentMonitorIds.has(m.id));
    const monitorsToRemove = Array.from(currentMonitorIds).filter(id => !newMonitorIds.has(id));

    // Schedule new monitors
    monitorsToAdd.forEach(monitor => {
      log(`New monitor detected: ${monitor.name}`);
      scheduleMonitor(monitor);
    });

    // Remove old monitors
    monitorsToRemove.forEach(id => {
      log(`Monitor removed: ${id}`);
      if (monitorSchedules.has(id)) {
        clearTimeout(monitorSchedules.get(id));
        monitorSchedules.delete(id);
      }
    });

    // Check for updated monitors
    monitors.forEach(monitor => {
      if (currentMonitorIds.has(monitor.id)) {
        // Re-schedule to pick up any changes to interval, etc.
        scheduleMonitor(monitor);
      }
    });
  } catch (error) {
    log(`Failed to check for monitor updates: ${error.message}`, 'ERROR');
  }

  // Schedule next check
  setTimeout(checkForMonitorUpdates, CHECK_INTERVAL);
}

// Start the service
async function startService() {
  log('Starting Monitor Service...');

  try {
    // Test Supabase connection
    const { data, error } = await supabase.from('monitors').select('count()', { count: 'exact' });

    if (error) {
      throw error;
    }

    log(`Connected to Supabase. Found ${data[0].count} monitors.`);

    // Load and schedule monitors
    await loadAndScheduleMonitors();

    // Start checking for monitor updates
    checkForMonitorUpdates();

    log('Monitor Service started successfully');
  } catch (error) {
    log(`Failed to start service: ${error.message}`, 'ERROR');
    log('Retrying in 30 seconds...');

    // Retry after a delay
    setTimeout(startService, 30000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  log('Shutting down Monitor Service...');

  // Cancel all scheduled checks
  for (const [id, timeoutId] of monitorSchedules.entries()) {
    clearTimeout(timeoutId);
  }

  log('Monitor Service stopped');
  process.exit(0);
});

// Start the service
startService();
