-- This script creates a company for the current user if they don't have one
-- Run this in the Supabase SQL Editor

-- Check if the current user already has a company
DO $$
DECLARE
    user_company_count INTEGER;
    current_user_id UUID;
    new_company_id UUID;
    user_email TEXT;
BEGIN
    -- Get the current user ID
    SELECT auth.uid() INTO current_user_id;
    
    -- Get the user's email
    SELECT email INTO user_email
    FROM auth.users
    WHERE id = current_user_id;
    
    RAISE NOTICE 'Current user ID: %, Email: %', current_user_id, user_email;
    
    -- Check if the user already has a company
    SELECT COUNT(*) INTO user_company_count
    FROM public.company_members
    WHERE user_id = current_user_id;
    
    RAISE NOTICE 'User company count: %', user_company_count;
    
    -- If the user doesn't have a company, create one
    IF user_company_count = 0 THEN
        -- Create a default company for the current user
        INSERT INTO public.companies (name, description)
        VALUES (CONCAT(user_email, '''s Company'), CONCAT('Default company for ', user_email))
        RETURNING id INTO new_company_id;
        
        RAISE NOTICE 'Created new company with ID: %', new_company_id;
        
        -- Add the current user as an admin to the company
        INSERT INTO public.company_members (company_id, user_id, role)
        VALUES (new_company_id, current_user_id, 'admin');
        
        RAISE NOTICE 'Added user % as admin to company %', current_user_id, new_company_id;
        
        -- Update existing monitors to be associated with the company
        UPDATE public.monitors
        SET company_id = new_company_id
        WHERE user_id = current_user_id AND (company_id IS NULL OR company_id::text = '');
        
        RAISE NOTICE 'Updated monitors for user % to company %', current_user_id, new_company_id;
    ELSE
        RAISE NOTICE 'User % already has % companies', current_user_id, user_company_count;
    END IF;
END $$;
