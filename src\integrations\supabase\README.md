# Supabase Integration

This directory contains the Supabase client configuration and database types for the application.

## Files

- `client.ts`: The centralized Supabase client that should be used throughout the application
- `types.ts`: TypeScript types for the Supabase database schema

## Usage

Import the Supabase client like this:

```typescript
import { supabase } from '@/integrations/supabase/client';
```

Import database types like this:

```typescript
import { Database } from '@/integrations/supabase/types';
```

## Environment Variables

The Supabase client uses the following environment variables:

- `VITE_SUPABASE_URL`: The URL of your Supabase project
- `VITE_SUPABASE_ANON_KEY`: The anonymous key for your Supabase project

These variables are defined in the following files:

- `.env.local`: Local development environment variables (not committed to the repository)
- `.env`: Fallback environment variables (committed to the repository, but should not contain sensitive information)

## Security

Never commit sensitive information like API keys to the repository. Use environment variables instead.

For local development, create a `.env.local` file based on the `.env.local.example` template.

For production, set the environment variables in your hosting platform's environment configuration.
