-- Create notifications table
CREATE TABLE IF NOT EXISTS public.notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    monitor_id UUID NOT NULL REFERENCES public.monitors(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    type TEXT NOT NULL, -- 'up', 'down', etc.
    read BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_monitor_id ON public.notifications(monitor_id);
CREATE INDEX IF NOT EXISTS idx_notifications_company_id ON public.notifications(company_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON public.notifications(read);

-- Enable Row Level Security
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

-- Create policy for viewing notifications
CREATE POLICY "Users can view their own notifications"
ON public.notifications
FOR SELECT
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = notifications.company_id
        AND company_members.user_id = auth.uid()
    )
);

-- Create policy for updating notifications (marking as read)
CREATE POLICY "Users can update their own notifications"
ON public.notifications
FOR UPDATE
USING (
    user_id = auth.uid() OR
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = notifications.company_id
        AND company_members.user_id = auth.uid()
    )
);

-- Grant necessary permissions
GRANT ALL ON public.notifications TO authenticated;
