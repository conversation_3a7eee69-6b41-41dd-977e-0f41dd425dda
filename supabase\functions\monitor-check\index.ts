import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import axios from 'https://esm.sh/axios@1.3.5'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Get the current user
    const {
      data: { user },
    } = await supabaseClient.auth.getUser()

    if (!user) {
      return new Response(JSON.stringify({ error: 'Unauthorized' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Get the request body
    const { monitorId } = await req.json()

    if (!monitorId) {
      return new Response(JSON.stringify({ error: 'Monitor ID is required' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      })
    }

    // Get the monitor details
    const { data: monitor, error: monitorError } = await supabaseClient
      .from('monitors')
      .select('*')
      .eq('id', monitorId)
      .single()

    if (monitorError || !monitor) {
      return new Response(JSON.stringify({ error: 'Monitor not found' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 404,
      })
    }

    // Check if the user has access to this monitor
    const { data: userCompanies } = await supabaseClient
      .from('company_members')
      .select('company_id')
      .eq('user_id', user.id)

    const userCompanyIds = userCompanies?.map((uc) => uc.company_id) || []

    const { data: monitorCompanies } = await supabaseClient
      .from('monitor_companies')
      .select('company_id')
      .eq('monitor_id', monitorId)

    const monitorCompanyIds = monitorCompanies?.map((mc) => mc.company_id) || []

    // Check if the user is a member of any company that has access to this monitor
    const hasAccess = userCompanyIds.some((id) => monitorCompanyIds.includes(id))

    // Check if the user is a superadmin
    const { data: userRoles } = await supabaseClient
      .from('user_roles')
      .select('*')
      .eq('user_id', user.id)
      .eq('role_name', 'superadmin')

    const isSuperadmin = userRoles && userRoles.length > 0

    if (!hasAccess && !isSuperadmin) {
      return new Response(JSON.stringify({ error: 'Access denied' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 403,
      })
    }

    // Perform the check
    const result = await performCheck(monitor)

    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

async function performCheck(monitor) {
  const startTime = Date.now()
  let status = false
  let statusCode = null
  let responseTime = 0
  let errorMessage = null

  try {
    // Set up request options
    const options = {
      timeout: monitor.timeout * 1000 || 30000,
      validateStatus: null, // Don't throw on any status code
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; VUMMonitorService/1.0; +https://github.com/RayZwankhuizen/VUM)',
      },
      maxRedirects: 5,
    }

    console.log(`Sending HTTP request to ${monitor.target}...`)
    console.log(`Request options:`, JSON.stringify(options))

    // Send the request
    const response = await axios.get(monitor.target, options)
    
    // Calculate response time
    responseTime = Date.now() - startTime
    
    // Get status code
    statusCode = response.status
    
    // Determine if the check was successful
    status = statusCode >= 200 && statusCode < 400
  } catch (error) {
    responseTime = Date.now() - startTime
    
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      statusCode = error.response.status
      errorMessage = `HTTP status ${statusCode}: ${error.message}`
      console.error(`HTTP response error: ${errorMessage}`)
    } else if (error.request) {
      // The request was made but no response was received
      errorMessage = error.message
      console.error(`Request was made but no response was received`)
      console.error(`Check failed for monitor ${monitor.name}: ${errorMessage}`)
    } else {
      // Something happened in setting up the request that triggered an Error
      errorMessage = error.message
      console.error(`Error setting up request: ${errorMessage}`)
    }
    
    status = false
  }

  // Prepare the result
  const result = {
    monitor_id: monitor.id,
    timestamp: new Date().toISOString(),
    status: status ? 'up' : 'down',
    response_time: responseTime,
    status_code: statusCode,
    error_message: errorMessage,
  }

  return result
}
