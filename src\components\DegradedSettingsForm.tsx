import React, { useState, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Save, AlertTriangle } from 'lucide-react';

interface DegradedThresholds {
  response_time: number;
  error_rate: number;
  status_codes: number[];
  consecutive_failures: number;
}

interface DegradedSettingsFormProps {
  initialSettings?: DegradedThresholds | null;
  onSettingsSaved?: () => void;
}

const DegradedSettingsForm: React.FC<DegradedSettingsFormProps> = ({
  initialSettings,
  onSettingsSaved
}) => {
  // Use refs to reduce re-renders
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // State management
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [settings, setSettings] = useState<DegradedThresholds>(initialSettings || {
    response_time: 1000,
    error_rate: 10,
    status_codes: [429, 503],
    consecutive_failures: 2
  });
  const [statusCodeInput, setStatusCodeInput] = useState('');

  // Load settings from the database
  const loadSettings = useCallback(async () => {
    try {
      // Get settings from the degraded_settings table
      const { data, error } = await supabase
        .from('degraded_settings')
        .select('*')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is 'no rows returned'
        throw error;
      }

      if (data) {
        setSettings({
          response_time: data.response_time,
          error_rate: data.error_rate,
          status_codes: data.status_codes || [],
          consecutive_failures: data.consecutive_failures
        });
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error loading settings:', error);
      }
      toast({
        title: 'Error',
        description: 'Failed to load settings. Please try again.',
        variant: 'destructive',
      });
    }
  }, []);

  // Save settings to the database
  const saveSettings = useCallback(async () => {
    try {
      setIsSaving(true);

      // Get the first record ID
      const { data: existingData, error: fetchError } = await supabase
        .from('degraded_settings')
        .select('id')
        .limit(1)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is 'no rows returned'
        throw fetchError;
      }

      let result;
      if (existingData?.id) {
        // Update existing record
        const { error } = await supabase
          .from('degraded_settings')
          .update({
            response_time: settings.response_time,
            error_rate: settings.error_rate,
            status_codes: settings.status_codes,
            consecutive_failures: settings.consecutive_failures,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);

        if (error) throw error;
        result = { success: true, id: existingData.id };
      } else {
        // Insert new record
        const { data, error } = await supabase
          .from('degraded_settings')
          .insert({
            response_time: settings.response_time,
            error_rate: settings.error_rate,
            status_codes: settings.status_codes,
            consecutive_failures: settings.consecutive_failures
          })
          .select()
          .single();

        if (error) throw error;
        result = { success: true, id: data.id };
      }

      // Success - no toast needed

      if (onSettingsSaved) {
        onSettingsSaved();
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error saving settings:', error);
      }
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [settings, onSettingsSaved]);

  // Handle form submission
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    saveSettings();
  }, [saveSettings]);

  // Handle reset settings
  const handleResetSettings = useCallback(async () => {
    try {
      setIsResetting(true);
      await loadSettings();
    } catch (error) {
      console.error('Error resetting settings:', error);
    } finally {
      setIsResetting(false);
    }
  }, [loadSettings]);

  // Debounced input change handler to reduce state updates
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Clear any existing debounce timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new debounce timer (300ms delay)
    debounceTimerRef.current = setTimeout(() => {
      // Convert to number for numeric fields
      if (name === 'response_time' || name === 'error_rate' || name === 'consecutive_failures') {
        setSettings(prev => ({
          ...prev,
          [name]: parseInt(value) || 0
        }));
      }

      // Clear the timer reference
      debounceTimerRef.current = null;
    }, 300);
  }, []);

  // Handle status code input
  const handleStatusCodeAdd = useCallback(() => {
    const code = parseInt(statusCodeInput);
    if (code && !isNaN(code) && code > 0) {
      if (!settings.status_codes.includes(code)) {
        setSettings(prev => ({
          ...prev,
          status_codes: [...prev.status_codes, code].sort((a, b) => a - b)
        }));
      }
      setStatusCodeInput('');
    }
  }, [statusCodeInput, settings.status_codes]);

  // Handle status code removal
  const handleStatusCodeRemove = useCallback((code: number) => {
    setSettings(prev => ({
      ...prev,
      status_codes: prev.status_codes.filter(c => c !== code)
    }));
  }, []);

  // Status codes list
  const statusCodesList = (
    <>
      {settings.status_codes.map(code => (
        <Badge
          key={code}
          variant="secondary"
          className="flex items-center gap-1 px-3 py-1"
        >
          {code}
          <button
            type="button"
            className="ml-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
            onClick={() => handleStatusCodeRemove(code)}
          >
            &times;
          </button>
        </Badge>
      ))}
      {settings.status_codes.length === 0 && (
        <p className="text-sm text-slate-500 italic">No status codes added</p>
      )}
    </>
  );

  // Handle status code input change
  const handleStatusCodeInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setStatusCodeInput(e.target.value);
  }, []);

  // Handle status code input key down
  const handleStatusCodeInputKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleStatusCodeAdd();
    }
  }, [handleStatusCodeAdd]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configure Global Degraded Service Thresholds</CardTitle>
        <CardDescription>
          These global settings determine when a service is considered degraded rather than operational or down.
          Individual monitors can override these settings with their own specific thresholds.
        </CardDescription>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          <Tabs defaultValue="thresholds">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
              <TabsTrigger value="status-codes">Status Codes</TabsTrigger>
            </TabsList>

            <TabsContent value="thresholds" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="response_time">
                  Response Time Threshold (ms)
                </Label>
                <Input
                  id="response_time"
                  name="response_time"
                  type="number"
                  min="0"
                  value={settings.response_time}
                  onChange={handleInputChange}
                />
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Services with response times above this threshold will be considered degraded.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="error_rate">
                  Error Rate Threshold (%)
                </Label>
                <Input
                  id="error_rate"
                  name="error_rate"
                  type="number"
                  min="0"
                  max="100"
                  value={settings.error_rate}
                  onChange={handleInputChange}
                />
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Services with error rates above this percentage will be considered degraded.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="consecutive_failures">
                  Consecutive Failures Threshold
                </Label>
                <Input
                  id="consecutive_failures"
                  name="consecutive_failures"
                  type="number"
                  min="1"
                  value={settings.consecutive_failures}
                  onChange={handleInputChange}
                />
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Number of consecutive partial failures before a service is considered degraded.
                </p>
              </div>
            </TabsContent>

            <TabsContent value="status-codes" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="status_codes">
                  HTTP Status Codes for Degraded Status
                </Label>
                <div className="flex space-x-2">
                  <Input
                    id="status_code_input"
                    placeholder="Add status code (e.g. 429)"
                    value={statusCodeInput}
                    onChange={handleStatusCodeInputChange}
                    onKeyDown={handleStatusCodeInputKeyDown}
                  />
                  <Button
                    type="button"
                    onClick={handleStatusCodeAdd}
                  >
                    Add
                  </Button>
                </div>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  HTTP status codes that indicate a degraded service rather than a complete outage.
                </p>

                <div className="flex flex-wrap gap-2 mt-2">
                  {statusCodesList}
                </div>
              </div>

              <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-md">
                <h3 className="font-medium flex items-center text-amber-800 dark:text-amber-300">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Common HTTP Status Codes for Degraded Services
                </h3>
                <ul className="mt-2 text-sm text-amber-700 dark:text-amber-400 space-y-1">
                  <li><strong>429</strong> - Too Many Requests (rate limiting)</li>
                  <li><strong>503</strong> - Service Unavailable (temporary overload)</li>
                  <li><strong>507</strong> - Insufficient Storage</li>
                  <li><strong>509</strong> - Bandwidth Limit Exceeded</li>
                </ul>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        <CardFooter className="flex justify-between border-t pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleResetSettings}
            disabled={isResetting || isSaving}
          >
            {isResetting ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Resetting...
              </>
            ) : (
              <>Reset</>
            )}
          </Button>
          <Button
            type="submit"
            disabled={isResetting || isSaving}
          >
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};

export default DegradedSettingsForm;
