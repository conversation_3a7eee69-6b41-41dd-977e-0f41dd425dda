import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Minus, Check, X, Info, AlertCircle, Loader2 } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { checkPorts, PortCheckResult } from '@/utils/portChecker';

// Common ports with their services
const commonPorts: { port: number; service: string }[] = [
  { port: 21, service: 'FTP' },
  { port: 22, service: 'SSH' },
  { port: 23, service: 'Telnet' },
  { port: 25, service: 'SMTP' },
  { port: 53, service: 'DNS' },
  { port: 80, service: 'HTTP' },
  { port: 110, service: 'POP3' },
  { port: 143, service: 'IMAP' },
  { port: 443, service: 'HTTPS' },
  { port: 465, service: 'SMTPS' },
  { port: 587, service: 'SMTP (Submission)' },
  { port: 993, service: 'IMAPS' },
  { port: 995, service: 'POP3S' },
  { port: 1433, service: 'MS SQL' },
  { port: 3306, service: 'MySQL' },
  { port: 3389, service: 'RDP' },
  { port: 5432, service: 'PostgreSQL' },
  { port: 8080, service: 'HTTP Alternate' },
  { port: 8443, service: 'HTTPS Alternate' },
];

interface PortSelectorProps {
  host: string;
  onHostChange: (host: string) => void;
  selectedPorts: number[];
  onPortsChange: (ports: number[]) => void;
  isEditing?: boolean;
}

const PortSelector: React.FC<PortSelectorProps> = ({
  host,
  onHostChange,
  selectedPorts,
  onPortsChange,
  isEditing = false,
}) => {
  const [customPort, setCustomPort] = useState<string>('');
  const [allSelected, setAllSelected] = useState<boolean>(false);
  const [isChecking, setIsChecking] = useState<boolean>(false);
  const [portResults, setPortResults] = useState<PortCheckResult[]>([]);
  const [showResults, setShowResults] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Update allSelected state when selectedPorts changes
  useEffect(() => {
    setAllSelected(selectedPorts.length === commonPorts.length);
  }, [selectedPorts]);

  // Check ports when they change (if we have a host)
  useEffect(() => {
    // Reset results when ports change
    setPortResults([]);
    setShowResults(false);
    setErrorMessage('');
  }, [selectedPorts]);

  const handleHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Remove any http:// or https:// prefix
    let newHost = e.target.value;
    newHost = newHost.replace(/^https?:\/\//, '');
    onHostChange(newHost);
  };

  const handlePortToggle = (port: number) => {
    if (selectedPorts.includes(port)) {
      onPortsChange(selectedPorts.filter((p) => p !== port));
    } else {
      onPortsChange([...selectedPorts, port].sort((a, b) => a - b));
    }
  };

  const handleAddCustomPort = () => {
    const port = parseInt(customPort);
    if (!isNaN(port) && port > 0 && port <= 65535 && !selectedPorts.includes(port)) {
      onPortsChange([...selectedPorts, port].sort((a, b) => a - b));
      setCustomPort('');
    }
  };

  const handleSelectAll = () => {
    if (allSelected) {
      // Deselect all
      onPortsChange([]);
    } else {
      // Select all
      onPortsChange(commonPorts.map((p) => p.port));
    }
    setAllSelected(!allSelected);
  };

  // Check if the selected ports are accessible
  const handleCheckPorts = async () => {
    if (!host || selectedPorts.length === 0) {
      setErrorMessage('Please enter a host and select at least one port');
      return;
    }

    setIsChecking(true);
    setShowResults(false);
    setErrorMessage('');

    try {
      // Set a timeout to prevent infinite loading
      const timeoutPromise = new Promise<null>((_, reject) => {
        setTimeout(() => reject(new Error('Port check timed out after 10 seconds')), 10000);
      });

      // Race the port check against the timeout
      const results = await Promise.race([
        checkPorts(host, selectedPorts),
        timeoutPromise
      ]) as PortCheckResult[];

      setPortResults(results);
      setShowResults(true);

      // Check if any ports are open
      const anyPortOpen = results.some(result => result.status);
      if (!anyPortOpen) {
        setErrorMessage('Host not found or none of the selected ports are accessible. The monitor will likely show as DOWN.');
      }
    } catch (error) {
      console.error('Error checking ports:', error);
      setErrorMessage(`Error checking host: ${error.message}`);

      // If we timed out, provide a fallback result
      if (error.message.includes('timed out')) {
        const fallbackResults = selectedPorts.map(port => ({
          port,
          status: false,
          error: 'Check timed out'
        }));
        setPortResults(fallbackResults);
        setShowResults(true);
      }
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <Label htmlFor="host">Host</Label>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Info className="h-4 w-4 text-slate-500 cursor-help" />
              </TooltipTrigger>
              <TooltipContent className="max-w-sm">
                <p>For port monitoring, we'll check if the host exists and monitor the specified ports.</p>
                <p className="mt-1">Due to browser security restrictions, we can only verify that the host exists before creating the monitor.</p>
                <p className="mt-1">The actual port accessibility check will happen when the monitor runs on the server.</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <Input
          id="host"
          placeholder="example.com"
          value={host}
          onChange={handleHostChange}
        />
        <p className="text-sm text-slate-500 dark:text-slate-400">
          Enter the hostname without http:// or https://
        </p>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <Label>Ports to Monitor</Label>
          <Button
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            className="flex items-center gap-1"
          >
            {allSelected ? <X className="h-4 w-4" /> : <Check className="h-4 w-4" />}
            {allSelected ? 'Deselect All' : 'Select All'}
          </Button>
        </div>

        <Card>
          <CardContent className="p-4">
            <ScrollArea className="h-60 pr-4">
              <div className="space-y-2">
                {commonPorts.map((item) => (
                  <div key={item.port} className="flex items-center space-x-2">
                    <Checkbox
                      id={`port-${item.port}`}
                      checked={selectedPorts.includes(item.port)}
                      onCheckedChange={() => handlePortToggle(item.port)}
                    />
                    <Label
                      htmlFor={`port-${item.port}`}
                      className="flex-1 cursor-pointer"
                    >
                      {item.port} - {item.service}
                    </Label>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-2">
        <Label htmlFor="customPort">Add Custom Port</Label>
        <div className="flex space-x-2">
          <Input
            id="customPort"
            placeholder="Port number (1-65535)"
            value={customPort}
            onChange={(e) => setCustomPort(e.target.value)}
            type="number"
            min="1"
            max="65535"
          />
          <Button
            type="button"
            variant="outline"
            onClick={handleAddCustomPort}
            disabled={!customPort || isNaN(parseInt(customPort))}
          >
            <Plus className="h-4 w-4" />
            Add
          </Button>
        </div>
      </div>

      {selectedPorts.length > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label>Selected Ports</Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleCheckPorts}
              disabled={isChecking || !host || selectedPorts.length === 0}
            >
              {isChecking ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Checking...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Check Ports
                </>
              )}
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedPorts.map((port) => {
              // Find port result if available
              const result = portResults.find(r => r.port === port);
              let statusColor = '';

              if (result) {
                statusColor = result.status ? 'bg-green-100 dark:bg-green-900/20' : 'bg-red-100 dark:bg-red-900/20';
              }

              return (
                <div
                  key={port}
                  className={`flex items-center ${statusColor || 'bg-slate-100 dark:bg-slate-800'} rounded-md px-2 py-1`}
                >
                  <span className="mr-1">{port}</span>
                  {result && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <span className="mx-1">
                            {result.status ? (
                              <Check className="h-3 w-3 text-green-500" />
                            ) : (
                              <X className="h-3 w-3 text-red-500" />
                            )}
                          </span>
                        </TooltipTrigger>
                        <TooltipContent>
                          {result.status
                            ? 'Host found in DNS. Port will be checked when monitor runs.'
                            : `Host not found in DNS: ${result.error || 'Not accessible'}`
                          }
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                  <button
                    type="button"
                    onClick={() => handlePortToggle(port)}
                    className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 ml-1"
                  >
                    <Minus className="h-3 w-3" />
                  </button>
                </div>
              );
            })}
          </div>

          {errorMessage && (
            <Alert variant="destructive" className="mt-2">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {showResults && portResults.some(r => r.status) && (
            <Alert variant="default" className="mt-2 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
              <Check className="h-4 w-4 text-green-500" />
              <AlertTitle>Host Found</AlertTitle>
              <AlertDescription>
                The host {host} was found in DNS lookups.
                <p className="mt-1 text-sm">
                  Due to browser security restrictions, we can only verify that the host exists before creating the monitor.
                  The actual port accessibility check will happen when the monitor runs on the server.
                </p>
                <p className="mt-1 text-sm">
                  The monitor will show as UP if any of the selected ports are accessible when checked by the server.
                </p>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
};

export default PortSelector;
