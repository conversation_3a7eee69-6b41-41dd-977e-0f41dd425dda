import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertCircle, ArrowRight } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { useCompany } from '@/contexts/CompanyContext';

interface MonitorLimitWarningProps {
  onClose?: () => void;
}

export function MonitorLimitWarning({ onClose }: MonitorLimitWarningProps) {
  const navigate = useNavigate();
  const { currentCompany } = useCompany();
  const { useCompanySubscription, useCanAddMonitor, useCompanyMonitorLimit } = useSubscription();
  const [showWarning, setShowWarning] = useState(false);

  const { data: subscription } = useCompanySubscription();
  const { data: canAdd, isLoading } = useCanAddMonitor();
  const { data: monitorLimit } = useCompanyMonitorLimit();

  useEffect(() => {
    if (!isLoading && canAdd === false) {
      setShowWarning(true);
    } else {
      setShowWarning(false);
    }
  }, [canAdd, isLoading]);

  if (!showWarning || !subscription) {
    return null;
  }

  return (
    <Alert variant="destructive" className="mb-6">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Monitor limit reached</AlertTitle>
      <AlertDescription className="space-y-4">
        <p>
          Your company has reached the maximum number of monitors
          ({monitorLimit ? `${monitorLimit.currentCount}/${monitorLimit.maxMonitors}` : subscription?.max_monitors})
          allowed on the {subscription?.tier_name} plan. To add more monitors, you need to upgrade
          your subscription or delete some existing monitors.
        </p>
        {monitorLimit && (
          <div className="text-sm bg-red-50 dark:bg-red-950/50 p-3 rounded-md">
            <strong>Current usage:</strong> {monitorLimit.currentCount} monitors<br />
            <strong>Tier limit:</strong> {monitorLimit.maxMonitors} monitors ({subscription?.tier_name} tier)<br />
            <strong>Available:</strong> {monitorLimit.monitorsAvailable} monitors
          </div>
        )}
        <div className="flex space-x-4">
          <Button variant="outline" size="sm" onClick={() => navigate('/subscription')}>
            View Subscription Options
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              Dismiss
            </Button>
          )}
        </div>
      </AlertDescription>
    </Alert>
  );
}
