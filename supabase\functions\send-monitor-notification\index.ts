// Supabase Edge Function to send email notifications for monitor status changes
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailPayload {
  to: string[]
  subject: string
  html: string
}

interface StatusChangePayload {
  monitor_id: string
  status: string
  company_id: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Parse the request body
    const payload = await req.json() as StatusChangePayload
    const { monitor_id, status, company_id } = payload

    // Validate required fields
    if (!monitor_id || !status || !company_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get monitor details
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('name, target, type')
      .eq('id', monitor_id)
      .single()

    if (monitorError) {
      return new Response(
        JSON.stringify({ error: `Failed to get monitor details: ${monitorError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get company details
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select('name')
      .eq('id', company_id)
      .single()

    if (companyError) {
      return new Response(
        JSON.stringify({ error: `Failed to get company details: ${companyError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get company admin emails using our SQL function
    const { data: adminEmails, error: adminEmailsError } = await supabase
      .rpc('get_company_admin_emails', { company_id })

    if (adminEmailsError) {
      return new Response(
        JSON.stringify({ error: `Failed to get admin emails: ${adminEmailsError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    if (!adminEmails || adminEmails.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No admin emails found for this company' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create email content
    const statusText = status === 'up' ? 'UP' : status === 'down' ? 'DOWN' : 'DEGRADED'
    const statusColor = status === 'up' ? '#4CAF50' : status === 'down' ? '#F44336' : '#FF9800'
    
    const subject = `[${company.name}] Monitor ${monitor.name} is ${statusText}`
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Monitor Status Change</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: ${statusColor}; color: white;">
          <h3 style="margin-top: 0;">Monitor is now ${statusText}</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Monitor:</strong> ${monitor.name}</p>
          <p><strong>Type:</strong> ${monitor.type}</p>
          <p><strong>Target:</strong> ${monitor.target}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is an automated message from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `

    // Send email using Resend API
    const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
    
    if (!RESEND_API_KEY) {
      return new Response(
        JSON.stringify({ error: 'RESEND_API_KEY is not set' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Send email to all admins
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${RESEND_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: 'Vurbis Uptime Monitor <<EMAIL>>',
        to: adminEmails,
        subject,
        html,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      return new Response(
        JSON.stringify({ error: `Failed to send email: ${errorText}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    const result = await response.json()
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        message: `Status change email sent to ${adminEmails.length} admins`,
        result
      }),
      { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
