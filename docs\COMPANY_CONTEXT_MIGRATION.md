# Company Context Migration Guide

This guide explains how to migrate from the old monolithic CompanyContext to the new React Query-based architecture.

## Overview of Changes

The CompanyContext has been refactored to:
- Use React Query for server state management
- Separate concerns into service layer and custom hooks
- Provide better error handling and loading states
- Enable optimistic updates and proper caching
- Reduce the context to only essential global state

## Architecture Changes

### Before (Old Pattern)
```tsx
// Old: Everything in one massive context
const { 
  companies, 
  companyMembers, 
  isLoading, 
  error,
  fetchCompanies,
  createCompany,
  updateCompany,
  deleteCompany,
  // ... many more methods
} = useCompany();

// Manual loading states and error handling
if (isLoading) return <Spinner />;
if (error) return <Error message={error.message} />;

// Direct method calls
const handleCreate = async () => {
  const result = await createCompany(data);
  if (result) {
    // Manual success handling
  }
};
```

### After (New Pattern)
```tsx
// New: Simplified context for global state only
const { currentCompany, setCurrentCompany, isAdmin } = useCompany();

// Separate hooks for data operations
const { useGetCompaniesQuery, useCreateCompanyMutation } = useCompanies();

// React Query provides loading/error states automatically
const { data: companies, isLoading, error } = useGetCompaniesQuery();
const createMutation = useCreateCompanyMutation();

// Automatic error handling via toast notifications
const handleCreate = () => {
  createMutation.mutate(data); // Automatic success/error handling
};
```

## Migration Steps

### 1. Update Imports

**Before:**
```tsx
import { useCompany } from '@/contexts/CompanyContext';

const { 
  companies, 
  fetchCompanies, 
  createCompany 
} = useCompany();
```

**After:**
```tsx
import { useCompany } from '@/contexts/CompanyContext';
import { useCompanies } from '@/hooks/use-companies';

const { currentCompany, setCurrentCompany, isAdmin } = useCompany();
const { 
  useGetCompaniesQuery, 
  useCreateCompanyMutation 
} = useCompanies();

const { data: companies } = useGetCompaniesQuery();
const createMutation = useCreateCompanyMutation();
```

### 2. Replace Data Fetching

**Before:**
```tsx
// Manual fetching with loading states
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState(null);

useEffect(() => {
  const loadData = async () => {
    setIsLoading(true);
    try {
      await fetchCompanies();
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };
  loadData();
}, []);
```

**After:**
```tsx
// Automatic loading states and error handling
const { 
  data: companies = [], 
  isLoading, 
  error 
} = useGetCompaniesQuery();

// React Query handles caching, refetching, and error states automatically
```

### 3. Replace Mutations

**Before:**
```tsx
const handleCreateCompany = async (data) => {
  try {
    const result = await createCompany(data);
    if (result) {
      toast({ title: 'Success', description: 'Company created' });
      // Manual state updates
    }
  } catch (error) {
    toast({ title: 'Error', description: error.message });
  }
};
```

**After:**
```tsx
const createMutation = useCreateCompanyMutation();

const handleCreateCompany = (data) => {
  createMutation.mutate(data);
  // Automatic success/error handling with toasts
  // Automatic cache invalidation and UI updates
};

// Access loading state
if (createMutation.isPending) {
  // Show loading spinner
}
```

### 4. Update Company Members

**Before:**
```tsx
const { companyMembers, fetchCompanyMembers } = useCompany();

useEffect(() => {
  if (currentCompany) {
    fetchCompanyMembers(currentCompany.id);
  }
}, [currentCompany]);
```

**After:**
```tsx
const { useGetCompanyMembersQuery } = useCompanies();
const { data: companyMembers = [] } = useGetCompanyMembersQuery(currentCompany?.id || '');

// Automatic refetching when currentCompany changes
// Built-in loading and error states
```

## Key Benefits

### 1. Better Performance
- Automatic caching prevents duplicate API calls
- Background refetching keeps data fresh
- Optimistic updates for better UX

### 2. Improved Error Handling
- Consistent error handling across all operations
- Automatic retry mechanisms
- Better error boundaries

### 3. Simplified Code
- No manual loading state management
- No manual error handling
- Automatic cache invalidation

### 4. Better Developer Experience
- TypeScript support throughout
- Consistent patterns across the app
- Easier testing with React Query

## Common Patterns

### Loading States
```tsx
// Old
if (isLoading) return <Spinner />;

// New
const { data, isLoading } = useGetCompaniesQuery();
if (isLoading) return <Spinner />;
```

### Error Handling
```tsx
// Old
if (error) return <ErrorMessage error={error} />;

// New
const { data, error } = useGetCompaniesQuery();
if (error) return <ErrorMessage error={error} />;
```

### Mutations with Loading
```tsx
// Old
const [isCreating, setIsCreating] = useState(false);
const handleCreate = async () => {
  setIsCreating(true);
  try {
    await createCompany(data);
  } finally {
    setIsCreating(false);
  }
};

// New
const createMutation = useCreateCompanyMutation();
const handleCreate = () => createMutation.mutate(data);
// Use createMutation.isPending for loading state
```

## Breaking Changes

1. **Removed Methods**: All CRUD methods are no longer available directly from `useCompany()`
2. **Loading States**: `isLoading` is no longer global - each query has its own loading state
3. **Error Handling**: Global error state removed - each query handles its own errors
4. **Manual Refetching**: `fetchCompanies()` and similar methods no longer exist

## Files Changed

- `src/contexts/CompanyContext.tsx` - Simplified to only manage global state
- `src/services/company-service.ts` - New service layer for data operations
- `src/hooks/use-companies.ts` - New React Query hooks for company operations
- `src/components/examples/CompanyManagementExample.tsx` - Example usage

## Next Steps

1. Update all components that use the old CompanyContext pattern
2. Test the new implementation thoroughly
3. Remove any manual loading/error state management
4. Take advantage of React Query's advanced features like optimistic updates
