import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, AlertTriangle } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { SubscriptionTier } from '@/types/subscription';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface CompanySubscriptionTierSelectorProps {
  companyId: string;
  companyName?: string;
  isSuperadmin: boolean;
}

export default function CompanySubscriptionTierSelector({
  companyId,
  companyName,
  isSuperadmin
}: CompanySubscriptionTierSelectorProps) {
  const {
    useSubscriptionTiers,
    useCompanySubscription,
    useUpdateCompanySubscriptionTier,
    useUpdateCompanySubscriptionOverrides
  } = useSubscription();

  const { data: tiers, isLoading: tiersLoading } = useSubscriptionTiers();
  const { data: subscription, isLoading: subscriptionLoading, refetch } = useCompanySubscription(companyId);
  const updateTierMutation = useUpdateCompanySubscriptionTier();
  const updateOverridesMutation = useUpdateCompanySubscriptionOverrides();

  const [selectedTierId, setSelectedTierId] = useState<string>('');
  const [customMaxMonitors, setCustomMaxMonitors] = useState<string>('');
  const [customRetentionDays, setCustomRetentionDays] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Alert dialog states
  const [showMonitorLimitAlert, setShowMonitorLimitAlert] = useState(false);
  const [showCustomValueAlert, setShowCustomValueAlert] = useState(false);
  const [pendingTierChange, setPendingTierChange] = useState<{
    tierId: string;
    tierName: string;
    maxMonitors: number;
    retentionDays: number;
  } | null>(null);

  // Original tier ID for comparison
  const originalTierIdRef = useRef<string>('');

  useEffect(() => {
    if (subscription && tiers) {
      // Find the tier ID based on the tier name
      const tierObj = tiers.find(t => t.name === subscription.tier_name);
      const tierId = tierObj?.id || '';

      setSelectedTierId(tierId);
      originalTierIdRef.current = tierId;

      // Always set the current values, whether they're custom or from the tier
      setCustomMaxMonitors(subscription.max_monitors ? subscription.max_monitors.toString() : '');
      setCustomRetentionDays(subscription.history_retention_days ? subscription.history_retention_days.toString() : '');
    }
  }, [subscription, tiers]);

  if (!isSuperadmin) {
    return null;
  }

  const isLoading = tiersLoading || subscriptionLoading;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleTierChange = (newTierId: string) => {
    if (!subscription || !tiers) return;

    const newTier = tiers.find(t => t.id === newTierId);
    if (!newTier) return;

    const currentTier = tiers.find(t => t.id === originalTierIdRef.current);

    // Store the pending tier change
    setPendingTierChange({
      tierId: newTierId,
      tierName: newTier.name,
      maxMonitors: newTier.max_monitors,
      retentionDays: newTier.history_retention_days
    });

    // Check if the new tier has a lower monitor limit than current usage
    if (subscription.current_monitors > newTier.max_monitors) {
      setShowMonitorLimitAlert(true);
      return; // Don't set the new tier ID yet
    }

    // Check if there are custom values that would be affected
    const hasCustomMaxMonitors = subscription.is_custom &&
      subscription.max_monitors !== currentTier?.max_monitors;
    const hasCustomRetentionDays = subscription.is_custom &&
      subscription.history_retention_days !== currentTier?.history_retention_days;

    if (hasCustomMaxMonitors || hasCustomRetentionDays) {
      setShowCustomValueAlert(true);
      return; // Don't set the new tier ID yet
    }

    // If no issues, set the new tier ID and update the custom values to match the new tier
    setSelectedTierId(newTierId);
    setCustomMaxMonitors(''); // Reset to use tier default
    setCustomRetentionDays(''); // Reset to use tier default
  };

  const handleSave = async () => {
    if (!companyId) return;

    setIsSaving(true);
    try {
      // Update tier if changed
      if (selectedTierId) {
        await updateTierMutation.mutateAsync({ companyId, tierIdOrName: selectedTierId });
      }

      // Update overrides if any
      await updateOverridesMutation.mutateAsync({
        companyId,
        overrides: {
          custom_max_monitors: customMaxMonitors ? parseInt(customMaxMonitors) : null,
          custom_history_retention_days: customRetentionDays ? parseInt(customRetentionDays) : null
        }
      });

      toast({
        title: 'Subscription Updated',
        description: `Subscription settings for ${companyName || 'this company'} have been updated.`,
      });

      setIsEditing(false);
      refetch();
    } catch (error) {
      console.error('Error saving subscription settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save subscription settings.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    // Reset to original values
    if (subscription && tiers) {
      const tierObj = tiers.find(t => t.name === subscription.tier_name);
      setSelectedTierId(tierObj?.id || '');

      // Always set the current values, whether they're custom or from the tier
      setCustomMaxMonitors(subscription.max_monitors ? subscription.max_monitors.toString() : '');
      setCustomRetentionDays(subscription.history_retention_days ? subscription.history_retention_days.toString() : '');
    }

    setIsEditing(false);
  };

  const handleResetOverrides = () => {
    setCustomMaxMonitors('');
    setCustomRetentionDays('');
  };

  const handleConfirmTierChange = () => {
    if (!pendingTierChange) return;

    // Apply the tier change
    setSelectedTierId(pendingTierChange.tierId);

    // Reset custom values to use tier defaults
    setCustomMaxMonitors('');
    setCustomRetentionDays('');

    // Close the alert dialog
    setShowCustomValueAlert(false);
    setPendingTierChange(null);
  };

  const handleCancelTierChange = () => {
    // Reset to the original tier
    setSelectedTierId(originalTierIdRef.current);

    // Close the alert dialogs
    setShowCustomValueAlert(false);
    setShowMonitorLimitAlert(false);
    setPendingTierChange(null);
  };

  return (
    <>
      {/* Alert for monitor limit exceeded */}
      <AlertDialog open={showMonitorLimitAlert} onOpenChange={setShowMonitorLimitAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" /> Monitor Limit Exceeded
            </AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4">
                This company currently has <strong>{subscription?.current_monitors}</strong> monitors,
                but the <strong>{pendingTierChange?.tierName}</strong> tier only allows
                <strong> {pendingTierChange?.maxMonitors}</strong> monitors.
              </p>
              <p>
                You must delete some monitors before downgrading to this tier.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={handleCancelTierChange}>
              Understood
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Alert for custom values that would be affected */}
      <AlertDialog open={showCustomValueAlert} onOpenChange={setShowCustomValueAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-amber-500" /> Custom Settings Will Be Reset
            </AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4">
                This company has custom subscription settings that will be reset to the
                defaults for the <strong>{pendingTierChange?.tierName}</strong> tier:
              </p>
              <ul className="list-disc list-inside mb-4 space-y-1">
                <li>Max Monitors: <strong>{pendingTierChange?.maxMonitors}</strong></li>
                <li>History Retention: <strong>{pendingTierChange?.retentionDays} days</strong></li>
              </ul>
              <p>
                Do you want to continue with this change?
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelTierChange}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmTierChange}>
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Subscription Tier</CardTitle>
            <CardDescription>Manage subscription settings for this company</CardDescription>
          </div>
          {subscription?.is_custom && !isEditing && (
            <Badge variant="outline">Custom Settings</Badge>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
        ) : (
          <>
            {isEditing ? (
              <div className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="subscription-tier">Subscription Tier</Label>
                  <Select
                    value={selectedTierId}
                    onValueChange={handleTierChange}
                    disabled={isSaving}
                  >
                    <SelectTrigger id="subscription-tier">
                      <SelectValue placeholder="Select a tier" />
                    </SelectTrigger>
                    <SelectContent>
                      {tiers?.map((tier) => (
                        <SelectItem key={tier.id} value={tier.id}>
                          {tier.name} ({tier.max_monitors} monitors, {tier.history_retention_days} days)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground mt-1">
                    Current tier: {subscription?.tier_name} ({subscription?.max_monitors} monitors, {subscription?.history_retention_days} days)
                  </p>
                </div>

                <Separator />

                <div>
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-medium">Custom Overrides</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetOverrides}
                      disabled={isSaving || (!customMaxMonitors && !customRetentionDays)}
                    >
                      Reset to Tier Defaults
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="max-monitors">Max Monitors</Label>
                      <Input
                        id="max-monitors"
                        type="number"
                        min="1"
                        value={customMaxMonitors}
                        onChange={(e) => setCustomMaxMonitors(e.target.value)}
                        placeholder={`Tier default: ${tiers?.find(t => t.id === selectedTierId)?.max_monitors || 'N/A'}`}
                        disabled={isSaving}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {subscription?.is_custom && subscription?.max_monitors !== tiers?.find(t => t.id === selectedTierId)?.max_monitors
                          ? 'Using custom value'
                          : 'Using tier default'}
                      </p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="retention-days">History Retention (days)</Label>
                      <Input
                        id="retention-days"
                        type="number"
                        min="1"
                        value={customRetentionDays}
                        onChange={(e) => setCustomRetentionDays(e.target.value)}
                        placeholder={`Tier default: ${tiers?.find(t => t.id === selectedTierId)?.history_retention_days || 'N/A'}`}
                        disabled={isSaving}
                      />
                      <p className="text-xs text-muted-foreground mt-1">
                        {subscription?.is_custom && subscription?.history_retention_days !== tiers?.find(t => t.id === selectedTierId)?.history_retention_days
                          ? 'Using custom value'
                          : 'Using tier default'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium mb-1">Current Tier</h3>
                    <p className="text-2xl font-bold">{subscription?.tier_name || 'None'}</p>
                    <p className="text-sm text-muted-foreground mt-1">{subscription?.tier_description || ''}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-1">Monitor Usage</h3>
                    <p className="text-2xl font-bold">
                      {subscription?.current_monitors || 0} / {subscription?.max_monitors || 0}
                    </p>
                    <p className="text-sm text-muted-foreground mt-1">
                      {subscription?.current_monitors >= subscription?.max_monitors
                        ? 'Limit reached'
                        : `${subscription?.max_monitors - subscription?.current_monitors} monitors available`}
                    </p>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-medium mb-2">Retention Settings</h3>
                  <p className="text-lg">
                    <span className="font-medium">History Retention:</span> {subscription?.history_retention_days || 0} days
                  </p>
                  {subscription?.is_custom && (
                    <p className="text-sm text-muted-foreground mt-1">
                      This company has custom retention settings
                    </p>
                  )}
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          size="sm"
          onClick={() => refetch()}
          disabled={isLoading || isEditing}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>

        <div className="space-x-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
                Cancel
              </Button>
              <Button onClick={handleSave} disabled={isSaving}>
                {isSaving ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </>
          ) : (
            <Button onClick={handleEdit} disabled={isLoading}>
              Edit Subscription
            </Button>
          )}
        </div>
      </CardFooter>
    </Card>
    </>
  );
}
