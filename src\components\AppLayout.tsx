import React from 'react';
import AppSidebar from './AppSidebar';
import { useSidebar } from '@/contexts/SidebarContext';
import { ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from './ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
// Keyboard shortcut functionality now handled in the header

interface AppLayoutProps {
  children: React.ReactNode;
  header: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children, header }) => {
  const { sidebarVisible, toggleSidebar } = useSidebar();

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex flex-col">
      {/* Unified header at the top */}
      {header}

      {/* Main content area with sidebar */}
      <div className="flex flex-1 overflow-hidden relative">
        {/* Sidebar without its own header */}
        <div
          className={`bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 overflow-y-auto transition-all duration-300 ${sidebarVisible ? 'w-64' : 'w-0'}`}
        >
          <div className={`py-4 ${sidebarVisible ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}>
            <AppSidebar hideHeader={true} />
          </div>
        </div>

        {/* Toggle button removed - functionality now in the icon at the top */}

        {/* Main content - expands when sidebar is hidden */}
        <div className="flex-1 overflow-auto transition-all duration-300">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
