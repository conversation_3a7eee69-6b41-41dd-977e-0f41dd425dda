-- This script manually creates a company and associates it with your user
-- Run this in the Supabase SQL Editor

-- Step 1: Create a company
INSERT INTO public.companies (name, description)
VALUES ('My Company', 'Default company for my monitors')
RETURNING id;

-- Step 2: Get the ID of the company we just created
-- Copy the ID from the result of the previous query and use it in the next query

-- Step 3: Add your user as an admin to the company
-- Replace 'YOUR_COMPANY_ID' with the ID from Step 1
INSERT INTO public.company_members (company_id, user_id, role)
VALUES ('YOUR_COMPANY_ID', '3a19a1f7-3f61-48a8-949c-caa3fba04924', 'admin');

-- Step 4: Update your existing monitors to be associated with the company
-- Replace 'YOUR_COMPANY_ID' with the ID from Step 1
UPDATE public.monitors
SET company_id = 'YOUR_COMPANY_ID'
WHERE user_id = '3a19a1f7-3f61-48a8-949c-caa3fba04924' AND (company_id IS NULL OR company_id::text = '');
