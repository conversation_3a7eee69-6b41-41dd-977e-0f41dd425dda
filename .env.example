# Supabase Configuration
# Replace these with your actual Supabase URL and keys
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
# The frontend uses the anon key with <PERSON><PERSON>
# The service key should only be used by the monitor service
SUPABASE_PROJECT_REF=your-project-id

# Monitor Service Configuration
CHECK_INTERVAL=60000  # Check every 60 seconds (60000 ms)
LOG_LEVEL=info        # Log level (debug, info, warn, error)
MONITOR_TABLE=monitors
HISTORY_TABLE=monitor_history
NOTIFICATION_TABLE=notifications
MAX_CONCURRENT_CHECKS=10  # Maximum number of concurrent checks
LOG_FILE_PATH=C:\VUM\monitor-checker-service.log

# Database Connection (for notification listener)
# These credentials are needed for the PostgreSQL notification listener
DB_HOST=db.your-project-id.supabase.co
DB_PORT=5432
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=your_database_password_here

# Monitor Service User (should be a superadmin)
# This user has been created in Supabase and made a superadmin
MONITOR_SERVICE_EMAIL=<EMAIL>
MONITOR_SERVICE_PASSWORD=your_secure_password_here

# Email Configuration
# Get your API key from https://resend.com
RESEND_API_KEY=your_resend_api_key_here
RESEND_API_URL=https://api.resend.com/emails
RESEND_FROM_EMAIL=Your App <<EMAIL>>
ENABLE_EMAIL_ALERTS=false  # Set to true to enable email alerts

# Legacy Email Configuration (SMTP)
# Only used if RESEND_API_KEY is not set
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password
SMTP_FROM=<EMAIL>
