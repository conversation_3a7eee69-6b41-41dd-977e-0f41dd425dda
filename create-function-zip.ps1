# PowerShell script to create a ZIP file of the monitor-checker function

Write-Host "Creating ZIP file of the monitor-checker function..." -ForegroundColor Cyan

# Navigate to the functions directory
Push-Location -Path "supabase/functions"

try {
    # Create a ZIP file of the monitor-checker directory
    Compress-Archive -Path "monitor-checker" -DestinationPath "monitor-checker.zip" -Force
    
    # Check if ZIP creation was successful
    if (Test-Path "monitor-checker.zip") {
        Write-Host "ZIP file created successfully at: $(Resolve-Path "monitor-checker.zip")" -ForegroundColor Green
    } else {
        Write-Host "Failed to create ZIP file" -ForegroundColor Red
    }
} catch {
    Write-Host "An error occurred while creating ZIP file: $_" -ForegroundColor Red
} finally {
    # Return to the original directory
    Pop-Location
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
