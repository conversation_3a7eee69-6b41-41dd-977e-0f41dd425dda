// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.land/manual/examples/supabase

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface EmailPayload {
  to: string
  subject: string
  html: string
}

interface StatusChangePayload {
  monitor_id: string
  status: 'up' | 'down' | 'degraded'
  company_id: string
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header from the request
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'No authorization header provided' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create a Supabase client with the auth header
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      { global: { headers: { Authorization: authHeader } } }
    )

    // Parse the request body
    const { monitor_id, status, company_id } = await req.json() as StatusChangePayload

    // Validate required fields
    if (!monitor_id || !status || !company_id) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: monitor_id, status, company_id' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get monitor details
    const { data: monitor, error: monitorError } = await supabaseClient
      .from('monitors')
      .select('name, target, type')
      .eq('id', monitor_id)
      .single()

    if (monitorError) {
      return new Response(
        JSON.stringify({ error: `Failed to get monitor details: ${monitorError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get company details
    const { data: company, error: companyError } = await supabaseClient
      .from('companies')
      .select('name')
      .eq('id', company_id)
      .single()

    if (companyError) {
      return new Response(
        JSON.stringify({ error: `Failed to get company details: ${companyError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Get company admin emails
    const { data: admins, error: adminsError } = await supabaseClient
      .from('company_members')
      .select(`
        user_id,
        users:user_id (
          email
        )
      `)
      .eq('company_id', company_id)
      .eq('role_type', 'admin')

    if (adminsError) {
      return new Response(
        JSON.stringify({ error: `Failed to get company admins: ${adminsError.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Extract admin emails
    const adminEmails = admins.map(admin => admin.users.email)

    if (adminEmails.length === 0) {
      return new Response(
        JSON.stringify({ message: 'No admin emails found for this company' }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    // Create email content
    const statusText = status === 'up' ? 'UP' : status === 'down' ? 'DOWN' : 'DEGRADED'
    const statusColor = status === 'up' ? '#4CAF50' : status === 'down' ? '#F44336' : '#FF9800'
    
    const subject = `[${company.name}] Monitor ${monitor.name} is ${statusText}`
    
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Monitor Status Change</h2>
        <div style="padding: 20px; border-radius: 5px; margin-bottom: 20px; background-color: ${statusColor}; color: white;">
          <h3 style="margin-top: 0;">Monitor is now ${statusText}</h3>
        </div>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
          <p><strong>Company:</strong> ${company.name}</p>
          <p><strong>Monitor:</strong> ${monitor.name}</p>
          <p><strong>Type:</strong> ${monitor.type}</p>
          <p><strong>Target:</strong> ${monitor.target}</p>
          <p><strong>Status:</strong> <span style="color: ${statusColor}; font-weight: bold;">${statusText}</span></p>
          <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
        </div>
        <div style="font-size: 12px; color: #666; margin-top: 30px;">
          <p>This is an automated message from Vurbis Uptime Monitor.</p>
        </div>
      </div>
    `

    // Send email to each admin
    const emailPromises = adminEmails.map(async (email) => {
      // Use Resend API to send email
      const emailPayload: EmailPayload = {
        to: email,
        subject,
        html,
      }

      // Use your preferred email service here
      // This example uses Resend, but you can use any email service
      const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
      
      if (!RESEND_API_KEY) {
        throw new Error('RESEND_API_KEY is not set')
      }

      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${RESEND_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: 'Vurbis Uptime Monitor <<EMAIL>>',
          to: email,
          subject,
          html,
        }),
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Failed to send email: ${errorText}`)
      }

      return response.json()
    })

    try {
      await Promise.all(emailPromises)
      
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: `Status change emails sent to ${adminEmails.length} admins` 
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (error) {
      return new Response(
        JSON.stringify({ error: `Failed to send emails: ${error.message}` }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
