import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { UserWithRoles } from '@/hooks/use-users';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface AssignCompanyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedUser: UserWithRoles | null;
  selectedCompanyId: string;
  setSelectedCompanyId: (id: string) => void;
  selectedCompanyRole: string;
  setSelectedCompanyRole: (role: string) => void;
  handleAssignUserToCompany: () => Promise<void>;
  getAvailableCompanies: (user: UserWithRoles) => any[];
  isPending: boolean;
}

const AssignCompanyDialog: React.FC<AssignCompanyDialogProps> = ({
  open,
  onOpenChange,
  selectedUser,
  selectedCompanyId,
  setSelectedCompanyId,
  selectedCompanyRole,
  setSelectedCompanyRole,
  handleAssignUserToCompany,
  getAvailableCompanies,
  isPending,
}) => {
  const availableCompanies = selectedUser ? getAvailableCompanies(selectedUser) : [];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Assign to Company</DialogTitle>
          <DialogDescription>
            Add this user to a company and set their role
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {selectedUser && availableCompanies.length === 0 ? (
            <p className="text-center text-slate-500">
              This user is already a member of all available companies.
            </p>
          ) : (
            <>
              <div className="space-y-2">
                <Label htmlFor="companySelect">Company</Label>
                <Select
                  value={selectedCompanyId}
                  onValueChange={setSelectedCompanyId}
                >
                  <SelectTrigger id="companySelect">
                    <SelectValue placeholder="Select a company" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCompanies.map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        {company.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="roleSelect">Role</Label>
                <Select
                  value={selectedCompanyRole}
                  onValueChange={setSelectedCompanyRole}
                >
                  <SelectTrigger id="roleSelect">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="member">Member</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleAssignUserToCompany}
            disabled={
              isPending ||
              !selectedCompanyId ||
              !selectedCompanyRole ||
              (selectedUser && availableCompanies.length === 0)
            }
          >
            {isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            Assign
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AssignCompanyDialog;
