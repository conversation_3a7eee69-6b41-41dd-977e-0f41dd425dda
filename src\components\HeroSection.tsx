
import React from "react";
import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>R<PERSON>, <PERSON>, Clock, Shield, LineChart } from "lucide-react";

const HeroSection = () => {
  return (
    <div className="container mx-auto px-4 py-20">
      <div className="max-w-3xl mx-auto text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-cyan-600">
         Website Monitoring For Vurbis Clients
                 </h1>
        <p className="text-xl text-slate-600 dark:text-slate-300 mb-8">
          Monitor your websites, get notified when they're down, and visualize uptime performance with our easy-to-use dashboard.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link to="/register">
            <Button size="lg" className="px-8">
              Get Started <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
