import { useEffect } from 'react';

interface DocumentTitleProps {
  title: string;
  suffix?: boolean;
}

export function DocumentTitle({ title, suffix = true }: DocumentTitleProps) {
  useEffect(() => {
    // Set the document title with or without the suffix
    document.title = suffix ? `${title} | Vurbis Uptime Monitor` : title;
    
    // Restore the original title when the component unmounts
    return () => {
      document.title = 'Vurbis Uptime Monitor';
    };
  }, [title, suffix]);

  // This component doesn't render anything
  return null;
}

export default DocumentTitle;
