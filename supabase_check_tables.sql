-- This script checks the current state of the company-related tables
-- Run this in the Supabase SQL Editor

-- Check if the companies table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'companies'
) AS companies_table_exists;

-- Check if the company_members table exists
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'company_members'
) AS company_members_table_exists;

-- Check if the monitors table has a company_id column
SELECT EXISTS (
    SELECT FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'monitors' 
    AND column_name = 'company_id'
) AS monitors_has_company_id;

-- Check the policies on the companies table
SELECT 
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'companies';

-- Check the policies on the company_members table
SELECT 
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'company_members';

-- Check the policies on the monitors table
SELECT 
    policyname,
    cmd,
    roles
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename = 'monitors';

-- Check if the current user has any companies
SELECT 
    c.id,
    c.name,
    c.description,
    cm.role
FROM 
    public.companies c
JOIN 
    public.company_members cm ON c.id = cm.company_id
WHERE 
    cm.user_id = auth.uid();

-- Check if there are any monitors without a company_id
SELECT COUNT(*) AS monitors_without_company
FROM public.monitors
WHERE company_id IS NULL;
