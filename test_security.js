import { addTo<PERSON>hitelist, removeFrom<PERSON>hitelist, isIp<PERSON>hitelisted, rateLimiter, requestLogger } from './security_middleware.js';
import fs from 'fs/promises';
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

const TEST_IP = '*************';

async function testIPWhitelist() {
    console.log('\n1. Testing IP Whitelist:');
    console.log('------------------------');
    
    try {
        // Test adding IP
        console.log(`Adding IP ${TEST_IP} to whitelist...`);
        await addToWhitelist(TEST_IP);
        
        // Test checking IP
        const isWhitelisted = await isIpWhitelisted(TEST_IP);
        console.log(`Is IP whitelisted? ${isWhitelisted}`);
        
        // Test non-whitelisted IP
        const randomIP = '********';
        const isRandomWhitelisted = await isIpWhitelisted(randomIP);
        console.log(`Is random IP (${randomIP}) whitelisted? ${isRandomWhitelisted}`);
        
        // Test removing IP
        console.log(`Removing IP ${TEST_IP} from whitelist...`);
        await removeFromWhitelist(TEST_IP);
        
        // Verify removal
        const isStillWhitelisted = await isIpWhitelisted(TEST_IP);
        console.log(`Is IP still whitelisted? ${isStillWhitelisted}`);
        
    } catch (error) {
        console.error('Error testing IP whitelist:', error);
    }
}

async function testRequestLogging() {
    console.log('\n2. Testing Request Logging:');
    console.log('-------------------------');
    
    const supabase = createClient(
        process.env.SUPABASE_URL,
        process.env.SUPABASE_SERVICE_KEY
    );
    
    try {
        // Test the request logging function directly
        const mockReq = {
            method: 'GET',
            url: '/test',
            ip: TEST_IP,
            headers: {
                'user-agent': 'Test Script',
                'authorization': 'Bearer test-token'
            }
        };
        
        const mockRes = {
            statusCode: 200,
            end: function(chunk, encoding) {
                console.log('Response ended');
            }
        };
        
        const mockNext = () => {};
        
        console.log('Testing request logger...');
        await requestLogger(mockReq, mockRes, mockNext);
        
        // Simulate response end
        mockRes.end();
        
        // Give the logger a moment to write
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Check the log file
        console.log('\nChecking request log file...');
        const logContent = await fs.readFile('requests.log', 'utf-8');
        const lastLog = logContent.trim().split('\n').pop();
        console.log('Last log entry:', JSON.parse(lastLog));
        
    } catch (error) {
        console.error('Error testing request logging:', error);
    }
}

async function testRateLimiting() {
    console.log('\n3. Testing Rate Limiting:');
    console.log('------------------------');
    
    // Create a new rate limiter instance for testing
    const testLimiter = rateLimiter;
    
    // Simulate multiple requests
    const requests = 10;
    console.log(`Simulating ${requests} rapid requests...`);
    
    for (let i = 1; i <= requests; i++) {
        const mockReq = {
            ip: TEST_IP,
            requestNum: i
        };
        
        const mockRes = {
            status: (code) => ({
                json: (data) => console.log(`Request ${mockReq.requestNum}: Status ${code}`, data)
            }),
            set: () => {}
        };
        
        const mockNext = () => console.log(`Request ${mockReq.requestNum}: Passed rate limit`);
        
        await new Promise(resolve => {
            testLimiter(mockReq, mockRes, () => {
                mockNext();
                resolve();
            });
        });
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }
}

// Run all tests
console.log('Starting Security Tests...');

async function runTests() {
    await testIPWhitelist();
    await testRequestLogging();
    await testRateLimiting();
    console.log('\nSecurity Tests Complete!');
}

runTests();
