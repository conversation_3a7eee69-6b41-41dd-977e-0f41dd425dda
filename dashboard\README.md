# VUM - Vurbis Uptime Monitor Dashboard

This is the dashboard for the Vurbis Uptime Monitor (VUM) system. It provides a user-friendly interface to monitor the status of your websites and services.

## Features

- Real-time monitoring of website status
- Historical uptime data visualization
- Response time tracking
- Incident management
- Notifications for status changes
- Mobile-responsive design

## Getting Started

1. Open `index.html` in your web browser to view the dashboard
2. Connect to your Supabase backend by updating the configuration in `scripts.js`

## Structure

- `index.html` - Main dashboard page
- `styles.css` - CSS styles for the dashboard
- `scripts.js` - JavaScript functionality
- `vurbis-logo.svg` - Vurbis logo for the header

## Integration with Monitor Service

This dashboard connects to the VUM Monitor Service to display real-time data. Make sure the monitor service is running and properly configured.

## Customization

You can customize the dashboard by:

1. Modifying the color scheme in `styles.css` (look for the `:root` CSS variables)
2. Adding or removing sections in `index.html`
3. Extending functionality in `scripts.js`

## Browser Compatibility

The dashboard is compatible with all modern browsers:
- Chrome
- Firefox
- Safari
- Edge
