# Monitor Service Fix

This document provides instructions for fixing the monitor service issues.

## Current Issues

1. **Status Constraint Violation**: The monitor service is encountering an error when saving check results:
   ```
   new row for relation "monitor_history" violates check constraint "valid_status"
   ```
   This suggests there's a constraint on the `status` field in the `monitor_history` table that's being violated.

2. **Separate Frontend and Backend**: You want to keep the frontend and backend separate so the backend can run on a dedicated machine.

## Fix Instructions

### 1. Fix the Status Constraint Issue

#### Option A: Update the Database Constraint

Run the `fix_monitor_history_constraint.sql` script in the Supabase SQL Editor. This script:
- Checks the structure of the `monitor_history` table
- Identifies the constraints on the table
- Modifies the `valid_status` constraint to accept the correct data type

#### Option B: Update the Monitor Service Code

Apply the changes from `fix_monitor_status.js` to your monitor service code:

1. Find the function that saves check results (likely in `database.js` or similar)
2. Update it to ensure the status is always a boolean
3. Find the `performCheck` function and ensure it's returning a boolean status

### 2. Diagnose the Issue (Optional)

Run the `check_monitor_history.js` script to diagnose the issue:
```bash
cd monitor-service
node check_monitor_history.js
```

This script:
- Checks the structure of the `monitor_history` table
- Identifies the constraints on the table
- Tries to insert a test record with different status formats

### 3. Keep Frontend and Backend Separate

You already have the correct setup for running the frontend and backend separately:

#### Frontend (from the root directory):
```bash
npm run dev
```

#### Backend (from the monitor-service directory):
```bash
cd monitor-service
npm run start  # or npm run dev for development with auto-restart
```

## Troubleshooting

If you continue to encounter issues:

1. **Check the monitor_history Table Structure**:
   - Make sure the `status` column is defined as a boolean
   - Check if there are any constraints on the `status` column

2. **Check the Monitor Service Code**:
   - Make sure the `status` value being saved is a boolean (true/false)
   - Add debug logging to see the exact value being sent

3. **Check for Other Issues**:
   - Look for any other error messages in the logs
   - Check if there are any network or authentication issues

## Additional Resources

- `monitor-service-patch.js`: Fixes issues with notifications
- `fix_notification_user_id.sql`: Fixes issues with null user_id in notifications
- `fix_monitor_history_constraint.sql`: Fixes the status constraint issue
- `check_monitor_history.js`: Diagnoses issues with the monitor_history table
