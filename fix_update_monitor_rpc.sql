-- This script fixes the issue with the update_monitor_with_companies RPC function
-- Run this in the Supabase SQL Editor

-- Create a new function with a different name to avoid conflicts
CREATE OR REPLACE FUNCTION update_monitor_with_companies_v2(
  monitor_id UUID,
  monitor_name TEXT DEFAULT NULL,
  monitor_target TEXT DEFAULT NULL,
  monitor_type TEXT DEFAULT NULL,
  monitor_interval INTEGER DEFAULT NULL,
  monitor_timeout INTEGER DEFAULT NULL,
  monitor_active BOOLEAN DEFAULT NULL,
  company_ids UUID[] DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  monitor_exists BOOLEAN;
  v_monitor_id ALIAS FOR monitor_id;
  company_id UUID;
BEGIN
  -- Check if the monitor exists
  SELECT EXISTS(SELECT 1 FROM monitors WHERE id = v_monitor_id) INTO monitor_exists;

  IF NOT monitor_exists THEN
    RAISE EXCEPTION 'Monitor with ID % does not exist', v_monitor_id;
  END IF;

  -- Update the monitor
  <PERSON><PERSON><PERSON> monitors
  SET
    name = COALESCE(monitor_name, name),
    target = COALESCE(monitor_target, target),
    type = COALESCE(monitor_type, type),
    interval = COALESCE(monitor_interval, interval),
    timeout = COALESCE(monitor_timeout, timeout),
    active = COALESCE(monitor_active, active)
  WHERE
    id = v_monitor_id;

  -- If company_ids is provided, update the monitor-company relationships
  IF company_ids IS NOT NULL THEN
    -- Delete existing relationships using explicit table reference
    DELETE FROM monitor_companies mc
    WHERE mc.monitor_id = v_monitor_id;

    -- Create new relationships if company_ids is not empty
    IF array_length(company_ids, 1) > 0 THEN
      -- Use a more reliable bulk insert method with explicit parameter reference
      INSERT INTO monitor_companies (monitor_id, company_id)
      SELECT v_monitor_id, c.id
      FROM unnest(company_ids) AS c(id);
    END IF;
  END IF;

  -- Get the updated monitor with its companies
  SELECT
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'target', m.target,
      'type', m.type,
      'interval', m.interval,
      'timeout', m.timeout,
      'active', m.active,
      'user_id', m.user_id,
      'created_at', m.created_at,
      'companies', COALESCE(
        (
          SELECT jsonb_agg(
            jsonb_build_object(
              'id', mc.id,
              'monitor_id', mc.monitor_id,
              'company_id', mc.company_id,
              'created_at', mc.created_at
            )
          )
          FROM monitor_companies mc
          WHERE mc.monitor_id = m.id
        ),
        '[]'::jsonb
      )
    ) INTO result
  FROM
    monitors m
  WHERE
    m.id = v_monitor_id;

  -- If no result was found, raise an exception
  IF result IS NULL THEN
    RAISE EXCEPTION 'Failed to retrieve updated monitor data';
  END IF;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add a comment to explain the function
COMMENT ON FUNCTION update_monitor_with_companies_v2(UUID, TEXT, TEXT, TEXT, INTEGER, INTEGER, BOOLEAN, UUID[]) IS
'Updates a monitor and its company associations. This is an improved version that fixes the ambiguous column reference issue.';

-- Drop the old function if you want to completely replace it (optional)
-- Uncomment the line below if you want to drop the old function after testing the new one
-- DROP FUNCTION IF EXISTS update_monitor_with_companies(uuid, text, text, text, integer, integer, boolean, uuid[]);
