-- Drop existing RLS policies on the user_roles table
DROP POLICY IF EXISTS "Users can view their own roles" ON public.user_roles;
DROP POLICY IF EXISTS "Superadmins can view all roles" ON public.user_roles;
DROP POLICY IF EXISTS "Superadmins can insert roles" ON public.user_roles;
DROP POLICY IF EXISTS "Superadmins can update roles" ON public.user_roles;
DROP POLICY IF EXISTS "Superadmins can delete roles" ON public.user_roles;

-- Create RLS policies for the user_roles table
-- 1. Users can view their own roles
CREATE POLICY "Users can view their own roles"
ON public.user_roles
FOR SELECT
USING (user_id = auth.uid());

-- 2. Use the is_global_superadmin_bypass function to check superadmin status
-- This function doesn't use <PERSON><PERSON> to check superadmin status, avoiding circular dependency
CREATE POLICY "Superadmins can view all roles"
ON public.user_roles
FOR SELECT
USING (is_global_superadmin_bypass());

-- 3. <PERSON><PERSON><PERSON> can insert new roles
CREATE POLICY "Superadmins can insert roles"
ON public.user_roles
FOR INSERT
WITH CHECK (is_global_superadmin_bypass());

-- 4. <PERSON><PERSON><PERSON> can update roles
CREATE POLICY "Superadmins can update roles"
ON public.user_roles
FOR UPDATE
USING (is_global_superadmin_bypass());

-- 5. Superadmins can delete roles
CREATE POLICY "Superadmins can delete roles"
ON public.user_roles
FOR DELETE
USING (is_global_superadmin_bypass());

-- Make sure RLS is enabled on the user_roles table
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
