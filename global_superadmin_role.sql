-- This script modifies the role system to make superadmin a global user role
-- Run this in the Supabase SQL Editor

-- Make sure the uuid-ossp extension is available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create a table for global user roles if it doesn't exist
CREATE TABLE IF NOT EXISTS public.user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role_type user_role NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(user_id, role_type)
);

-- Enable Row Level Security
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;

-- Create policies for user_roles table
CREATE POLICY "Users can view their own global roles"
ON public.user_roles
FOR SELECT
USING (
    user_id = auth.uid()
);

CREATE POLICY "Only superadmins can manage global roles"
ON public.user_roles
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_id = auth.uid()
        AND role_type = 'superadmin'
    )
);

-- Grant necessary permissions
GRANT ALL ON public.user_roles TO authenticated;

-- Create a function to check if a user is a global superadmin
CREATE OR REPLACE FUNCTION is_global_superadmin(
  check_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  user_to_check UUID;
  is_superadmin BOOLEAN;
BEGIN
  -- If no user_id is provided, use the authenticated user
  user_to_check := COALESCE(check_user_id, auth.uid());
  
  -- Check if the user has the superadmin role
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_id = user_to_check
    AND role_type = 'superadmin'
  ) INTO is_superadmin;
  
  RETURN is_superadmin;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to assign global superadmin role
CREATE OR REPLACE FUNCTION assign_global_superadmin(
  target_user_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user is a superadmin
  SELECT is_global_superadmin(admin_user_id) INTO is_admin_superadmin;
  
  -- Only allow superadmins to assign superadmin roles
  IF NOT is_admin_superadmin THEN
    RAISE EXCEPTION 'Only superadmins can assign superadmin roles';
  END IF;
  
  -- Insert or update the user role
  INSERT INTO public.user_roles (user_id, role_type)
  VALUES (target_user_id, 'superadmin')
  ON CONFLICT (user_id, role_type) 
  DO UPDATE SET updated_at = NOW()
  RETURNING jsonb_build_object(
    'id', id,
    'user_id', user_id,
    'role_type', role_type,
    'created_at', created_at,
    'updated_at', updated_at
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to revoke global superadmin role
CREATE OR REPLACE FUNCTION revoke_global_superadmin(
  target_user_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  is_admin_superadmin BOOLEAN;
  success BOOLEAN;
BEGIN
  -- Check if the admin user is a superadmin
  SELECT is_global_superadmin(admin_user_id) INTO is_admin_superadmin;
  
  -- Only allow superadmins to revoke superadmin roles
  IF NOT is_admin_superadmin THEN
    RAISE EXCEPTION 'Only superadmins can revoke superadmin roles';
  END IF;
  
  -- Don't allow revoking your own superadmin role
  IF target_user_id = admin_user_id THEN
    RAISE EXCEPTION 'Cannot revoke your own superadmin role';
  END IF;
  
  -- Delete the user role
  DELETE FROM public.user_roles
  WHERE user_id = target_user_id
  AND role_type = 'superadmin';
  
  success := FOUND;
  
  RETURN success;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Modify the existing company role functions to check for global superadmin
CREATE OR REPLACE FUNCTION assign_admin_role(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user is a global superadmin
  SELECT is_global_superadmin(admin_user_id) INTO is_superadmin;
  
  -- If not a global superadmin, check if they're a company admin
  IF NOT is_superadmin THEN
    SELECT 
      CASE 
        WHEN EXISTS (
          SELECT 1 FROM company_members
          WHERE company_id = assign_admin_role.company_id
          AND user_id = admin_user_id
          AND (role_type = 'admin' OR role_type = 'superadmin')
        ) THEN TRUE
        ELSE FALSE
      END INTO is_admin;
      
    -- Only allow admins and superadmins to assign admin roles
    IF NOT is_admin THEN
      RAISE EXCEPTION 'User does not have permission to assign admin roles';
    END IF;
  END IF;
  
  -- Check if the user is already a member of the company
  IF EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = assign_admin_role.user_id
    AND company_id = assign_admin_role.company_id
  ) THEN
    -- Update the existing membership
    UPDATE public.company_members
    SET role_type = 'admin'
    WHERE user_id = assign_admin_role.user_id
    AND company_id = assign_admin_role.company_id
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  ELSE
    -- Create a new membership
    INSERT INTO public.company_members (user_id, company_id, role_type)
    VALUES (assign_admin_role.user_id, assign_admin_role.company_id, 'admin')
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the user role function to check for global superadmin
CREATE OR REPLACE FUNCTION assign_user_role(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user is a global superadmin
  SELECT is_global_superadmin(admin_user_id) INTO is_superadmin;
  
  -- If not a global superadmin, check if they're a company admin
  IF NOT is_superadmin THEN
    SELECT 
      CASE 
        WHEN EXISTS (
          SELECT 1 FROM company_members
          WHERE company_id = assign_user_role.company_id
          AND user_id = admin_user_id
          AND (role_type = 'admin' OR role_type = 'superadmin')
        ) THEN TRUE
        ELSE FALSE
      END INTO is_admin;
      
    -- Only allow admins and superadmins to assign user roles
    IF NOT is_admin THEN
      RAISE EXCEPTION 'User does not have permission to assign user roles';
    END IF;
  END IF;
  
  -- Check if the user is already a member of the company
  IF EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = assign_user_role.user_id
    AND company_id = assign_user_role.company_id
  ) THEN
    -- Update the existing membership
    UPDATE public.company_members
    SET role_type = 'user'
    WHERE user_id = assign_user_role.user_id
    AND company_id = assign_user_role.company_id
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  ELSE
    -- Create a new membership
    INSERT INTO public.company_members (user_id, company_id, role_type)
    VALUES (assign_user_role.user_id, assign_user_role.company_id, 'user')
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the remove company member function to check for global superadmin
CREATE OR REPLACE FUNCTION remove_company_member(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user is a global superadmin
  SELECT is_global_superadmin(admin_user_id) INTO is_superadmin;
  
  -- If not a global superadmin, check if they're a company admin
  IF NOT is_superadmin THEN
    SELECT 
      CASE 
        WHEN EXISTS (
          SELECT 1 FROM company_members
          WHERE company_id = remove_company_member.company_id
          AND user_id = admin_user_id
          AND (role_type = 'admin' OR role_type = 'superadmin')
        ) THEN TRUE
        ELSE FALSE
      END INTO is_admin;
      
    -- Only allow admins and superadmins to remove users
    IF NOT is_admin THEN
      RAISE EXCEPTION 'User does not have permission to remove company members';
    END IF;
  END IF;
  
  -- Don't allow removing the last admin
  IF (
    SELECT role_type FROM company_members
    WHERE user_id = remove_company_member.user_id
    AND company_id = remove_company_member.company_id
  ) = 'admin' AND (
    SELECT COUNT(*) FROM company_members
    WHERE company_id = remove_company_member.company_id
    AND role_type = 'admin'
  ) <= 1 THEN
    RAISE EXCEPTION 'Cannot remove the last admin from a company';
  END IF;
  
  -- Remove the user from the company
  DELETE FROM public.company_members
  WHERE user_id = remove_company_member.user_id
  AND company_id = remove_company_member.company_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the soft delete monitor function to check for global superadmin
CREATE OR REPLACE FUNCTION soft_delete_monitor(
  monitor_id UUID,
  user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the user is a global superadmin
  SELECT is_global_superadmin(user_id) INTO is_superadmin;
  
  -- If not a global superadmin, check if they're a company admin
  IF NOT is_superadmin THEN
    SELECT 
      CASE 
        WHEN EXISTS (
          SELECT 1 FROM company_members cm
          JOIN monitor_companies mc ON cm.company_id = mc.company_id
          WHERE mc.monitor_id = monitor_id
          AND cm.user_id = user_id
          AND cm.role_type = 'admin'
        ) THEN TRUE
        ELSE FALSE
      END INTO is_admin;
      
    -- Only allow admins and superadmins to soft delete monitors
    IF NOT is_admin THEN
      RAISE EXCEPTION 'User does not have permission to delete this monitor';
    END IF;
  END IF;
  
  -- Soft delete the monitor
  UPDATE monitors
  SET 
    deleted = TRUE,
    deleted_at = NOW(),
    deleted_by = user_id
  WHERE
    id = monitor_id
    AND deleted = FALSE;
  
  -- Get the updated monitor
  SELECT 
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'deleted', m.deleted,
      'deleted_at', m.deleted_at,
      'deleted_by', m.deleted_by
    ) INTO result
  FROM 
    monitors m
  WHERE 
    m.id = monitor_id;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the restore monitor function to check for global superadmin
CREATE OR REPLACE FUNCTION restore_monitor(
  monitor_id UUID,
  user_id UUID DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the user is a global superadmin
  SELECT is_global_superadmin(user_id) INTO is_superadmin;
  
  -- If not a global superadmin, check if they're a company admin
  IF NOT is_superadmin THEN
    SELECT 
      CASE 
        WHEN EXISTS (
          SELECT 1 FROM company_members cm
          JOIN monitor_companies mc ON cm.company_id = mc.company_id
          WHERE mc.monitor_id = monitor_id
          AND cm.user_id = user_id
          AND cm.role_type = 'admin'
        ) THEN TRUE
        ELSE FALSE
      END INTO is_admin;
      
    -- Only allow admins and superadmins to restore monitors
    IF NOT is_admin THEN
      RAISE EXCEPTION 'User does not have permission to restore this monitor';
    END IF;
  END IF;
  
  -- Restore the monitor
  UPDATE monitors
  SET 
    deleted = FALSE,
    deleted_at = NULL,
    deleted_by = NULL
  WHERE
    id = monitor_id
    AND deleted = TRUE;
  
  -- Get the updated monitor
  SELECT 
    jsonb_build_object(
      'id', m.id,
      'name', m.name,
      'deleted', m.deleted,
      'deleted_at', m.deleted_at,
      'deleted_by', m.deleted_by
    ) INTO result
  FROM 
    monitors m
  WHERE 
    m.id = monitor_id;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies for monitors to check for global superadmin
DROP POLICY IF EXISTS "Superadmins can hard delete monitors" ON public.monitors;

CREATE POLICY "Global superadmins can hard delete monitors"
ON public.monitors
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- Update RLS policies for monitor_companies to check for global superadmin
DROP POLICY IF EXISTS "Superadmins can manage all monitor-company relationships" ON public.monitor_companies;

CREATE POLICY "Global superadmins can manage all monitor-company relationships"
ON public.monitor_companies
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_roles.user_id = auth.uid()
        AND user_roles.role_type = 'superadmin'
    )
);

-- Migrate existing superadmins to the global role
INSERT INTO public.user_roles (user_id, role_type)
SELECT DISTINCT user_id, 'superadmin'::user_role
FROM public.company_members
WHERE role_type = 'superadmin'
ON CONFLICT (user_id, role_type) DO NOTHING;

-- Remove the superadmin role from company_members (optional)
-- Uncomment if you want to remove all superadmin roles from company_members
-- UPDATE public.company_members
-- SET role_type = 'admin'
-- WHERE role_type = 'superadmin';

-- Verify the migration
SELECT 
    u.id,
    u.email,
    ur.role_type AS global_role
FROM 
    auth.users u
LEFT JOIN 
    public.user_roles ur ON u.id = ur.user_id
WHERE 
    ur.role_type = 'superadmin'
ORDER BY 
    u.email;
