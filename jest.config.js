export default {
    testEnvironment: 'node',
    testMatch: ['**/*.test.js'],
    testPathIgnorePatterns: [
        '/node_modules/',
        '/tests/', // Ignore backend tests for now
        '/routes/',
        '/middleware/'
    ],
    collectCoverageFrom: [
        'src/**/*.{js,jsx,ts,tsx}',
        '!src/**/*.d.ts'
    ],
    coveragePathIgnorePatterns: [
        '/node_modules/',
        '/tests/',
        '/routes/',
        '/middleware/'
    ],
    setupFiles: ['dotenv/config'],
    transform: {},
    passWithNoTests: true
};
