-- This script checks and fixes the monitor_history table constraints
-- Run this in the Supabase SQL Editor

-- First, let's check the structure of the monitor_history table
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'monitor_history'
ORDER BY 
    ordinal_position;

-- Check the constraints on the monitor_history table
SELECT 
    con.conname AS constraint_name,
    con.contype AS constraint_type,
    pg_get_constraintdef(con.oid) AS constraint_definition
FROM 
    pg_constraint con
    JOIN pg_class rel ON rel.oid = con.conrelid
    JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
WHERE 
    nsp.nspname = 'public'
    AND rel.relname = 'monitor_history';

-- Check a sample record to see the actual data type
SELECT 
    id, 
    monitor_id, 
    status, 
    pg_typeof(status) AS status_type,
    response_time,
    error_message,
    timestamp
FROM 
    public.monitor_history
LIMIT 1;

-- Fix the status column if it's a text type but should be boolean
DO $$
DECLARE
    status_type text;
BEGIN
    -- Get the data type of the status column
    SELECT data_type INTO status_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'monitor_history'
    AND column_name = 'status';
    
    -- If it's text, convert it to boolean
    IF status_type = 'text' THEN
        -- First, check if there are any non-boolean values
        IF EXISTS (
            SELECT 1 FROM public.monitor_history 
            WHERE status NOT IN ('true', 'false')
            AND status IS NOT NULL
        ) THEN
            RAISE NOTICE 'Found non-boolean text values in status column. Manual cleanup needed.';
        ELSE
            -- Convert text to boolean
            ALTER TABLE public.monitor_history ALTER COLUMN status TYPE boolean USING 
                CASE 
                    WHEN status = 'true' THEN true
                    WHEN status = 'false' THEN false
                    ELSE NULL
                END;
                
            RAISE NOTICE 'Converted status column from text to boolean';
        END IF;
    ELSE
        RAISE NOTICE 'Status column is already type: %', status_type;
    END IF;
END $$;

-- Drop the existing constraint if it exists
ALTER TABLE public.monitor_history DROP CONSTRAINT IF EXISTS valid_status;

-- Add the appropriate constraint based on the column type
DO $$
DECLARE
    status_type text;
BEGIN
    -- Get the data type of the status column
    SELECT data_type INTO status_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'monitor_history'
    AND column_name = 'status';
    
    -- Add the appropriate constraint
    IF status_type = 'boolean' THEN
        EXECUTE 'ALTER TABLE public.monitor_history ADD CONSTRAINT valid_status CHECK (status IS NOT NULL)';
        RAISE NOTICE 'Added NOT NULL constraint for boolean status column';
    ELSIF status_type = 'text' THEN
        EXECUTE 'ALTER TABLE public.monitor_history ADD CONSTRAINT valid_status CHECK (status IN (''true'', ''false''))';
        RAISE NOTICE 'Added text value constraint for text status column';
    ELSE
        RAISE NOTICE 'Status column is type: %. No constraint added.', status_type;
    END IF;
END $$;

-- Check if we have any monitor history records
SELECT COUNT(*) AS total_records FROM public.monitor_history;

-- Check the distribution of status values
SELECT status, COUNT(*) FROM public.monitor_history GROUP BY status;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
