import React, { useState, useEffect } from "react";
import { useNavigate, useParams, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { toast } from "@/components/ui/use-toast";
import { ArrowLeft } from "lucide-react";
import AppLayout from "@/components/AppLayout";
import UnifiedHeader from "@/components/UnifiedHeader";
import DocumentTitle from "@/components/DocumentTitle";
import { useAuth } from "@/contexts/AuthContext";
import { useCompany } from "@/contexts/CompanyContext";
import { useMonitors } from "@/hooks/use-monitors";
import { Skeleton } from "@/components/ui/skeleton";
import MonitorCompanySelector from "@/components/MonitorCompanySelector";
import { UpdateMonitorData, DegradedThresholds } from "@/types/monitor";
import MonitorDegradedSettings from "@/components/MonitorDegradedSettings";
import { useCompanyRoles } from "@/hooks/use-company-roles";
import PortSelector from "@/components/PortSelector";
import { checkPorts } from "@/utils/portChecker";

const EditMonitor = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const focusOnDegraded = searchParams.get('focus') === 'degraded';
  const [formData, setFormData] = useState({
    name: "",
    url: "",
    type: "http",
    interval: "5",
    timeout: "30",
    retries: "1",
    alertWhenDown: true,
    alertWhenUp: true,
    alertMethod: "email",
  });

  // State for selected companies
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<string[]>([]);

  // State for degraded settings
  const [degradedSettings, setDegradedSettings] = useState<DegradedThresholds | null>(null);

  // State for port monitoring
  const [portHost, setPortHost] = useState("");
  const [selectedPorts, setSelectedPorts] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reference for scrolling to degraded settings
  const degradedSettingsRef = React.useRef<HTMLDivElement>(null);

  const navigate = useNavigate();
  const { user } = useAuth();
  const { currentCompany, isAdmin } = useCompany();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { useGetMonitorQuery, useUpdateMonitorMutation } = useMonitors();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();

  // Fetch the monitor data
  const { data: monitor, isLoading, error } = useGetMonitorQuery(id || '');

  // Check if company has changed and navigate to dashboard if needed
  useEffect(() => {
    // Only check once when the component mounts
    const companyChanged = localStorage.getItem('company_changed');
    if (companyChanged === 'true') {
      console.log('Company changed, navigating to dashboard');
      localStorage.removeItem('company_changed');
      navigate('/dashboard');
    }
  }, []);

  // Update mutation
  const updateMonitor = useUpdateMonitorMutation();

  // Redirect if not an admin (but only if a company is selected)
  useEffect(() => {
    if (currentCompany && !isAdmin) {
      toast({
        title: "Access Denied",
        description: "You do not have permission to edit monitors",
        variant: "destructive",
      });
      navigate("/dashboard");
    }

    // Superadmin check is now handled by the useGlobalSuperadminQuery hook
  }, [currentCompany, isAdmin, navigate]);

  // Scroll to degraded settings if focus parameter is present
  useEffect(() => {
    if (focusOnDegraded && degradedSettingsRef.current) {
      // Wait for the component to fully render
      setTimeout(() => {
        degradedSettingsRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
        // Add a highlight effect
        degradedSettingsRef.current?.classList.add('bg-blue-50', 'dark:bg-blue-900/20');
        setTimeout(() => {
          degradedSettingsRef.current?.classList.remove('bg-blue-50', 'dark:bg-blue-900/20');
        }, 2000); // Remove highlight after 2 seconds
      }, 500);
    }
  }, [focusOnDegraded, monitor]);

  // Populate form with monitor data when it loads
  useEffect(() => {
    if (monitor) {
      // For port monitoring, parse the target (format: host|port1,port2,port3)
      if (monitor.type === 'port' && monitor.target.includes('|')) {
        const [host, portsStr] = monitor.target.split('|');
        setPortHost(host);
        const ports = portsStr.split(',').map(p => parseInt(p.trim())).filter(p => !isNaN(p));
        setSelectedPorts(ports);
      }

      setFormData({
        name: monitor.name,
        url: monitor.target,
        type: monitor.type,
        interval: monitor.interval.toString(),
        timeout: monitor.timeout.toString(),
        retries: "1", // Default value if not stored
        alertWhenDown: true, // Default value if not stored
        alertWhenUp: true, // Default value if not stored
        alertMethod: "email", // Default value if not stored
      });

      // Set selected companies from the monitor data
      if (monitor.companies && Array.isArray(monitor.companies)) {
        setSelectedCompanyIds(monitor.companies.map(company => company.company_id));
      } else if (monitor.company_id) {
        // Fallback for backward compatibility
        setSelectedCompanyIds([monitor.company_id]);
      }

      // Load monitor-specific degraded settings if they exist
      const loadDegradedSettings = async () => {
        try {
          const { data, error } = await supabase
            .rpc('get_monitor_degraded_settings_rpc', { p_monitor_id: monitor.id });

          if (error) {
            console.error('Error loading degraded settings:', error);
            return;
          }

          if (data) {
            setDegradedSettings({
              response_time: data.response_time,
              error_rate: data.error_rate,
              status_codes: data.status_codes || [],
              consecutive_failures: data.consecutive_failures
            });
          }
        } catch (error) {
          console.error('Error loading degraded settings:', error);
        }
      };

      loadDegradedSettings();
    }
  }, [monitor]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!user || !id) {
      toast({
        title: "Error",
        description: "You must be logged in to update a monitor.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // If a company is selected, check if the user is an admin
    if (currentCompany && !isAdmin) {
      toast({
        title: "Access Denied",
        description: "You do not have permission to update monitors in this company",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // Validate that at least one company is selected
    if (selectedCompanyIds.length === 0) {
      toast({
        title: "Company Required",
        description: "Please select at least one company for this monitor.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    // For port monitoring, check if the host exists before updating the monitor
    if (formData.type === 'port') {
      try {
        // Check if the host exists
        const results = await checkPorts(portHost, selectedPorts);
        const hostExists = results.some(result => result.status);

        if (!hostExists) {
          // Ask for confirmation before updating a monitor for a host that doesn't exist
          if (!window.confirm(
            "The host could not be found in DNS lookups. The monitor will likely show as DOWN. Do you still want to update this monitor?"
          )) {
            setIsSubmitting(false);
            return;
          }
        }
      } catch (error) {
        console.error('Error checking host:', error);
        // Continue with monitor update even if host checking fails
      }
    }

    // Create the monitor update object from form data
    let target = formData.url;

    // For port monitoring, format the target as host|port1,port2,port3
    if (formData.type === 'port') {
      if (!portHost) {
        toast({
          title: "Host Required",
          description: "Please enter a host for port monitoring.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      if (selectedPorts.length === 0) {
        toast({
          title: "Ports Required",
          description: "Please select at least one port to monitor.",
          variant: "destructive",
        });
        setIsSubmitting(false);
        return;
      }

      target = `${portHost}|${selectedPorts.join(',')}`;
    }

    const monitorUpdate: UpdateMonitorData & { id: string } = {
      id,
      name: formData.name,
      target: target,
      type: formData.type as any, // Cast to the correct type
      interval: parseInt(formData.interval),
      timeout: parseInt(formData.timeout),
      company_ids: selectedCompanyIds,
      degraded_settings: degradedSettings, // Include degraded settings
    };

    updateMonitor.mutate(monitorUpdate, {
      onSuccess: async (updatedMonitor) => {
        // Show success message
        toast({
          title: "Monitor Updated",
          description: "Your monitor has been updated successfully.",
        });

        // Set a timeout for the initial check to prevent UI from getting stuck
        const initialCheckTimeout = 5000; // 5 seconds timeout
        let initialCheckCompleted = false;

        // Perform an immediate check of the updated monitor in the background
        const checkPromise = (async () => {
          try {
            // Call the monitor-checker edge function with the monitor ID
            const { data, error } = await supabase.functions.invoke('monitor-checker', {
              body: { monitorId: updatedMonitor.id },
            });

            if (error) throw error;
            initialCheckCompleted = true;
          } catch (checkError) {
            console.error("Error performing check after update:", checkError);
            // Only show toast if we're still on this page
            if (!initialCheckCompleted) {
              toast({
                title: "Note",
                description: "Monitor updated, but initial check is still pending. It will be checked according to its schedule.",
                variant: "default",
              });
            }
          }
        })();

        // Set a timeout to ensure we don't wait too long for the initial check
        const timeoutPromise = new Promise(resolve => setTimeout(() => {
          if (!initialCheckCompleted) {
            console.log("Initial check timed out after", initialCheckTimeout, "ms");
            resolve(null);
          }
        }, initialCheckTimeout));

        // Wait for either the check to complete or the timeout to expire
        await Promise.race([checkPromise, timeoutPromise]);

        // Navigate to the monitor detail page
        navigate(`/monitor/${id}`);
      },
      onError: (error) => {
        console.error("Error updating monitor:", error);

        // Check for specific error messages
        let errorMessage = "There was an error updating your monitor. Please try again.";

        if (error.message) {
          if (error.message.includes("Monitor limit reached")) {
            errorMessage = "Monitor limit reached for this company. Please upgrade your subscription to add more monitors.";
          } else {
            // Use the actual error message from the server
            errorMessage = error.message;
          }
        }

        toast({
          title: "Error updating monitor",
          description: errorMessage,
          variant: "destructive",
        });
        setIsSubmitting(false);
      },
      // Always reset the submitting state when the mutation settles
      onSettled: () => {
        setIsSubmitting(false);
      }
    });
  };

  const loadingHeader = (
    <UnifiedHeader
      title="Loading Monitor..."
    />
  );

  const errorHeader = (
    <UnifiedHeader
      title="Monitor Not Found"
      actions={
        <Button variant="outline" onClick={() => navigate("/dashboard")}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
      }
    />
  );

  if (isLoading) {
    return (
      <AppLayout header={loadingHeader}>
        <DocumentTitle title="Loading Monitor..." />
        <div className="container mx-auto py-8 px-4">
          <Card className="max-w-3xl mx-auto">
            <CardHeader>
              <Skeleton className="h-8 w-1/3 mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {[1, 2, 3, 4, 5].map((i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  if (error || !monitor) {
    return (
      <AppLayout header={errorHeader}>
        <DocumentTitle title="Monitor Not Found" />
        <div className="container mx-auto py-8 px-4">
          <Card className="max-w-3xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">Error</CardTitle>
              <CardDescription>
                Could not load monitor details. The monitor may not exist or you don't have permission to edit it.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => navigate("/dashboard")}>Return to Dashboard</Button>
            </CardContent>
          </Card>
        </div>
      </AppLayout>
    );
  }

  const header = (
    <UnifiedHeader
      title={`Edit Monitor: ${monitor.name}`}
      actions={
        <Button variant="outline" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
      }
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title={`Edit Monitor: ${monitor.name}`} />
      <div className="container mx-auto py-8 px-4">

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle className="text-2xl">Edit Monitor</CardTitle>
            <CardDescription>
              Update the settings for {monitor.name}.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Basic Information</h3>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Monitor Name</Label>
                    <Input
                      id="name"
                      name="name"
                      placeholder="My Website"
                      required
                      value={formData.name}
                      onChange={handleChange}
                    />
                  </div>
                  {formData.type !== 'port' ? (
                    <div className="space-y-2">
                      <Label htmlFor="url">URL</Label>
                      <Input
                        id="url"
                        name="url"
                        placeholder="https://example.com"
                        required
                        value={formData.url}
                        onChange={handleChange}
                      />
                    </div>
                  ) : (
                    <PortSelector
                      host={portHost}
                      onHostChange={setPortHost}
                      selectedPorts={selectedPorts}
                      onPortsChange={setSelectedPorts}
                      isEditing={true}
                    />
                  )}
                  <div className="space-y-2">
                    <Label htmlFor="companies">Assign to Companies</Label>
                    <MonitorCompanySelector
                      selectedCompanyIds={selectedCompanyIds}
                      onChange={setSelectedCompanyIds}
                    />
                    <p className="text-sm text-slate-500 dark:text-slate-400">
                      Select one or more companies to assign this monitor to
                    </p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Monitor Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="interval">Check Interval</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("interval", value)}
                      defaultValue={formData.interval}
                      value={formData.interval}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 minute</SelectItem>
                        <SelectItem value="5">5 minutes</SelectItem>
                        <SelectItem value="10">10 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">60 minutes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout">Timeout</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("timeout", value)}
                      defaultValue={formData.timeout}
                      value={formData.timeout}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select timeout" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 seconds</SelectItem>
                        <SelectItem value="10">10 seconds</SelectItem>
                        <SelectItem value="30">30 seconds</SelectItem>
                        <SelectItem value="60">60 seconds</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">Monitor Type</Label>
                    <Select
                      onValueChange={(value) => handleSelectChange("type", value)}
                      defaultValue={formData.type}
                      value={formData.type}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="http">HTTP</SelectItem>
                        <SelectItem value="port">Port</SelectItem>
                        <SelectItem value="ping" disabled>Ping (Coming Soon)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Degraded Settings - Only visible to superadmins */}
              <div ref={degradedSettingsRef} className="transition-colors duration-500 rounded-md p-2 -m-2">
                <MonitorDegradedSettings
                  monitorId={id || ''}
                  initialSettings={degradedSettings}
                  onSettingsChange={setDegradedSettings}
                  isSuperadmin={isSuperadmin}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Alert Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="alertWhenDown" className="cursor-pointer">Alert when down</Label>
                      <Switch
                        id="alertWhenDown"
                        checked={formData.alertWhenDown}
                        onCheckedChange={(checked) => handleSwitchChange("alertWhenDown", checked)}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="alertWhenUp" className="cursor-pointer">Alert when back up</Label>
                      <Switch
                        id="alertWhenUp"
                        checked={formData.alertWhenUp}
                        onCheckedChange={(checked) => handleSwitchChange("alertWhenUp", checked)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Alert Method</Label>
                    <RadioGroup
                      defaultValue={formData.alertMethod}
                      value={formData.alertMethod}
                      onValueChange={(value) => handleSelectChange("alertMethod", value)}
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="email" id="email" />
                        <Label htmlFor="email">Email</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="sms" id="sms" disabled />
                        <Label htmlFor="sms" className="text-muted-foreground">SMS (Coming Soon)</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="webhook" id="webhook" disabled />
                        <Label htmlFor="webhook" className="text-muted-foreground">Webhook (Coming Soon)</Label>
                      </div>
                    </RadioGroup>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button variant="outline" type="button" onClick={() => navigate(-1)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting || updateMonitor.isPending}>
                  {isSubmitting || updateMonitor.isPending ? "Updating..." : "Update Monitor"}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default EditMonitor;
