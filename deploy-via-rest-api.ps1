# PowerShell script to deploy the monitor-checker function via the Supabase REST API

Write-Host "This script will help you deploy the monitor-checker function via the Supabase REST API" -ForegroundColor Cyan
Write-Host "Note: This is an alternative approach if the Supabase CLI is not working" -ForegroundColor Yellow

# Prompt for Supabase access token
$accessToken = Read-Host "Enter your Supabase access token (from https://app.supabase.com/account/tokens)"

# Project reference
$projectRef = "axcfqilzeombkbzebeym"

# Function details
$functionName = "monitor-checker"
$functionPath = "supabase/functions/monitor-checker"

# Check if the function directory exists
if (-not (Test-Path $functionPath)) {
    Write-Host "Error: Function directory not found at $functionPath" -ForegroundColor Red
    exit 1
}

# Read the function code
$functionCode = Get-Content -Path "$functionPath/index.ts" -Raw

Write-Host "Function code loaded successfully" -ForegroundColor Green
Write-Host "Preparing to deploy..." -ForegroundColor Yellow

# Create the request body
$body = @{
    name = $functionName
    verify_jwt = $true
    body = $functionCode
} | ConvertTo-<PERSON>son

try {
    # Make the API request
    $response = Invoke-RestMethod `
        -Uri "https://api.supabase.com/v1/projects/$projectRef/functions" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $accessToken"
            "Content-Type" = "application/json"
        } `
        -Body $body
    
    Write-Host "Function deployed successfully!" -ForegroundColor Green
    Write-Host "Function ID: $($response.id)" -ForegroundColor Cyan
} catch {
    Write-Host "Error deploying function: $_" -ForegroundColor Red
    if ($_.Exception.Response) {
        $responseBody = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseBody)
        $responseContent = $reader.ReadToEnd()
        Write-Host "Response content: $responseContent" -ForegroundColor Red
    }
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
