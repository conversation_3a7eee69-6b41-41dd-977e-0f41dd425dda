import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from '@/components/ui/use-toast';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { supabase } from '@/integrations/supabase/client';

/**
 * A component to test superadmin access using different methods
 */
const SuperadminAccessTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const { isGlobalSuperadmin, useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin, isLoading: queryLoading } = useGlobalSuperadminQuery();

  const runTests = async () => {
    setIsLoading(true);
    const results: Record<string, any> = {};

    try {
      // Test 1: Using the hook function directly
      try {
        const hookResult = await isGlobalSuperadmin();
        results.hookFunction = { success: true, result: hookResult };
      } catch (err) {
        results.hookFunction = { 
          success: false, 
          error: err instanceof Error ? err.message : 'Unknown error' 
        };
      }

      // Test 2: Using the query hook
      results.queryHook = { 
        success: !queryLoading, 
        result: isSuperadmin,
        loading: queryLoading
      };

      // Test 3: Using the check_superadmin_access function directly
      try {
        const { data, error } = await supabase.rpc('check_superadmin_access');
        results.directFunction = { 
          success: !error, 
          result: data,
          error: error?.message
        };
      } catch (err) {
        results.directFunction = { 
          success: false, 
          error: err instanceof Error ? err.message : 'Unknown error' 
        };
      }

      // Test 4: Direct query to user_roles table
      try {
        const { data, error } = await supabase
          .from('user_roles')
          .select('*')
          .eq('role_type', 'superadmin');
        
        results.directQuery = { 
          success: !error, 
          result: data,
          error: error?.message
        };
      } catch (err) {
        results.directQuery = { 
          success: false, 
          error: err instanceof Error ? err.message : 'Unknown error' 
        };
      }

      setTestResults(results);
      toast({
        title: 'Tests completed',
        description: 'Superadmin access tests have been completed.',
      });
    } catch (err) {
      console.error('Error running tests:', err);
      toast({
        title: 'Error',
        description: `Failed to run tests: ${err instanceof Error ? err.message : 'Unknown error'}`,
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>Superadmin Access Test</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-slate-500">
                Test different methods of checking superadmin access
              </p>
            </div>
            <Button onClick={runTests} disabled={isLoading}>
              {isLoading ? 'Running Tests...' : 'Run Tests'}
            </Button>
          </div>

          {Object.keys(testResults).length > 0 && (
            <div className="mt-4">
              <h3 className="text-lg font-medium mb-2">Test Results</h3>
              <pre className="bg-slate-100 dark:bg-slate-800 p-4 rounded-md overflow-auto text-sm">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SuperadminAccessTest;
