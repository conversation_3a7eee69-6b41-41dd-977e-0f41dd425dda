-- This script removes the user_id field from the notifications table
-- and updates related functions and policies

-- First, drop the foreign key constraint
ALTER TABLE public.notifications
DROP CONSTRAINT IF EXISTS notifications_user_id_fkey;

-- Update the RLS policies to use company_id instead of user_id
DROP POLICY IF EXISTS "Users can view their own notifications" ON public.notifications;
CREATE POLICY "Users can view notifications for their companies" 
ON public.notifications
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = notifications.company_id
        AND company_members.user_id = auth.uid()
    )
);

DROP POLICY IF EXISTS "Users can update their own notifications" ON public.notifications;
CREATE POLICY "Users can update notifications for their companies" 
ON public.notifications
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = notifications.company_id
        AND company_members.user_id = auth.uid()
    )
);

-- Update the notify_monitor_status_change function to remove user_id
CREATE OR REPLACE FUNCTION public.notify_monitor_status_change()
R<PERSON><PERSON>NS trigger
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  previous_status BOOLEAN;
  monitor_record RECORD;
  company_record RECORD;
BEGIN
  -- Get the previous status for this monitor
  SELECT status INTO previous_status
  FROM monitor_history
  WHERE monitor_id = NEW.monitor_id
  AND id != NEW.id
  ORDER BY timestamp DESC
  LIMIT 1;
  
  -- If this is the first check or status has changed
  IF previous_status IS NULL OR previous_status != NEW.status THEN
    -- Get monitor details
    SELECT * INTO monitor_record
    FROM monitors
    WHERE id = NEW.monitor_id;
    
    -- For each company associated with this monitor
    FOR company_record IN 
      SELECT company_id
      FROM monitor_companies
      WHERE monitor_id = NEW.monitor_id
    LOOP
      -- Insert a notification record
      INSERT INTO notifications (
        monitor_id,
        company_id,
        message,
        type,
        read,
        created_at
      ) VALUES (
        NEW.monitor_id,
        company_record.company_id,
        CASE 
          WHEN NEW.status = TRUE THEN 'Monitor ' || monitor_record.name || ' is now operational'
          ELSE 'Monitor ' || monitor_record.name || ' is down'
        END,
        CASE 
          WHEN NEW.status = TRUE THEN 'up'
          ELSE 'down'
        END,
        false,
        NOW()
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Update the test_monitor_notification function to remove user_id
CREATE OR REPLACE FUNCTION public.test_monitor_notification(p_monitor_id uuid DEFAULT NULL, p_company_id uuid DEFAULT NULL, p_status text DEFAULT 'down'::text)
RETURNS text
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  v_monitor_id UUID;
  v_company_id UUID;
  v_monitor_name TEXT;
  v_message TEXT;
  v_previous_status BOOLEAN;
  v_status_changed BOOLEAN;
BEGIN
  -- If no monitor ID is provided, get the first active monitor
  IF p_monitor_id IS NULL THEN
    SELECT id, name INTO v_monitor_id, v_monitor_name
    FROM monitors
    WHERE active = true
    LIMIT 1;
    
    IF v_monitor_id IS NULL THEN
      RETURN 'No active monitors found. Please create a monitor first.';
    END IF;
  ELSE
    SELECT id, name INTO v_monitor_id, v_monitor_name
    FROM monitors
    WHERE id = p_monitor_id;
    
    IF v_monitor_id IS NULL THEN
      RETURN 'Monitor not found with ID: ' || p_monitor_id;
    END IF;
  END IF;
  
  -- If no company ID is provided, get the first company associated with the monitor
  IF p_company_id IS NULL THEN
    SELECT company_id INTO v_company_id
    FROM monitor_companies
    WHERE monitor_id = v_monitor_id
    LIMIT 1;
    
    IF v_company_id IS NULL THEN
      -- If no company is associated, get the first company
      SELECT id INTO v_company_id
      FROM companies
      LIMIT 1;
      
      IF v_company_id IS NULL THEN
        RETURN 'No companies found. Please create a company first.';
      END IF;
    END IF;
  ELSE
    v_company_id := p_company_id;
  END IF;
  
  -- Validate status
  IF p_status NOT IN ('up', 'down', 'degraded') THEN
    RAISE WARNING 'Invalid status: %. Defaulting to down', p_status;
    p_status := 'down';
  END IF;
  
  -- Get the previous status
  SELECT status INTO v_previous_status
  FROM monitor_history
  WHERE monitor_id = v_monitor_id
  ORDER BY timestamp DESC
  LIMIT 1;
  
  -- Determine if status has changed
  IF v_previous_status IS NULL THEN
    v_status_changed := TRUE;
  ELSE
    -- Convert text status to boolean for comparison
    v_status_changed := (p_status = 'up' AND v_previous_status = FALSE) OR 
                        (p_status != 'up' AND v_previous_status = TRUE);
  END IF;
  
  -- Use our custom function to insert the history record with proper status conversion
  PERFORM insert_monitor_history(
    v_monitor_id,
    p_status,
    CASE WHEN p_status = 'up' THEN 200 ELSE NULL END,
    CASE WHEN p_status = 'up' THEN NULL ELSE 'Test error message' END,
    NOW()
  );
  
  -- Create a notification
  IF v_status_changed THEN
    INSERT INTO notifications (
      monitor_id,
      company_id,
      message,
      type,
      read,
      created_at
    ) VALUES (
      v_monitor_id,
      v_company_id,
      CASE 
        WHEN p_status = 'up' THEN 'Monitor ' || v_monitor_name || ' is now operational'
        WHEN p_status = 'down' THEN 'Monitor ' || v_monitor_name || ' is down'
        ELSE 'Monitor ' || v_monitor_name || ' is experiencing degraded performance'
      END,
      CASE 
        WHEN p_status = 'up' THEN 'up'
        WHEN p_status = 'down' THEN 'down'
        ELSE 'other'
      END,
      false,
      NOW()
    );
  END IF;
  
  -- Return success message
  RETURN 'Test notification triggered for monitor: ' || v_monitor_name || 
         ' with status: ' || p_status || 
         ' for company ID: ' || v_company_id || 
         CASE WHEN v_status_changed 
           THEN E'\nStatus changed from ' || COALESCE(v_previous_status::text, 'NULL') || ' to ' || p_status || '. Notification created.'
           ELSE E'\nStatus did not change. No notification created.'
         END;
END;
$$;

-- Finally, drop the user_id column from the notifications table
ALTER TABLE public.notifications
DROP COLUMN IF EXISTS user_id;
