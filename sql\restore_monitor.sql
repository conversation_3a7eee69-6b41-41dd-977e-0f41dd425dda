-- Create a function to restore a soft-deleted monitor
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION restore_monitor(
    monitor_id UUID,
    user_id UUID
) RETURNS JSONB AS $$
DECLARE
    result JSONB;
    monitor_data JSONB;
    is_admin BOOLEAN;
    is_superadmin BOOLEAN;
BEGIN
    -- Get the monitor data before restoration for audit log
    SELECT to_jsonb(m) INTO monitor_data
    FROM monitors m
    WHERE id = monitor_id AND deleted = TRUE;
    
    -- If monitor doesn't exist or is not deleted
    IF monitor_data IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Check if the user is a global superadmin
    SELECT is_global_superadmin(user_id) INTO is_superadmin;
    
    -- If not a global superadmin, check if they're a company admin
    IF NOT is_superadmin THEN
        SELECT 
            CASE 
                WHEN EXISTS (
                    SELECT 1 FROM company_members cm
                    JOIN monitor_companies mc ON cm.company_id = mc.company_id
                    WHERE mc.monitor_id = monitor_id
                    AND cm.user_id = restore_monitor.user_id
                    AND cm.role_type = 'admin'
                ) THEN TRUE
                ELSE FALSE
            END INTO is_admin;
            
        -- Only allow admins and superadmins to restore monitors
        IF NOT is_admin THEN
            RAISE EXCEPTION 'User does not have permission to restore this monitor';
        END IF;
    END IF;
    
    -- Restore the monitor
    UPDATE monitors
    SET 
        deleted = FALSE,
        deleted_at = NULL,
        deleted_by = NULL
    WHERE
        id = monitor_id
        AND deleted = TRUE
    RETURNING to_jsonb(monitors.*) INTO result;
    
    -- Log the action
    PERFORM log_action(
        user_id,
        'restore',
        'monitors',
        monitor_id,
        monitor_data,
        result
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
