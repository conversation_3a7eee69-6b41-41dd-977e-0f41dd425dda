-- This script creates a storage bucket for avatars in Supabase
-- Run this in the Supabase SQL Editor

-- Create a storage bucket for avatars (if it doesn't exist)
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Set up security policies for the avatars bucket
-- Allow authenticated users to upload their own avatar
CREATE POLICY "Users can upload their own avatar"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'avatars')
ON CONFLICT DO NOTHING;

-- Allow authenticated users to update their own avatar
CREATE POLICY "Users can update their own avatar"
ON storage.objects
FOR UPDATE
TO authenticated
USING (bucket_id = 'avatars')
ON CONFLICT DO NOTHING;

-- Allow authenticated users to read their own avatar
CREATE POLICY "Users can read their own avatar"
ON storage.objects
FOR SELECT
TO authenticated
USING (bucket_id = 'avatars')
ON CONFLICT DO NOTHING;

-- Allow public access to all avatars (for display purposes)
CREATE POLICY "Public can view all avatars"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'avatars')
ON CONFLICT DO NOTHING;

-- Add a policy to allow users to delete their own avatars
CREATE POLICY "Users can delete their own avatars"
ON storage.objects
FOR DELETE
TO authenticated
USING (bucket_id = 'avatars')
ON CONFLICT DO NOTHING;
