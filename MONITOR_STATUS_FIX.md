# Monitor Status Fix

This document provides instructions for fixing the monitor status issues in the VUM application.

## Current Issues

1. **Status Constraint Violation**: The monitor service is encountering an error when saving check results:
   ```
   new row for relation "monitor_history" violates check constraint "valid_status"
   ```

2. **Status Counts Not Showing**: The monitor overview page is showing 0 for all status counts (active, degraded, paused, down).

3. **Type Mismatch**: There's a type mismatch between what the monitor service is sending and what the database expects:
   ```
   ERROR: 42883: operator does not exist: text = boolean
   ```

## Fix Instructions

### 1. Fix the Database Schema and Data

Run the `fix_monitor_history_data.sql` script in the Supabase SQL Editor. This script:

- Checks the structure of the `monitor_history` table
- Fixes any invalid data in the `status` column
- Converts the `status` column to boolean if it's currently text
- Adds the correct constraint to ensure `status` is not null
- Creates helper functions and views for status counts

### 2. Fix the Monitor Service Code

Apply the changes from `fix_monitor_service_code.js` to your monitor service code:

1. Find the function that saves check results and ensure it's sending a boolean status
2. Find the function that performs checks and ensure it's setting status as a boolean
3. Add debug logging to help diagnose issues
4. Add a database connection test function

Key points to check:

- Make sure `status` is always a boolean (true/false) when saving to the database
- Use `Boolean(status)` to convert any value to a proper boolean
- Add logging to show the type of the status value being saved

### 3. Restart the Monitor Service

After making these changes:

1. Stop the current monitor service
2. Start the updated monitor service:
   ```bash
   cd monitor-service
   npm run start
   ```

### 4. Verify the Fix

1. Check the monitor service logs for any errors
2. Refresh the dashboard and verify that status counts are showing correctly
3. Check individual monitors to ensure they show the correct status

## Troubleshooting

If you continue to encounter issues:

1. **Check the Data Types**:
   - Run this query to see the actual data type of the status column:
     ```sql
     SELECT column_name, data_type 
     FROM information_schema.columns
     WHERE table_schema = 'public' 
     AND table_name = 'monitor_history'
     AND column_name = 'status';
     ```

2. **Check the Actual Data**:
   - Run this query to see what values are in the status column:
     ```sql
     SELECT status, COUNT(*) 
     FROM public.monitor_history 
     GROUP BY status;
     ```

3. **Test a Manual Insert**:
   - Try inserting a record with a boolean status:
     ```sql
     INSERT INTO public.monitor_history (
       monitor_id, 
       status, 
       response_time, 
       error_message, 
       timestamp
     ) VALUES (
       '00000000-0000-0000-0000-000000000000',
       true,
       100,
       'Test record',
       NOW()
     );
     ```

4. **Check the Monitor Service Code**:
   - Add console.log statements to show the type of status being saved:
     ```javascript
     console.log(`Status: ${status}, Type: ${typeof status}`);
     ```

## Additional Resources

- `fix_monitor_history_data.sql`: Fixes the database schema and data
- `fix_monitor_service_code.js`: Provides code snippets to fix the monitor service
