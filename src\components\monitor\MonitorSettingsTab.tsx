import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Settings, AlertTriangle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { formatDateTime } from "@/utils/dateFormat";
import { DegradedThresholds } from "@/types/monitor";

interface MonitorSettingsTabProps {
  monitor: any;
  isAdmin: boolean;
  isSuperadmin: boolean;
  degradedSettings: DegradedThresholds | null;
  globalDegradedSettings: DegradedThresholds | null;
  loadingDegradedSettings: boolean;
  onEditClick: () => void;
  onEditDegradedClick: () => void;
}

const MonitorSettingsTab: React.FC<MonitorSettingsTabProps> = ({
  monitor,
  isAdmin,
  isSuperadmin,
  degradedSettings,
  globalDegradedSettings,
  loadingDegradedSettings,
  onEditClick,
  onEditDegradedClick
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Monitor Settings</CardTitle>
        <CardDescription>
          Current configuration for this monitor
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium mb-2">Basic Information</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Monitor Type:</span>
                <span className="font-medium">{monitor.type.toUpperCase()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Created:</span>
                <span className="font-medium">{formatDateTime(new Date(monitor.created_at))}</span>
              </div>
            </div>
          </div>
          <div>
            <h3 className="font-medium mb-2">Monitor Configuration</h3>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Check Interval:</span>
                <span className="font-medium">{monitor.interval} minutes</span>
              </div>
              <div className="flex justify-between">
                <span className="text-slate-600 dark:text-slate-400">Timeout:</span>
                <span className="font-medium">{monitor.timeout} seconds</span>
              </div>
            </div>
          </div>
        </div>

        {isSuperadmin && (
          <div className="mt-6">
            <h3 className="font-medium mb-2 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
              Degraded Service Settings
              {degradedSettings && (
                <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                  Custom Settings Active
                </Badge>
              )}
            </h3>

            {loadingDegradedSettings ? (
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : degradedSettings ? (
              <div className="space-y-4 border-2 border-blue-200 dark:border-blue-800 rounded-md p-4 relative bg-blue-50/50 dark:bg-blue-900/20">
                {isSuperadmin && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="absolute top-2 right-2"
                    onClick={onEditDegradedClick}
                  >
                    <Settings className="h-4 w-4 mr-1" />
                    Edit Degraded Settings
                  </Button>
                )}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Response Time Threshold</h4>
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                      <span className="font-medium">{degradedSettings.response_time} ms</span>
                    </div>
                    {globalDegradedSettings && (
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>Global Default:</span>
                        <span>{globalDegradedSettings.response_time} ms</span>
                      </div>
                    )}
                    <p className="text-xs text-slate-500 mt-1">
                      Services with response times above this threshold will be considered degraded.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Error Rate Threshold</h4>
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                      <span className="font-medium">{degradedSettings.error_rate}%</span>
                    </div>
                    {globalDegradedSettings && (
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>Global Default:</span>
                        <span>{globalDegradedSettings.error_rate}%</span>
                      </div>
                    )}
                    <p className="text-xs text-slate-500 mt-1">
                      Services with error rates above this percentage will be considered degraded.
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Consecutive Failures</h4>
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                      <span className="font-medium">{degradedSettings.consecutive_failures}</span>
                    </div>
                    {globalDegradedSettings && (
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>Global Default:</span>
                        <span>{globalDegradedSettings.consecutive_failures}</span>
                      </div>
                    )}
                    <p className="text-xs text-slate-500 mt-1">
                      Number of consecutive partial failures before a service is considered degraded.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">Status Codes</h4>
                    <div className="flex justify-between">
                      <span className="text-slate-600 dark:text-slate-400">Custom Setting:</span>
                      <span className="font-medium">
                        {degradedSettings.status_codes?.length > 0
                          ? degradedSettings.status_codes.join(', ')
                          : 'None'}
                      </span>
                    </div>
                    {globalDegradedSettings && (
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>Global Default:</span>
                        <span>
                          {globalDegradedSettings.status_codes?.length > 0
                            ? globalDegradedSettings.status_codes.join(', ')
                            : 'None'}
                        </span>
                      </div>
                    )}
                    <p className="text-xs text-slate-500 mt-1">
                      HTTP status codes that indicate a degraded service rather than a complete outage.
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-slate-600 dark:text-slate-400 border rounded-md p-4">
                <p>This monitor uses global degraded settings. No custom settings are configured.</p>
                {globalDegradedSettings && (
                  <div className="mt-2 space-y-2">
                    <div className="grid grid-cols-2 gap-2 text-sm">
                      <div>Response Time Threshold:</div>
                      <div>{globalDegradedSettings.response_time} ms</div>

                      <div>Error Rate Threshold:</div>
                      <div>{globalDegradedSettings.error_rate}%</div>

                      <div>Consecutive Failures:</div>
                      <div>{globalDegradedSettings.consecutive_failures}</div>

                      <div>Status Codes:</div>
                      <div>
                        {globalDegradedSettings.status_codes?.length > 0
                          ? globalDegradedSettings.status_codes.join(', ')
                          : 'None'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        {isAdmin && (
          <div className="mt-6">
            <Button onClick={onEditClick}>
              <Settings className="h-4 w-4 mr-2" />
              Edit Configuration
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default MonitorSettingsTab;
