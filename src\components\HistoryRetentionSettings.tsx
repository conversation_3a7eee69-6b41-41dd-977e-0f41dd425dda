import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Save, RefreshCw, Info } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface HistoryRetentionSettingsProps {
  companyId?: string;
  companyName?: string;
  isSuperadmin: boolean;
}

const HistoryRetentionSettings: React.FC<HistoryRetentionSettingsProps> = ({
  companyId,
  companyName,
  isSuperadmin
}) => {
  const [globalRetentionDays, setGlobalRetentionDays] = useState<number>(7);
  const [companyRetentionDays, setCompanyRetentionDays] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch current settings
  useEffect(() => {
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        // Fetch global settings
        const { data: globalData, error: globalError } = await supabase
          .from('app_settings')
          .select('value')
          .eq('key', 'global_history_retention')
          .single();

        if (globalError && globalError.code !== 'PGRST116') {
          throw globalError;
        }

        if (globalData) {
          setGlobalRetentionDays(parseInt(globalData.value?.days) || 7);
        }

        // If we have a company ID, fetch company-specific settings
        if (companyId) {
          const { data: companyData, error: companyError } = await supabase
            .from('companies')
            .select('history_retention_days')
            .eq('id', companyId)
            .single();

          if (companyError) throw companyError;

          setCompanyRetentionDays(companyData.history_retention_days);
        }
      } catch (error) {
        console.error('Error fetching history retention settings:', error);
        toast({
          title: 'Error',
          description: 'Failed to load history retention settings.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isSuperadmin) {
      fetchSettings();
    }
  }, [companyId, isSuperadmin]);

  // Save global settings
  const saveGlobalSettings = async () => {
    if (!isSuperadmin) return;

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('app_settings')
        .upsert({
          key: 'global_history_retention',
          value: { days: globalRetentionDays },
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'key',
        });

      if (error) throw error;
      // Success - no toast needed
    } catch (error) {
      console.error('Error saving global history retention settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save global history retention settings.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Save company settings
  const saveCompanySettings = async () => {
    if (!isSuperadmin || !companyId) return;

    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('companies')
        .update({
          history_retention_days: companyRetentionDays,
        })
        .eq('id', companyId);

      if (error) throw error;
      // Success - no toast needed
    } catch (error) {
      console.error('Error saving company history retention settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save company history retention settings.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Reset company settings to use global default
  const resetToGlobal = () => {
    setCompanyRetentionDays(null);
  };

  if (!isSuperadmin) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Info className="h-5 w-5 mr-2 text-blue-500" />
          History Retention Settings
          <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
            Superadmin Only
          </Badge>
        </CardTitle>
        <CardDescription>
          Configure how long monitor history data is retained and displayed.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : (
          <>
            {/* Global Settings */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Global Default</h3>
              <p className="text-sm text-slate-500 dark:text-slate-400">
                This is the default retention period for all companies unless overridden.
              </p>
              <div className="flex items-end gap-4">
                <div className="flex-1">
                  <Label htmlFor="global-retention">
                    Global History Retention (days)
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button variant="ghost" size="icon" className="h-5 w-5 p-0 ml-1 text-slate-500 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200">
                          <AlertTriangle className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="right" align="start" className="max-w-sm p-4 z-50 text-left">
                        <div className="w-64 space-y-2">
                          <h4 className="font-medium text-sm">History Retention</h4>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            This setting controls how many days of monitor history data is available in the detail view.
                            Higher values provide more historical data but may impact performance.
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </Label>
                  <Input
                    id="global-retention"
                    type="number"
                    min="1"
                    max="365"
                    value={globalRetentionDays}
                    onChange={(e) => setGlobalRetentionDays(parseInt(e.target.value) || 7)}
                    className="mt-1"
                  />
                </div>
                <Button onClick={saveGlobalSettings} disabled={isSaving}>
                  {isSaving ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Global Setting
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Company-specific settings (only shown when editing a company) */}
            {companyId && (
              <div className="space-y-2 pt-4 border-t">
                <h3 className="text-lg font-medium">Company-Specific Setting</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400">
                  Override the global default for {companyName}.
                </p>
                <div className="flex items-end gap-4">
                  <div className="flex-1">
                    <Label htmlFor="company-retention">
                      {companyName} History Retention (days)
                    </Label>
                    <Input
                      id="company-retention"
                      type="number"
                      min="1"
                      max="365"
                      value={companyRetentionDays === null ? '' : companyRetentionDays}
                      onChange={(e) => setCompanyRetentionDays(e.target.value === '' ? null : parseInt(e.target.value))}
                      placeholder={`Using global default (${globalRetentionDays} days)`}
                      className="mt-1"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" onClick={resetToGlobal} disabled={isSaving || companyRetentionDays === null}>
                      Use Global Default
                    </Button>
                    <Button onClick={saveCompanySettings} disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Save Company Setting
                        </>
                      )}
                    </Button>
                  </div>
                </div>
                {companyRetentionDays === null && (
                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-2">
                    This company is using the global default of {globalRetentionDays} days.
                  </p>
                )}
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default HistoryRetentionSettings;
