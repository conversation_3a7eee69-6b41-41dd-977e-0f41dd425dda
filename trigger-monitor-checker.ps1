# PowerShell script to trigger the monitor-checker Edge Function
# Save this file to a location on your computer, e.g., C:\VUM\trigger-monitor-checker.ps1

# Configuration
$supabaseProjectRef = "axcfqilzeombkbzebeym"
$anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4Y2ZxaWx6ZW9tYmtiemViZXltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2MDEzMTYsImV4cCI6MjA1OTE3NzMxNn0.27w2cbxT9xQ2vd-aQmser6ozRUy9ibhn7mo2-VzN4w8"
$logFile = "C:\VUM\monitor-checker-logs.txt"

# Create log directory if it doesn't exist
$logDir = Split-Path -Path $logFile -Parent
if (-not (Test-Path -Path $logDir)) {
    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
}

# Function to write to log file
function Write-Log {
    param (
        [string]$Message
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timestamp - $Message" | Out-File -FilePath $logFile -Append
}

# Log start of script
Write-Log "Starting monitor checker trigger script"

try {
    # Prepare the request
    $url = "https://$supabaseProjectRef.functions.supabase.co/monitor-checker"
    $headers = @{
        "Authorization" = "Bearer $anonKey"
        "Content-Type" = "application/json"
    }
    
    # Make the request
    Write-Log "Sending request to $url"
    $response = Invoke-RestMethod -Uri $url -Method Post -Headers $headers -Body "{}"
    
    # Log the response
    Write-Log "Response received: Checked $($response.checksRun) monitors"
    if ($response.checksRun -gt 0) {
        foreach ($result in $response.results) {
            $status = if ($result.status) { "UP" } else { "DOWN" }
            Write-Log "Monitor '$($result.name)' is $status (Response time: $($result.response_time)ms)"
        }
    } else {
        Write-Log "No monitors were due for checking"
    }
} catch {
    # Log any errors
    Write-Log "Error: $_"
    Write-Log "Error details: $($_.Exception.Message)"
}

Write-Log "Script completed"
