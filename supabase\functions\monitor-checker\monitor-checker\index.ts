// Follow this setup guide to integrate the Deno runtime into your application:
// https://deno.com/manual/getting_started/setup_your_environment

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface Monitor {
  id: string
  name: string
  target: string
  type: 'http' | 'ping' | 'port' | 'keyword'
  interval: number
  timeout: number
  active: boolean
  company_id: string
  user_id: string
  created_at: string
}

interface CheckResult {
  monitor_id: string
  status: boolean
  response_time: number | null
  error_message: string | null
  timestamp: string
}

// Helper function to run tasks with concurrency limit
async function runWithConcurrency<T, R>(
  items: T[],
  taskFn: (item: T) => Promise<R>,
  concurrency = 5
): Promise<R[]> {
  const results: R[] = []
  const chunks: T[][] = []

  // Split items into chunks based on concurrency
  for (let i = 0; i < items.length; i += concurrency) {
    chunks.push(items.slice(i, i + concurrency))
  }

  // Process each chunk sequentially, but items within a chunk in parallel
  for (const chunk of chunks) {
    const chunkResults = await Promise.all(
      chunk.map(item => taskFn(item))
    )
    results.push(...chunkResults)
  }

  return results
}

// Perform a check on a monitor
async function performCheck(monitor: Monitor): Promise<CheckResult> {
  let status = false
  let responseTime: number | null = null
  let errorMessage: string | null = null

  const startTime = Date.now()

  try {
    switch (monitor.type) {
      case 'http':
        // HTTP check
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000)

        try {
          const response = await fetch(monitor.target, {
            method: 'GET',
            signal: controller.signal
          })

          clearTimeout(timeoutId)
          status = response.ok
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = `HTTP status: ${response.status}`
          }
        } catch (error) {
          clearTimeout(timeoutId)
          throw error
        }
        break

      case 'ping':
        // For ping, we'll use a simple HTTP check as a proxy since Deno edge functions
        // don't have direct ping capabilities
        try {
          const pingResponse = await fetch(monitor.target, {
            method: 'HEAD',
            headers: { 'Cache-Control': 'no-cache' }
          })
          status = pingResponse.ok
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = `Host unreachable, status: ${pingResponse.status}`
          }
        } catch (error) {
          throw new Error(`Ping failed: ${error.message}`)
        }
        break

      case 'port':
        // For port checks, we'll use fetch with the full URL
        // This is a simplification - in a real implementation you'd want to use
        // a proper TCP connection check
        try {
          // Extract host and port from target (format: host:port)
          const [host, port] = monitor.target.split(':')
          const protocol = port === '443' ? 'https' : 'http'
          const url = `${protocol}://${host}:${port}`

          const portResponse = await fetch(url, {
            method: 'HEAD',
            headers: { 'Cache-Control': 'no-cache' }
          })
          status = portResponse.ok
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = `Port check failed, status: ${portResponse.status}`
          }
        } catch (error) {
          throw new Error(`Port check failed: ${error.message}`)
        }
        break

      case 'keyword':
        // Keyword check - fetch the page and check for a keyword
        try {
          const response = await fetch(monitor.target, {
            method: 'GET',
            headers: { 'Cache-Control': 'no-cache' }
          })

          const text = await response.text()
          // Note: In a real implementation, you'd store the keyword to check for
          // in the monitor configuration. Here we're just checking if the response
          // contains any content.
          status = response.ok && text.length > 0
          responseTime = Date.now() - startTime

          if (!status) {
            errorMessage = 'Keyword not found or page error'
          }
        } catch (error) {
          throw new Error(`Keyword check failed: ${error.message}`)
        }
        break

      default:
        throw new Error(`Unsupported monitor type: ${monitor.type}`)
    }
  } catch (error) {
    status = false
    errorMessage = `Error: ${error.message}`
    console.error(`Check failed for monitor ${monitor.name}:`, error)
  }

  return {
    monitor_id: monitor.id,
    status,
    response_time: responseTime,
    error_message: errorMessage,
    timestamp: new Date().toISOString()
  }
}

// Check if a notification should be sent (status changed)
async function checkStatusChange(
  supabase: any,
  monitor: Monitor,
  currentStatus: boolean
): Promise<boolean> {
  // Get the previous check
  const { data: previousChecks } = await supabase
    .from('monitor_history')
    .select('status')
    .eq('monitor_id', monitor.id)
    .order('timestamp', { ascending: false })
    .limit(2)

  // If this is the first check or status changed, return true
  if (previousChecks.length < 2 || previousChecks[1].status !== currentStatus) {
    return true
  }

  return false
}

// Send a notification
async function sendNotification(
  supabase: any,
  monitor: Monitor,
  status: boolean
): Promise<void> {
  // In a real implementation, you'd send an email, SMS, webhook, etc.
  console.log(`Notification for monitor ${monitor.name}: Status is now ${status ? 'UP' : 'DOWN'}`)

  // For now, we'll just log the notification in a notifications table
  await supabase
    .from('notifications')
    .insert({
      monitor_id: monitor.id,
      user_id: monitor.user_id,
      company_id: monitor.company_id,
      message: `Monitor ${monitor.name} is now ${status ? 'UP' : 'DOWN'}`,
      type: status ? 'up' : 'down',
      read: false,
      created_at: new Date().toISOString()
    })
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse request body if present
    let requestBody = {};
    if (req.method === 'POST') {
      try {
        requestBody = await req.json();
      } catch (e) {
        // If parsing fails, assume empty body
        console.log('Failed to parse request body, assuming empty');
      }
    }
    // Create Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? ''
    const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    const supabase = createClient(supabaseUrl, supabaseKey)

    // Check if a specific monitor ID was provided
    const specificMonitorId = requestBody?.monitorId;

    // Get monitors to check
    let monitorsQuery = supabase
      .from('monitors')
      .select('*')
      .eq('active', true);

    // If a specific monitor ID was provided, only check that one
    if (specificMonitorId) {
      monitorsQuery = monitorsQuery.eq('id', specificMonitorId);
    }

    const { data: monitors, error: monitorsError } = await monitorsQuery

    if (monitorsError) {
      throw new Error(`Failed to fetch monitors: ${monitorsError.message}`)
    }

    const now = Date.now()
    const checksToRun: Monitor[] = []

    // Determine which monitors need to be checked
    for (const monitor of monitors) {
      const { data: lastCheck } = await supabase
        .from('monitor_history')
        .select('timestamp')
        .eq('monitor_id', monitor.id)
        .order('timestamp', { ascending: false })
        .limit(1)

      const lastCheckTime = lastCheck?.[0]?.timestamp
        ? new Date(lastCheck[0].timestamp).getTime()
        : 0

      const intervalMs = monitor.interval * 60 * 1000

      if (now - lastCheckTime >= intervalMs) {
        checksToRun.push(monitor)
      }
    }

    console.log(`Running checks for ${checksToRun.length} monitors`)

    // Run checks with concurrency control
    const results = await runWithConcurrency(checksToRun, async (monitor) => {
      const checkResult = await performCheck(monitor)

      // Save the check result
      await supabase
        .from('monitor_history')
        .insert(checkResult)

      // Check if status changed and send notification if needed
      const statusChanged = await checkStatusChange(supabase, monitor, checkResult.status)
      if (statusChanged) {
        await sendNotification(supabase, monitor, checkResult.status)
      }

      return {
        monitor_id: monitor.id,
        name: monitor.name,
        status: checkResult.status,
        response_time: checkResult.response_time
      }
    }, 5)

    return new Response(
      JSON.stringify({
        success: true,
        checksRun: results.length,
        results
      }),
      {
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error('Error in monitor checker:', error)

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})
