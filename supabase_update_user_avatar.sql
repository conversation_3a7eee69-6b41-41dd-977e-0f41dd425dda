-- This script updates your user metadata with the avatar URL
-- Run this in the Supabase SQL Editor

-- Update the user's metadata with the avatar URL
UPDATE auth.users
SET raw_user_meta_data = raw_user_meta_data || 
  jsonb_build_object('avatar_url', 'https://axcfqilzeombkbzebeym.supabase.co/storage/v1/object/public/avatars/test-avatar.png')
WHERE id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';

-- Verify the update
SELECT id, email, raw_user_meta_data
FROM auth.users
WHERE id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';
