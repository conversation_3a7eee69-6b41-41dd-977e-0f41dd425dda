// <PERSON><PERSON>t to force a monitor check by updating the last check timestamp
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to force a check by updating the timestamp
async function forceCheck(monitorId) {
  try {
    // If no monitor ID is provided, update all active monitors
    if (!monitorId) {
      // Get all active monitors
      const { data: monitors, error: monitorsError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('active', true);

      if (monitorsError) {
        throw new Error(`Failed to get monitors: ${monitorsError.message}`);
      }

      if (!monitors || monitors.length === 0) {
        console.log('No active monitors found');
        return { success: true, monitorsUpdated: 0 };
      }

      console.log(`Found ${monitors.length} active monitors`);

      // Update each monitor's last check timestamp
      let updatedCount = 0;
      for (const monitor of monitors) {
        try {
          // Check if monitor is due before updating
          const isDueBefore = await isMonitorDue(monitor.id, monitor.interval);
          console.log(`Monitor ${monitor.name} is ${isDueBefore ? 'due' : 'not due'} for checking before update`);

          // Update the timestamp
          await updateMonitorTimestamp(monitor.id, monitor.name, monitor.interval);
          updatedCount++;

          // Check if monitor is due after updating
          const isDueAfter = await isMonitorDue(monitor.id, monitor.interval);
          console.log(`Monitor ${monitor.name} is ${isDueAfter ? 'due' : 'not due'} for checking after update`);

          if (!isDueAfter) {
            console.warn(`Warning: Monitor ${monitor.name} is still not due for checking after update!`);
          }
        } catch (error) {
          console.error(`Error updating monitor ${monitor.name}: ${error.message}`);
        }
      }

      console.log(`Successfully updated ${updatedCount} monitors`);
      return { success: true, monitorsUpdated: updatedCount };
    } else {
      // Get the specific monitor
      const { data: monitor, error: monitorError } = await supabase
        .from('monitors')
        .select('id, name, interval')
        .eq('id', monitorId)
        .single();

      if (monitorError) {
        throw new Error(`Failed to get monitor: ${monitorError.message}`);
      }

      if (!monitor) {
        throw new Error(`Monitor with ID ${monitorId} not found`);
      }

      // Update the monitor's last check timestamp
      await updateMonitorTimestamp(monitor.id, monitor.name, monitor.interval);

      console.log(`Successfully updated monitor ${monitor.name}`);
      return { success: true, monitorsUpdated: 1 };
    }
  } catch (error) {
    console.error(`Error forcing check: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Helper function to check if a monitor is due for checking
async function isMonitorDue(monitorId, interval) {
  try {
    // Get the most recent check for this monitor
    const { data: lastCheck, error: lastCheckError } = await supabase
      .from('monitor_history')
      .select('timestamp')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(1);

    if (lastCheckError) {
      throw new Error(`Failed to get last check: ${lastCheckError.message}`);
    }

    if (!lastCheck || lastCheck.length === 0) {
      // No previous checks, so it's due
      return true;
    }

    // Calculate if it's due
    const lastCheckTime = new Date(lastCheck[0].timestamp).getTime();
    const now = new Date().getTime();
    const intervalMs = interval * 60 * 1000;
    const timeSinceLastCheck = now - lastCheckTime;

    console.log(`Monitor ${monitorId}: interval=${interval}min, last check=${Math.floor(timeSinceLastCheck/60000)}min ago`);

    return timeSinceLastCheck >= intervalMs;
  } catch (error) {
    console.error(`Error checking if monitor is due: ${error.message}`);
    return false;
  }
}

// Helper function to update a monitor's last check timestamp
async function updateMonitorTimestamp(monitorId, monitorName, interval) {
  try {
    // Get the most recent check for this monitor
    const { data: lastCheck, error: lastCheckError } = await supabase
      .from('monitor_history')
      .select('id, timestamp')
      .eq('monitor_id', monitorId)
      .order('timestamp', { ascending: false })
      .limit(1);

    if (lastCheckError) {
      throw new Error(`Failed to get last check: ${lastCheckError.message}`);
    }

    if (!lastCheck || lastCheck.length === 0) {
      console.log(`No previous checks found for monitor ${monitorName}. The monitor service will check it automatically.`);
      return;
    }

    // Calculate a new timestamp that will make the monitor due for checking
    // Set it to (interval * 2) minutes ago to ensure it's checked immediately
    // This is more aggressive to ensure it gets picked up
    const newTimestamp = new Date();
    newTimestamp.setMinutes(newTimestamp.getMinutes() - (interval * 2));
    console.log(`Setting timestamp to ${interval * 2} minutes ago: ${newTimestamp.toISOString()}`);

    // Get the current timestamp before updating
    console.log(`Current timestamp for monitor ${monitorName}: ${lastCheck[0].timestamp}`);

    // Update the timestamp of the most recent check
    const { data: updateData, error: updateError } = await supabase
      .from('monitor_history')
      .update({ timestamp: newTimestamp.toISOString() })
      .eq('id', lastCheck[0].id)
      .select();

    if (updateError) {
      throw new Error(`Failed to update timestamp: ${updateError.message}`);
    }

    console.log(`Updated timestamp for monitor ${monitorName} from ${lastCheck[0].timestamp} to ${newTimestamp.toISOString()}`);

    // Verify the update
    const { data: verifyData, error: verifyError } = await supabase
      .from('monitor_history')
      .select('timestamp')
      .eq('id', lastCheck[0].id)
      .single();

    if (verifyError) {
      console.error(`Error verifying update: ${verifyError.message}`);
    } else {
      console.log(`Verified timestamp for monitor ${monitorName} is now: ${verifyData.timestamp}`);
    }
  } catch (error) {
    console.error(`Error updating timestamp for monitor ${monitorName}: ${error.message}`);
    throw error;
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const monitorId = args.find(arg => arg.startsWith('--id='))?.split('=')[1];

// Run the function
forceCheck(monitorId)
  .then(result => {
    log(`Force check completed: ${JSON.stringify(result)}`, 'INFO');
    process.exit(0);
  })
  .catch(error => {
    log(`Error during force check: ${error.message}`, 'ERROR');
    process.exit(1);
  });
