/**
 * VUM - Vurbis Uptime Monitor
 * Dashboard Scripts
 */

document.addEventListener('DOMContentLoaded', async function() {
    // Toggle sidebar
    const menuToggle = document.getElementById('menu-toggle');
    const appContainer = document.querySelector('.app-container');

    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            appContainer.classList.toggle('sidebar-collapsed');
        });
    }

    // Initialize API
    try {
        await window.vumAPI.initializeAPI();

        // Load dashboard data
        await loadDashboardData();

        // Initialize charts
        initializeCharts();
    } catch (error) {
        console.error('Error initializing API:', error);
        // Show error message to user
        alert('Error connecting to the monitor service. Please check your connection and try again.');
    }

    // Initialize responsive behavior
    initializeResponsive();
});

/**
 * Initialize responsive behavior
 */
function initializeResponsive() {
    const sidebar = document.querySelector('.sidebar');
    const menuToggle = document.getElementById('menu-toggle');

    // On mobile, clicking outside the sidebar closes it
    document.addEventListener('click', function(event) {
        const isMobile = window.innerWidth <= 768;
        const isClickInsideSidebar = sidebar.contains(event.target);
        const isClickOnMenuToggle = menuToggle.contains(event.target);

        if (isMobile && !isClickInsideSidebar && !isClickOnMenuToggle && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
    });

    // On mobile, clicking the menu toggle shows the sidebar
    if (menuToggle) {
        menuToggle.addEventListener('click', function(event) {
            const isMobile = window.innerWidth <= 768;

            if (isMobile) {
                event.stopPropagation();
                sidebar.classList.toggle('show');
            }
        });
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        const isMobile = window.innerWidth <= 768;

        if (!isMobile && sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
    });
}

/**
 * Load dashboard data from API
 */
async function loadDashboardData() {
    try {
        // Get monitor statistics
        const stats = await window.vumAPI.getMonitorStats();

        // Update status cards
        document.querySelector('.status-card:nth-child(1) .status-card-content h3').textContent = stats.up;
        document.querySelector('.status-card:nth-child(2) .status-card-content h3').textContent = stats.down;
        document.querySelector('.status-card:nth-child(3) .status-card-content h3').textContent = stats.uptime + '%';
        document.querySelector('.status-card:nth-child(4) .status-card-content h3').textContent = stats.avgResponseTime + 'ms';

        // Get all monitors
        const monitors = await window.vumAPI.getMonitors();

        // Update monitors table
        updateMonitorsTable(monitors);

        // Get recent history for charts
        const recentHistory = await window.vumAPI.getRecentHistory(24);

        // Prepare data for charts
        prepareChartData(monitors, recentHistory);

        // Get notifications
        const notifications = await window.vumAPI.getNotifications(5);

        // Update incidents list
        updateIncidentsList(notifications);

        console.log('Dashboard data loaded successfully');
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

/**
 * Update monitors table
 */
function updateMonitorsTable(monitors) {
    const tableBody = document.querySelector('.monitors-table tbody');

    // Clear existing rows
    tableBody.innerHTML = '';

    // Add new rows
    monitors.forEach(monitor => {
        // Get last check time
        const lastCheckTime = monitor.last_check ? new Date(monitor.last_check) : null;
        const timeAgo = lastCheckTime ? getTimeAgo(lastCheckTime) : 'Never';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><span class="status-badge ${monitor.status ? 'up' : 'down'}"><i class="fas fa-${monitor.status ? 'check' : 'times'}-circle"></i> ${monitor.status ? 'Up' : 'Down'}</span></td>
            <td>${monitor.name}</td>
            <td>${monitor.target}</td>
            <td>${monitor.type}</td>
            <td>${timeAgo}</td>
            <td>${monitor.response_time ? monitor.response_time + 'ms' : '--'}</td>
            <td>${monitor.uptime ? monitor.uptime + '%' : '--'}</td>
            <td>
                <div class="table-actions">
                    <button class="btn-icon" onclick="viewMonitor('${monitor.id}')"><i class="fas fa-eye"></i></button>
                    <button class="btn-icon" onclick="editMonitor('${monitor.id}')"><i class="fas fa-edit"></i></button>
                    <button class="btn-icon" onclick="deleteMonitor('${monitor.id}')"><i class="fas fa-trash"></i></button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

/**
 * Update incidents list
 */
function updateIncidentsList(notifications) {
    const incidentsList = document.querySelector('.incidents-list');

    // Clear existing incidents
    incidentsList.innerHTML = '';

    // Add new incidents
    notifications.forEach(notification => {
        const isDown = notification.type === 'down';
        const timeAgo = getTimeAgo(new Date(notification.created_at));

        const incidentCard = document.createElement('div');
        incidentCard.className = 'incident-card';
        incidentCard.innerHTML = `
            <div class="incident-icon ${isDown ? 'down' : 'up'}">
                <i class="fas fa-${isDown ? 'times' : 'check'}-circle"></i>
            </div>
            <div class="incident-content">
                <h4>${notification.message}</h4>
                <p>${isDown ? 'The monitor is currently unreachable. Our team is investigating the issue.' : 'The monitor is back online. The issue has been resolved.'}</p>
                <div class="incident-meta">
                    <span><i class="fas fa-clock"></i> ${timeAgo}</span>
                    <span><i class="fas fa-server"></i> ${notification.monitors?.name || 'Unknown'}</span>
                </div>
            </div>
            <div class="incident-status">
                <span class="status-badge ${isDown ? 'down' : 'up'}">${isDown ? 'Ongoing' : 'Resolved'}</span>
            </div>
        `;

        incidentsList.appendChild(incidentCard);
    });

    // If no incidents, show a message
    if (notifications.length === 0) {
        const noIncidents = document.createElement('div');
        noIncidents.className = 'no-incidents';
        noIncidents.innerHTML = '<p>No recent incidents.</p>';
        incidentsList.appendChild(noIncidents);
    }
}

/**
 * Prepare data for charts
 */
function prepareChartData(monitors, history) {
    // Group history by monitor and time
    const monitorData = {};

    // Initialize data for each monitor
    monitors.forEach(monitor => {
        monitorData[monitor.id] = {
            name: monitor.name,
            responseTimes: [],
            uptime: 0,
            totalChecks: 0
        };
    });

    // Process history data
    history.forEach(record => {
        if (monitorData[record.monitor_id]) {
            // Add response time data point
            if (record.response_time !== null) {
                monitorData[record.monitor_id].responseTimes.push({
                    time: new Date(record.timestamp),
                    value: record.response_time
                });
            }

            // Update uptime calculation
            monitorData[record.monitor_id].totalChecks++;
            if (record.status) {
                monitorData[record.monitor_id].uptime++;
            }
        }
    });

    // Calculate uptime percentages
    Object.values(monitorData).forEach(data => {
        if (data.totalChecks > 0) {
            data.uptimePercentage = (data.uptime / data.totalChecks) * 100;
        } else {
            data.uptimePercentage = 0;
        }
    });

    // Store the prepared data for charts
    window.chartData = monitorData;
}

/**
 * Get time ago string
 */
function getTimeAgo(date) {
    const seconds = Math.floor((new Date() - date) / 1000);

    let interval = Math.floor(seconds / 31536000);
    if (interval >= 1) return interval + ' year' + (interval === 1 ? '' : 's') + ' ago';

    interval = Math.floor(seconds / 2592000);
    if (interval >= 1) return interval + ' month' + (interval === 1 ? '' : 's') + ' ago';

    interval = Math.floor(seconds / 86400);
    if (interval >= 1) return interval + ' day' + (interval === 1 ? '' : 's') + ' ago';

    interval = Math.floor(seconds / 3600);
    if (interval >= 1) return interval + ' hour' + (interval === 1 ? '' : 's') + ' ago';

    interval = Math.floor(seconds / 60);
    if (interval >= 1) return interval + ' minute' + (interval === 1 ? '' : 's') + ' ago';

    if (seconds < 10) return 'just now';

    return Math.floor(seconds) + ' second' + (Math.floor(seconds) === 1 ? '' : 's') + ' ago';
}

/**
 * View monitor details
 */
function viewMonitor(id) {
    alert('View monitor details: ' + id);
    // TODO: Implement monitor details view
}

/**
 * Edit monitor
 */
function editMonitor(id) {
    alert('Edit monitor: ' + id);
    // TODO: Implement monitor editing
}

/**
 * Delete monitor
 */
function deleteMonitor(id) {
    if (confirm('Are you sure you want to delete this monitor?')) {
        window.vumAPI.deleteMonitor(id)
            .then(success => {
                if (success) {
                    alert('Monitor deleted successfully');
                    loadDashboardData(); // Reload dashboard data
                } else {
                    alert('Failed to delete monitor');
                }
            })
            .catch(error => {
                console.error('Error deleting monitor:', error);
                alert('Error deleting monitor: ' + error.message);
            });
    }
}

/**
 * Initialize charts
 */
function initializeCharts() {
    // Check if we have chart data
    if (!window.chartData) {
        console.warn('No chart data available');
        return;
    }

    // Get monitors data
    const monitorData = window.chartData;
    const monitorIds = Object.keys(monitorData);

    // If no monitors, don't initialize charts
    if (monitorIds.length === 0) {
        console.warn('No monitors available for charts');
        return;
    }

    // Response Time Chart
    const responseTimeCtx = document.getElementById('response-time-chart');
    if (responseTimeCtx) {
        // Prepare datasets for response time chart
        const datasets = [];
        const colors = ['#0056b3', '#28a745', '#ff6b00', '#6f42c1', '#fd7e14'];

        // Get time labels (last 24 hours)
        const timeLabels = generateTimeLabels(24);

        // Create a dataset for each monitor (up to 5)
        monitorIds.slice(0, 5).forEach((monitorId, index) => {
            const monitor = monitorData[monitorId];

            // Skip if no response times
            if (monitor.responseTimes.length === 0) return;

            // Group response times by hour
            const hourlyData = new Array(24).fill(null);

            monitor.responseTimes.forEach(point => {
                const hour = point.time.getHours();
                if (hourlyData[hour] === null) {
                    hourlyData[hour] = [];
                }
                hourlyData[hour].push(point.value);
            });

            // Calculate average for each hour
            const averageData = hourlyData.map(values => {
                if (values === null || values.length === 0) return null;
                return Math.round(values.reduce((sum, val) => sum + val, 0) / values.length);
            });

            // Add dataset
            datasets.push({
                label: monitor.name,
                data: averageData,
                borderColor: colors[index % colors.length],
                backgroundColor: `rgba(${hexToRgb(colors[index % colors.length])}, 0.1)`,
                borderWidth: 2,
                tension: 0.4,
                pointRadius: 3,
                pointBackgroundColor: colors[index % colors.length],
                spanGaps: true
            });
        });

        // If no datasets, add a placeholder
        if (datasets.length === 0) {
            datasets.push({
                label: 'No Data',
                data: new Array(24).fill(null),
                borderColor: '#6c757d',
                backgroundColor: 'rgba(108, 117, 125, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                pointRadius: 3,
                pointBackgroundColor: '#6c757d'
            });
        }

        const responseTimeChart = new Chart(responseTimeCtx, {
            type: 'line',
            data: {
                labels: timeLabels,
                datasets: datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                        callbacks: {
                            label: function(context) {
                                if (context.raw === null) return context.dataset.label + ': No data';
                                return context.dataset.label + ': ' + context.raw + 'ms';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        }
                    }
                }
            }
        });
    }

    // Uptime Chart
    const uptimeCtx = document.getElementById('uptime-chart');
    if (uptimeCtx) {
        // Prepare data for uptime chart
        const labels = [];
        const data = [];
        const colors = [];

        // Add data for each monitor
        monitorIds.forEach(monitorId => {
            const monitor = monitorData[monitorId];
            labels.push(monitor.name);
            data.push(parseFloat(monitor.uptimePercentage.toFixed(1)));

            // Set color based on uptime percentage
            if (monitor.uptimePercentage >= 99) {
                colors.push('#28a745'); // Green for good uptime
            } else if (monitor.uptimePercentage >= 95) {
                colors.push('#ffc107'); // Yellow for moderate uptime
            } else {
                colors.push('#dc3545'); // Red for poor uptime
            }
        });

        // If no data, add a placeholder
        if (labels.length === 0) {
            labels.push('No Data');
            data.push(0);
            colors.push('#6c757d');
        }

        const uptimeChart = new Chart(uptimeCtx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Uptime (%)',
                        data: data,
                        backgroundColor: colors,
                        borderWidth: 0,
                        borderRadius: 4
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.raw + '%';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Uptime (%)'
                        }
                    }
                }
            }
        });
    }
}

/**
 * Convert hex color to RGB
 */
function hexToRgb(hex) {
    // Remove # if present
    hex = hex.replace('#', '');

    // Parse the hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
}

/**
 * Generate random data for charts
 */
function generateRandomData(count, min, max) {
    const data = [];
    for (let i = 0; i < count; i++) {
        data.push(Math.floor(Math.random() * (max - min + 1)) + min);
    }
    return data;
}

/**
 * Generate time labels for charts
 */
function generateTimeLabels(count) {
    const labels = [];
    const now = new Date();

    for (let i = count - 1; i >= 0; i--) {
        const time = new Date(now);
        time.setHours(now.getHours() - i);
        labels.push(time.getHours() + ':00');
    }

    return labels;
}
