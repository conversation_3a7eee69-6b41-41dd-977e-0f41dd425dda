import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyRoles } from './use-company-roles';

// Define types for user operations
export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at?: string;
}

export interface UserWithRoles extends User {
  companies: {
    id?: string;
    company_id: string;
    company_name: string;
    role_type: string;
  }[];
  is_superadmin: boolean;
}

export interface CreateUserData {
  email: string;
  password: string;
  full_name?: string;
}

export interface UpdateUserData {
  email?: string;
  full_name?: string;
  avatar_url?: string;
}

export function useUsers() {
  const { user } = useAuth();
  const { isGlobalSuperadmin } = useCompanyRoles();
  const queryClient = useQueryClient();

  // Get all users (for superadmins only)
  const getUsers = async (): Promise<UserWithRoles[]> => {
    try {
      // Use the new function to get all users with their roles and company memberships
      const { data, error } = await supabase.rpc('get_all_users_with_memberships');

      if (error) {
        console.error('Error fetching users with memberships:', error);
        throw error;
      }

      console.log('Users fetched with memberships:', data);

      // Transform the data to match our component's expected format
      const usersWithRoles: UserWithRoles[] = data.map((user: any) => ({
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        avatar_url: user.avatar_url,
        created_at: user.created_at,
        companies: user.companies || [],
        is_superadmin: user.is_superadmin
      }));

      return usersWithRoles;
    } catch (error) {
      console.error('Error in getUsers:', error);
      throw error;
    }
  };

  // Get a single user by ID
  const getUserById = async (userId: string): Promise<UserWithRoles | null> => {
    try {
      // Use the new function to get a single user with their roles and company memberships
      const { data, error } = await supabase.rpc('get_user_with_memberships', {
        p_user_id: userId
      });

      if (error) {
        console.error('Error fetching user with memberships:', error);
        throw error;
      }

      // If no user was found
      if (!data || data.length === 0) {
        return null;
      }

      const user = data[0];

      // Transform the data to match our component's expected format
      return {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        avatar_url: user.avatar_url,
        created_at: user.created_at,
        companies: user.companies || [],
        is_superadmin: user.is_superadmin
      };
    } catch (error) {
      console.error('Error in getUserById:', error);
      throw error;
    }
  };

  // Create a new user
  const createUser = async (data: CreateUserData): Promise<User> => {
    // Check if user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can create users');
    }

    // Create user in Auth - RLS policies will ensure only superadmins can create users
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: data.email,
      password: data.password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        full_name: data.full_name || ''
      }
    });

    if (authError) throw authError;

    // The user should be automatically added to the users table via trigger
    // But let's make sure it exists
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, avatar_url')
      .eq('id', authData.user.id)
      .single();

    if (userError) {
      // If user doesn't exist in the users table, create it
      const { data: insertData, error: insertError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: data.email,
          full_name: data.full_name || ''
        })
        .select('id, email, full_name, avatar_url')
        .single();

      if (insertError) throw insertError;
      return insertData;
    }

    return userData;
  };

  // Update a user
  const updateUser = async (userId: string, data: UpdateUserData): Promise<User> => {
    // Check if user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can update users');
    }

    // Update user in Auth if email is changing
    if (data.email) {
      const { error: authError } = await supabase.auth.admin.updateUserById(
        userId,
        { email: data.email }
      );

      if (authError) throw authError;
    }

    // Update user metadata if full_name or avatar_url is changing
    if (data.full_name || data.avatar_url) {
      const { error: metadataError } = await supabase.auth.admin.updateUserById(
        userId,
        {
          user_metadata: {
            full_name: data.full_name,
            avatar_url: data.avatar_url
          }
        }
      );

      if (metadataError) throw metadataError;
    }

    // Update user in the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .update({
        email: data.email,
        full_name: data.full_name,
        avatar_url: data.avatar_url
      })
      .eq('id', userId)
      .select('id, email, full_name, avatar_url')
      .single();

    if (userError) throw userError;

    return userData;
  };

  // Delete a user
  const deleteUser = async (userId: string): Promise<void> => {
    // Check if user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can delete users');
    }

    // Delete user from Auth (this should cascade to the users table)
    const { error } = await supabase.auth.admin.deleteUser(userId);

    if (error) throw error;
  };

  // Assign a user to a company
  const assignUserToCompany = async (
    userId: string,
    companyId: string,
    role_type: string
  ): Promise<void> => {
    // Check if user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can assign users to companies');
    }

    // Check if the user is already a member of the company
    const { data: existingMember, error: checkError } = await supabase
      .from('company_members')
      .select('id')
      .eq('user_id', userId)
      .eq('company_id', companyId)
      .maybeSingle();

    if (checkError) throw checkError;

    if (existingMember) {
      // Update existing membership
      const { error: updateError } = await supabase
        .from('company_members')
        .update({ role_type })
        .eq('id', existingMember.id);

      if (updateError) throw updateError;
    } else {
      // Create new membership
      const { error: insertError } = await supabase
        .from('company_members')
        .insert({
          user_id: userId,
          company_id: companyId,
          role_type
        });

      if (insertError) throw insertError;
    }
  };

  // Remove a user from a company
  const removeUserFromCompany = async (
    userId: string,
    companyId: string
  ): Promise<void> => {
    // Check if user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can remove users from companies');
    }

    // Delete the company membership
    const { error } = await supabase
      .from('company_members')
      .delete()
      .eq('user_id', userId)
      .eq('company_id', companyId);

    if (error) throw error;
  };

  // Set or revoke superadmin status
  const setSuperadminStatus = async (
    userId: string,
    isSuperadmin: boolean
  ): Promise<void> => {
    // Check if current user is superadmin
    const hasSuperadminAccess = await isGlobalSuperadmin();
    if (!hasSuperadminAccess) {
      throw new Error('Unauthorized: Only superadmins can manage superadmin status');
    }

    if (isSuperadmin) {
      // Grant superadmin role
      const { error } = await supabase
        .from('user_roles')
        .insert({
          user_id: userId,
          role_type: 'superadmin'
        });

      if (error && error.code !== '23505') { // Ignore duplicate key errors
        throw error;
      }
    } else {
      // Revoke superadmin role
      const { error } = await supabase
        .from('user_roles')
        .delete()
        .eq('user_id', userId)
        .eq('role_type', 'superadmin');

      if (error) throw error;
    }
  };

  // React Query hooks
  const useGetUsersQuery = () => {
    return useQuery({
      queryKey: ['users'],
      queryFn: getUsers,
      enabled: !!user,
    });
  };

  const useGetUserByIdQuery = (userId: string) => {
    return useQuery({
      queryKey: ['users', userId],
      queryFn: () => getUserById(userId),
      enabled: !!userId && !!user,
    });
  };

  const useCreateUserMutation = () => {
    return useMutation({
      mutationFn: createUser,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        toast({
          title: 'User created',
          description: 'The user has been created successfully.',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error creating user',
          description: error.message || 'An error occurred while creating the user.',
          variant: 'destructive',
        });
      },
    });
  };

  const useUpdateUserMutation = () => {
    return useMutation({
      mutationFn: ({ userId, data }: { userId: string; data: UpdateUserData }) =>
        updateUser(userId, data),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        queryClient.invalidateQueries({ queryKey: ['users', variables.userId] });
        toast({
          title: 'User updated',
          description: 'The user has been updated successfully.',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error updating user',
          description: error.message || 'An error occurred while updating the user.',
          variant: 'destructive',
        });
      },
    });
  };

  const useDeleteUserMutation = () => {
    return useMutation({
      mutationFn: deleteUser,
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        toast({
          title: 'User deleted',
          description: 'The user has been deleted successfully.',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error deleting user',
          description: error.message || 'An error occurred while deleting the user.',
          variant: 'destructive',
        });
      },
    });
  };

  const useAssignUserToCompanyMutation = () => {
    return useMutation({
      mutationFn: ({ userId, companyId, role }: { userId: string; companyId: string; role: string }) =>
        assignUserToCompany(userId, companyId, role === 'admin' ? 'admin' : 'user'),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        queryClient.invalidateQueries({ queryKey: ['users', variables.userId] });
        toast({
          title: 'User assigned',
          description: 'The user has been assigned to the company successfully.',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error assigning user',
          description: error.message || 'An error occurred while assigning the user to the company.',
          variant: 'destructive',
        });
      },
    });
  };

  const useRemoveUserFromCompanyMutation = () => {
    return useMutation({
      mutationFn: ({ userId, companyId }: { userId: string; companyId: string }) =>
        removeUserFromCompany(userId, companyId),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        queryClient.invalidateQueries({ queryKey: ['users', variables.userId] });
        toast({
          title: 'User removed',
          description: 'The user has been removed from the company successfully.',
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error removing user',
          description: error.message || 'An error occurred while removing the user from the company.',
          variant: 'destructive',
        });
      },
    });
  };

  const useSetSuperadminStatusMutation = () => {
    return useMutation({
      mutationFn: ({ userId, isSuperadmin }: { userId: string; isSuperadmin: boolean }) =>
        setSuperadminStatus(userId, isSuperadmin),
      onSuccess: (_, variables) => {
        queryClient.invalidateQueries({ queryKey: ['users'] });
        queryClient.invalidateQueries({ queryKey: ['users', variables.userId] });
        queryClient.invalidateQueries({ queryKey: ['global-superadmins'] });
        toast({
          title: variables.isSuperadmin ? 'Superadmin granted' : 'Superadmin revoked',
          description: `Superadmin privileges have been ${variables.isSuperadmin ? 'granted' : 'revoked'} successfully.`,
        });
      },
      onError: (error: any) => {
        toast({
          title: 'Error updating superadmin status',
          description: error.message || 'An error occurred while updating superadmin status.',
          variant: 'destructive',
        });
      },
    });
  };

  return {
    // Direct functions
    getUsers,
    getUserById,
    createUser,
    updateUser,
    deleteUser,
    assignUserToCompany,
    removeUserFromCompany,
    setSuperadminStatus,

    // React Query hooks
    useGetUsersQuery,
    useGetUserByIdQuery,
    useCreateUserMutation,
    useUpdateUserMutation,
    useDeleteUserMutation,
    useAssignUserToCompanyMutation,
    useRemoveUserFromCompanyMutation,
    useSetSuperadminStatusMutation,
  };
}
