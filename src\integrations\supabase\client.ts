// Supabase client configuration
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

/**
 * Centralized Supabase client for the application
 *
 * This is the only instance of the Supabase client that should be used throughout the application.
 * Import it like this:
 * import { supabase } from "@/integrations/supabase/client";
 */

// Access environment variables directly from import.meta.env
// No fallbacks - environment variables MUST be set
const url = import.meta.env.VITE_SUPABASE_URL;
const key = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Check if environment variables are set
if (!import.meta.env.VITE_SUPABASE_URL || !import.meta.env.VITE_SUPABASE_ANON_KEY) {
  console.error(
    'ERROR: Supabase credentials are not set. ' +
    'Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env.local file.'
  );
  throw new Error('Supabase credentials are not set');
}

// Create the Supabase client
export const supabase = createClient<Database>(url, key);