-- This script creates a function to get users by IDs
-- Run this in the Supabase SQL Editor

CREATE OR REPLACE FUNCTION get_users_by_ids(user_ids UUID[])
RETURNS SETOF json AS $$
DECLARE
  user_record RECORD;
  user_json json;
BEGIN
  FOR user_record IN
    SELECT 
      au.id,
      au.email,
      au.raw_user_meta_data->>'full_name' as full_name,
      au.raw_user_meta_data->>'avatar_url' as avatar_url
    FROM 
      auth.users au
    WHERE 
      au.id = ANY(user_ids)
  LOOP
    user_json := json_build_object(
      'id', user_record.id,
      'email', user_record.email,
      'full_name', COALESCE(user_record.full_name, ''),
      'avatar_url', COALESCE(user_record.avatar_url, '')
    );
    RETURN NEXT user_json;
  END LOOP;
  
  RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
