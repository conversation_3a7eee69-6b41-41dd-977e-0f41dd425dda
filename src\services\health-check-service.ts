import { supabase } from '@/integrations/supabase/client';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy';
  lastCheck?: string;
  responseTime?: number;
  error?: string;
  details?: any;
}

export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  services: HealthCheckResult[];
  lastUpdated: string;
}

/**
 * Check if the monitor checker edge function is responsive
 */
export async function checkMonitorCheckerHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Try to call the monitor checker function with a health check parameter
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/monitor-checker`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ healthCheck: true }),
      signal: AbortSignal.timeout(10000) // 10 second timeout
    });

    const responseTime = Date.now() - startTime;

    if (response.ok) {
      const data = await response.json();
      return {
        service: 'Monitor Checker',
        status: 'healthy',
        responseTime,
        lastCheck: new Date().toISOString(),
        details: data
      };
    } else {
      return {
        service: 'Monitor Checker',
        status: 'unhealthy',
        responseTime,
        lastCheck: new Date().toISOString(),
        error: `HTTP ${response.status}: ${response.statusText}`
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'Monitor Checker',
      status: 'unhealthy',
      responseTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check if recent monitor checks are happening
 */
export async function checkRecentMonitorActivity(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Check for monitor history entries in the last 10 minutes
    const { data, error } = await supabase
      .from('monitor_history')
      .select('timestamp, monitor_id')
      .gte('timestamp', new Date(Date.now() - 10 * 60 * 1000).toISOString())
      .order('timestamp', { ascending: false })
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        service: 'Monitor Activity',
        status: 'unhealthy',
        responseTime,
        lastCheck: new Date().toISOString(),
        error: error.message
      };
    }

    if (data && data.length > 0) {
      const lastActivity = new Date(data[0].timestamp);
      const minutesAgo = Math.floor((Date.now() - lastActivity.getTime()) / (1000 * 60));
      
      return {
        service: 'Monitor Activity',
        status: minutesAgo <= 5 ? 'healthy' : 'degraded',
        responseTime,
        lastCheck: new Date().toISOString(),
        details: {
          lastActivity: lastActivity.toISOString(),
          minutesAgo
        }
      };
    } else {
      return {
        service: 'Monitor Activity',
        status: 'unhealthy',
        responseTime,
        lastCheck: new Date().toISOString(),
        error: 'No recent monitor activity found'
      };
    }
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'Monitor Activity',
      status: 'unhealthy',
      responseTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Check database connectivity and basic functionality
 */
export async function checkDatabaseHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Simple query to test database connectivity - just get one monitor
    const { data, error } = await supabase
      .from('monitors')
      .select('id')
      .limit(1);

    const responseTime = Date.now() - startTime;

    if (error) {
      return {
        service: 'Database',
        status: 'unhealthy',
        responseTime,
        lastCheck: new Date().toISOString(),
        error: error.message
      };
    }

    return {
      service: 'Database',
      status: 'healthy',
      responseTime,
      lastCheck: new Date().toISOString(),
      details: {
        connectionTest: 'successful'
      }
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return {
      service: 'Database',
      status: 'unhealthy',
      responseTime,
      lastCheck: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Perform comprehensive system health check
 */
export async function performSystemHealthCheck(): Promise<SystemHealthStatus> {
  const checks = await Promise.all([
    checkDatabaseHealth(),
    checkMonitorCheckerHealth(),
    checkRecentMonitorActivity()
  ]);

  // Determine overall health
  const hasUnhealthy = checks.some(check => check.status === 'unhealthy');
  const hasDegraded = checks.some(check => check.status === 'degraded');
  
  let overall: 'healthy' | 'degraded' | 'unhealthy';
  if (hasUnhealthy) {
    overall = 'unhealthy';
  } else if (hasDegraded) {
    overall = 'degraded';
  } else {
    overall = 'healthy';
  }

  return {
    overall,
    services: checks,
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Get a simple health status for display in UI
 */
export function getHealthStatusDisplay(status: SystemHealthStatus) {
  const statusColors = {
    healthy: 'text-green-600 bg-green-50',
    degraded: 'text-yellow-600 bg-yellow-50', 
    unhealthy: 'text-red-600 bg-red-50'
  };

  const statusText = {
    healthy: 'All Systems Operational',
    degraded: 'Some Issues Detected',
    unhealthy: 'System Issues'
  };

  return {
    color: statusColors[status.overall],
    text: statusText[status.overall],
    details: status.services
  };
}
