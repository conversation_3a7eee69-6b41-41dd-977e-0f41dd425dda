# Background Monitoring Service

This document explains how to set up and use the background monitoring service for your application.

## Overview

The monitoring service consists of:

1. A Supabase Edge Function that checks the status of monitors
2. A notifications system to alert users when monitor status changes
3. A UI component to display notifications
4. A manual trigger to run checks on demand

## Setup Instructions

### 1. Create the Notifications Table

Run the `supabase_notifications_table.sql` script in the Supabase SQL Editor to create the notifications table and set up the necessary permissions.

### 2. Deploy the Edge Function

#### Option 1: Using the Supabase CLI with PowerShell

1. Install the Supabase CLI if you haven't already:
   ```
   npm install -g supabase
   ```

2. Log in to your Supabase account:
   ```
   supabase login
   ```

3. Run the PowerShell deployment script:
   ```
   ./deploy-edge-function.ps1
   ```

4. If you encounter any execution policy issues, you may need to run:
   ```
   Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass
   ```
   Then run the script again.

#### Option 2: Manual Deployment

1. Go to the Supabase dashboard
2. Navigate to Edge Functions
3. Create a new function named "monitor-checker"
4. Upload the contents of the `supabase/functions/monitor-checker/index.ts` file

### 3. Set Up Scheduled Execution

To run the monitor checker automatically at regular intervals, you have two options:

#### Option 1: Using Supabase Scheduled Functions (Recommended)

1. Go to the Supabase dashboard
2. Navigate to Database > Functions > Scheduled Functions
3. Create a new scheduled function:
   - Name: trigger_monitor_checker
   - Schedule: * * * * * (every minute)
   - Function: SELECT trigger_monitor_checker();

#### Option 2: Using an External Service

You can use an external service like Uptime Robot, Cronitor, or a simple cron job to call your Edge Function URL at regular intervals.

## Usage

### Viewing Notifications

Notifications will appear in the notification center in the top-right corner of the dashboard. Click the bell icon to view them.

### Manual Checks

You can trigger a manual check of all monitors by clicking the "Check Now" button on the dashboard.

### Monitor-Specific Checks

To check a specific monitor, you can use the ManualCheckButton component with a monitorId:

```jsx
<ManualCheckButton monitorId="your-monitor-id" />
```

## How It Works

1. The Edge Function runs at regular intervals (every minute)
2. For each active monitor, it checks if it's due for checking based on its interval
3. It performs the check and records the result in the monitor_history table
4. If a monitor's status changes, it creates a notification
5. The UI displays these notifications in real-time

## Customization

### Notification Types

The system supports different types of notifications:
- `up`: Monitor is now up
- `down`: Monitor is now down
- `other`: Other types of notifications

### Check Types

The system supports different types of checks:
- `http`: Standard HTTP request
- `ping`: Simple ping check
- `port`: Port availability check

## Troubleshooting

### Edge Function Not Running

1. Check the Supabase logs for any errors
2. Verify that the function is deployed correctly
3. Make sure the scheduled function is set up properly
4. Try manually invoking the function using the provided PowerShell script:
   ```
   ./invoke-monitor-checker.ps1
   ```

### Testing Locally

You can test the Edge Function locally before deploying:

1. Run the local test script:
   ```
   ./test-edge-function-locally.ps1
   ```

2. Send a test request to the local endpoint with a specific monitor ID:
   ```
   Invoke-RestMethod -Uri "http://localhost:54321/functions/v1/monitor-checker" -Method Post -Body '{"monitorId":"your-monitor-id"}' -ContentType "application/json"
   ```

### Notifications Not Appearing

1. Check the browser console for any errors
2. Verify that the notifications table exists and has the correct structure
3. Check that the user has the necessary permissions

## Advanced Configuration

For more advanced configuration options, you can modify:

1. The concurrency limit in the Edge Function (default: 5)
2. The timeout for checks (default: from monitor settings)
3. The notification display settings in the NotificationCenter component
