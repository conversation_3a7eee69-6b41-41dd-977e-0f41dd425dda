-- This script fixes the relationship between company_members and users tables
-- Run this in the Supabase SQL Editor

-- First, check if the auth.users table exists
DO $$
BEGIN
    IF EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'auth' AND table_name = 'users'
    ) THEN
        RAISE NOTICE 'auth.users table exists';
    ELSE
        RAISE EXCEPTION 'auth.users table does not exist';
    END IF;
END $$;

-- Create a public.users view that references auth.users
-- This allows us to join with auth.users from public tables
CREATE OR REPLACE VIEW public.users AS
SELECT 
    id,
    email,
    raw_user_meta_data->>'full_name' as full_name,
    raw_user_meta_data->>'avatar_url' as avatar_url
FROM auth.users;

-- Now we can modify the company_members query to use this view
-- In CompanyContext.tsx, use this query:
-- 
-- const { data, error } = await supabase
--   .from('company_members')
--   .select('*, user:users(id, email, full_name, avatar_url)')
--   .eq('company_id', companyId);
--
-- Note the change from 'users:user_id' to 'user:users'

-- Verify the view was created
SELECT * FROM public.users LIMIT 5;
