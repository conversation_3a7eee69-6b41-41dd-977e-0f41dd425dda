// This script forces a refresh of the user data
// Run this in the browser console if the avatar doesn't appear in the top corner

async function refreshUserData() {
  console.log('Starting user data refresh...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('Current user data:', user);
  console.log('Current user metadata:', user.user_metadata);
  
  // Force a refresh of the user data
  console.log('Forcing refresh of user data...');
  const { data, error } = await supabase.auth.refreshSession();
  
  if (error) {
    console.error('Error refreshing session:', error);
    return;
  }
  
  console.log('Session refreshed successfully!');
  console.log('Updated user data:', data.user);
  console.log('Updated user metadata:', data.user.user_metadata);
  
  // Check if the avatar URL is present
  if (data.user.user_metadata?.avatar_url) {
    console.log('Avatar URL found:', data.user.user_metadata.avatar_url);
    
    // Create a preview of the avatar
    const img = new Image();
    img.src = data.user.user_metadata.avatar_url;
    
    img.onload = () => {
      console.log('Avatar image loaded successfully!');
      
      // Create a div to display the image on the page
      const div = document.createElement('div');
      div.style.position = 'fixed';
      div.style.top = '10px';
      div.style.right = '10px';
      div.style.zIndex = '9999';
      div.style.background = 'white';
      div.style.padding = '10px';
      div.style.border = '1px solid black';
      div.style.borderRadius = '5px';
      div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
      
      const imgElement = document.createElement('img');
      imgElement.src = data.user.user_metadata.avatar_url;
      imgElement.style.width = '100px';
      imgElement.style.height = '100px';
      imgElement.style.borderRadius = '50%';
      imgElement.style.objectFit = 'cover';
      
      const message = document.createElement('p');
      message.textContent = 'User data refreshed! Please refresh the page to see the changes.';
      message.style.marginTop = '10px';
      message.style.fontSize = '14px';
      
      const refreshButton = document.createElement('button');
      refreshButton.textContent = 'Refresh Page';
      refreshButton.style.marginTop = '10px';
      refreshButton.style.padding = '5px';
      refreshButton.style.cursor = 'pointer';
      refreshButton.onclick = () => window.location.reload();
      
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Close';
      closeButton.style.marginTop = '10px';
      closeButton.style.marginLeft = '10px';
      closeButton.style.padding = '5px';
      closeButton.style.cursor = 'pointer';
      closeButton.onclick = () => document.body.removeChild(div);
      
      div.appendChild(imgElement);
      div.appendChild(message);
      div.appendChild(refreshButton);
      div.appendChild(closeButton);
      
      document.body.appendChild(div);
    };
    
    img.onerror = () => {
      console.error('Error loading avatar image. The URL might not be publicly accessible.');
    };
  } else {
    console.error('Avatar URL not found in user metadata!');
  }
  
  return {
    success: true,
    user: data.user
  };
}

// Run the refresh
refreshUserData().then(result => {
  console.log('Refresh completed. Results:', result);
});
