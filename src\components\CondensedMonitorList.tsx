import React from 'react';
import { Card } from '@/components/ui/card';
import { Check, AlertTriangle, XCircle, PauseCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';

type MonitorStatus = 'operational' | 'up' | 'degraded' | 'down' | 'paused' | 'unknown' |
                   'Operational' | 'Up' | 'Degraded' | 'Down' | 'Paused' | 'Unknown';

interface Monitor {
  id: string;
  name: string;
  target: string;
  status?: MonitorStatus;
  last_check_time?: string | null;
  last_response_time?: number | null;
  active?: boolean;
  error_message?: string | null;
  companies?: Array<{
    company_id: string;
    company_name?: string;
  }>;
}

interface CondensedMonitorListProps {
  monitors: Monitor[];
}

// Helper function to get status color
const getStatusColor = (status: MonitorStatus) => {
  // Normalize status to lowercase for case-insensitive comparison
  const statusLower = status.toLowerCase() as MonitorStatus;

  switch (statusLower) {
    case 'operational':
    case 'up':
      return 'bg-green-500 hover:bg-green-600';
    case 'degraded':
      return 'bg-yellow-500 hover:bg-yellow-600';
    case 'down':
      return 'bg-red-500 hover:bg-red-600';
    case 'paused':
      return 'bg-slate-500 hover:bg-slate-600';
    default:
      return 'bg-slate-500 hover:bg-slate-600';
  }
};

// Helper function to get status icon
const getStatusIcon = (status: MonitorStatus) => {
  // Normalize status to lowercase for case-insensitive comparison
  const statusLower = status.toLowerCase() as MonitorStatus;

  switch (statusLower) {
    case 'operational':
    case 'up':
      return <Check className="h-5 w-5 text-white" />;
    case 'degraded':
      return <AlertTriangle className="h-5 w-5 text-white" />;
    case 'down':
      return <XCircle className="h-5 w-5 text-white" />;
    case 'paused':
      return <PauseCircle className="h-5 w-5 text-white" />;
    default:
      return null;
  }
};

// Format response time with appropriate unit
const formatResponseTime = (responseTime: number | null) => {
  if (responseTime === null) return 'N/A';

  // Always display in milliseconds for consistency
  return `${Math.round(responseTime)}ms`;
};

// Helper function to get compact degraded reason
const getCompactDegradedReason = (errorMessage: string): string => {
  // Create compact error messages for condensed tiles
  if (errorMessage.includes('HTTP status indicates degraded service:')) {
    return errorMessage.replace('HTTP status indicates degraded service: ', 'Error ');
  }
  if (errorMessage.includes('Slow response time:')) {
    const match = errorMessage.match(/(\d+)ms.*threshold:\s*(\d+)ms/);
    return match ? `Slow: ${match[1]}ms` : errorMessage.replace('Slow response time: ', 'Slow: ');
  }
  // Truncate long error messages for condensed tiles
  return errorMessage.length > 25 ? errorMessage.substring(0, 25) + '...' : errorMessage;
};

// Helper function to get error explanation
const getErrorExplanation = (errorMessage: string): string => {
  // HTTP Status Code explanations
  if (errorMessage.includes('503')) {
    return "Service temporarily unavailable";
  }
  if (errorMessage.includes('429')) {
    return "Too many requests - rate limited";
  }
  if (errorMessage.includes('500')) {
    return "Internal server error";
  }
  if (errorMessage.includes('502')) {
    return "Bad gateway - upstream error";
  }
  if (errorMessage.includes('504')) {
    return "Gateway timeout";
  }

  // Response time explanations
  if (errorMessage.includes('Slow response time:')) {
    return "Performance issues detected";
  }

  // Generic degraded explanation
  return "Service experiencing issues";
};

export function CondensedMonitorList({ monitors }: CondensedMonitorListProps) {
  const navigate = useNavigate();

  // Group monitors by status
  const groupedMonitors = monitors.reduce((acc, monitor) => {
    // Normalize status to lowercase for case-insensitive comparison
    let status = (monitor.status || 'unknown').toString().toLowerCase();

    // Map 'operational' to 'up' for consistency
    if (status === 'operational') {
      status = 'up';
    }

    // Map 'true' to 'up' and 'false' to 'down' for legacy boolean values
    if (status === 'true') {
      status = 'up';
    } else if (status === 'false') {
      status = 'down';
    }

    // Ensure status is one of the allowed values
    if (!['up', 'down', 'degraded', 'paused', 'unknown'].includes(status)) {
      status = 'up';
    }

    if (!acc[status]) {
      acc[status] = [];
    }
    acc[status].push(monitor);
    return acc;
  }, {} as Record<string, Monitor[]>);

  // Order: down, degraded, up/operational, paused
  const statusOrder: MonitorStatus[] = ['down', 'degraded', 'up', 'paused'];

  // Create a map of normalized status values to their original values
  const normalizedStatusMap: Record<string, string[]> = {};

  // Add all possible status values to the map
  Object.keys(groupedMonitors).forEach(status => {
    const normalized = status.toLowerCase();
    if (!normalizedStatusMap[normalized]) {
      normalizedStatusMap[normalized] = [];
    }
    normalizedStatusMap[normalized].push(status);
  });

  // Check if any monitors are displayed
  const hasVisibleMonitors = statusOrder.some(status => (groupedMonitors[status] || []).length > 0);

  return (
    <div className="space-y-6">
      {!hasVisibleMonitors && (
        <div className="text-center py-8 bg-muted rounded-lg">
          <h3 className="text-lg font-medium mb-2">No Monitors to Display</h3>
          <p className="text-muted-foreground">
            There are no monitors to display in the condensed view.
          </p>
        </div>
      )}

      {statusOrder.map(statusLower => {
        // Get all monitors for this status, including different case variations
        let statusMonitors: Monitor[] = [];

        // Check if we have any monitors with this status (case-insensitive)
        const originalStatusValues = normalizedStatusMap[statusLower] || [];

        // Combine all monitors with this status, regardless of case
        originalStatusValues.forEach(originalStatus => {
          statusMonitors = [...statusMonitors, ...(groupedMonitors[originalStatus] || [])];
        });

        // If no monitors with this status, also check the direct status
        if (statusMonitors.length === 0) {
          statusMonitors = groupedMonitors[statusLower] || [];
        }

        if (statusMonitors.length === 0) return null;

        return (
          <div key={statusLower} className="space-y-2">
            <h3 className="text-lg font-medium capitalize flex items-center">
              {getStatusIcon(statusLower)}
              <span className="ml-2">
                {statusLower === 'up' ? 'Up' :
                 statusLower === 'down' ? 'Down' :
                 statusLower === 'degraded' ? 'Degraded' :
                 statusLower === 'paused' ? 'Paused' :
                 statusLower.charAt(0).toUpperCase() + statusLower.slice(1)}
              </span>
              <span className="ml-2 text-sm text-muted-foreground">({statusMonitors.length})</span>
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
              {statusMonitors.map(monitor => (
                <Card
                  key={monitor.id}
                  className={cn(
                    "cursor-pointer transition-all duration-200 border-0 overflow-hidden",
                    getStatusColor((monitor.status || 'unknown') as MonitorStatus)
                  )}
                  onClick={() => {
                    // Clear any company_changed flag to prevent navigation issues
                    localStorage.removeItem('company_changed');
                    navigate(`/monitor/${monitor.id}`, {
                      state: {
                        degradedStatus: monitor.status === 'degraded' ? monitor.status : null,
                        errorMessage: monitor.status === 'degraded' ? monitor.error_message : null,
                        fromDashboard: true
                      }
                    });
                  }}
                >
                  <div className="p-3 text-white">
                    <div className="font-medium truncate">{monitor.name}</div>
                    <div className="text-xs opacity-90 truncate">{monitor.target}</div>

                    {/* Show degraded reason and description for degraded monitors */}
                    {monitor.status === 'degraded' && monitor.error_message && (
                      <div className="mt-1 space-y-1">
                        <div className="text-xs font-medium bg-white/20 px-2 py-1 rounded">
                          {getCompactDegradedReason(monitor.error_message)}
                        </div>
                        <div className="text-xs opacity-80 leading-tight">
                          {getErrorExplanation(monitor.error_message)}
                        </div>
                      </div>
                    )}

                    {monitor.companies && monitor.companies.length > 0 && (
                      <div className="mt-1 flex flex-wrap gap-1">
                        {monitor.companies.map((company) => (
                          company.company_name && (
                            <span key={company.company_id} className="text-xs bg-white/20 px-1 rounded">
                              {company.company_name}
                            </span>
                          )
                        )).filter(Boolean)}
                      </div>
                    )}
                    <div className="flex justify-between items-center mt-2 text-xs">
                      <div>
                        {monitor.last_check_time ? (
                          formatDistanceToNow(new Date(monitor.last_check_time), { addSuffix: true })
                        ) : (
                          'Never checked'
                        )}
                      </div>
                      <div className="font-medium">
                        {formatResponseTime(monitor.last_response_time)}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
}

export default CondensedMonitorList;
