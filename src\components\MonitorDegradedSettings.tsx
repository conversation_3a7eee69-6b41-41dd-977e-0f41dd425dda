import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertTriangle, RefreshCw, Save, Trash2, HelpCircle, Info } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { DegradedThresholds } from '@/types/monitor';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Helper component for field explanations
const FieldHelp = ({ title, description, example }: { title: string; description: string; example: string }) => {
  return (
    <Tooltip delayDuration={300}>
      <TooltipTrigger asChild>
        <Button variant="ghost" size="icon" className="h-5 w-5 p-0 ml-1 text-slate-500 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200">
          <HelpCircle className="h-4 w-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent side="right" align="start" className="max-w-sm p-4 z-50 text-left">
        <div className="w-64">
        <div className="space-y-2">
          <h4 className="font-medium text-sm">{title}</h4>
          <p className="text-sm text-slate-500 dark:text-slate-400">{description}</p>
          <div className="mt-2 p-2 bg-slate-100 dark:bg-slate-800 rounded-md">
            <h5 className="text-xs font-medium mb-1">Example:</h5>
            <p className="text-xs">{example}</p>
          </div>
        </div>
        </div>
      </TooltipContent>
    </Tooltip>
  );
};

// Current settings display component
const CurrentSettings = ({ settings, globalSettings }: { settings: DegradedThresholds; globalSettings: DegradedThresholds | null }) => {
  return (
    <div className="border rounded-md p-4 bg-slate-50 dark:bg-slate-800/50 mb-4">
      <h4 className="font-medium flex items-center mb-3">
        <Info className="h-4 w-4 mr-2 text-blue-500" />
        Current Custom Settings
      </h4>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">Response Time Threshold:</span>
              <span className="font-medium">{settings.response_time} ms</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">Error Rate Threshold:</span>
              <span className="font-medium">{settings.error_rate}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-slate-600 dark:text-slate-400">Consecutive Failures:</span>
              <span className="font-medium">{settings.consecutive_failures}</span>
            </div>
          </div>
        </div>
        <div>
          <div className="space-y-2">
            <div>
              <span className="text-sm text-slate-600 dark:text-slate-400">Status Codes:</span>
              <div className="flex flex-wrap gap-1 mt-1">
                {settings.status_codes && settings.status_codes.length > 0 ? (
                  settings.status_codes.map(code => (
                    <Badge key={code} variant="secondary" className="text-xs">{code}</Badge>
                  ))
                ) : (
                  <span className="text-sm italic">None</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      {globalSettings && (
        <div className="mt-3 pt-3 border-t border-slate-200 dark:border-slate-700">
          <details className="text-xs text-slate-500 dark:text-slate-400">
            <summary className="cursor-pointer hover:text-slate-700 dark:hover:text-slate-300">Compare with global settings</summary>
            <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-2">
              <div>
                <div className="space-y-1">
                  <div className="flex justify-between">
                    <span>Global Response Time:</span>
                    <span>{globalSettings.response_time} ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Global Error Rate:</span>
                    <span>{globalSettings.error_rate}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Global Consecutive Failures:</span>
                    <span>{globalSettings.consecutive_failures}</span>
                  </div>
                </div>
              </div>
              <div>
                <div>
                  <span>Global Status Codes:</span>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {globalSettings.status_codes && globalSettings.status_codes.length > 0 ? (
                      globalSettings.status_codes.map(code => (
                        <Badge key={code} variant="outline" className="text-xs">{code}</Badge>
                      ))
                    ) : (
                      <span className="italic">None</span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </details>
        </div>
      )}
    </div>
  );
};

interface MonitorDegradedSettingsProps {
  monitorId: string;
  initialSettings?: DegradedThresholds | null;
  onSettingsChange: (settings: DegradedThresholds | null) => void;
  isSuperadmin: boolean;
}

// Default settings to use when no global settings are available
const DEFAULT_DEGRADED_SETTINGS = {
  response_time: 1000,
  error_rate: 10,
  status_codes: [429, 503],
  consecutive_failures: 2
};

const MonitorDegradedSettings: React.FC<MonitorDegradedSettingsProps> = ({
  monitorId,
  initialSettings,
  onSettingsChange,
  isSuperadmin
}) => {
  const [useCustomSettings, setUseCustomSettings] = useState<boolean>(!!initialSettings);
  const [settings, setSettings] = useState<DegradedThresholds>({
    response_time: initialSettings?.response_time || DEFAULT_DEGRADED_SETTINGS.response_time,
    error_rate: initialSettings?.error_rate || DEFAULT_DEGRADED_SETTINGS.error_rate,
    status_codes: initialSettings?.status_codes || DEFAULT_DEGRADED_SETTINGS.status_codes,
    consecutive_failures: initialSettings?.consecutive_failures || DEFAULT_DEGRADED_SETTINGS.consecutive_failures
  });
  const [statusCodeInput, setStatusCodeInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [globalSettings, setGlobalSettings] = useState<DegradedThresholds | null>(null);

  // Load global settings for reference
  useEffect(() => {
    const loadGlobalSettings = async () => {
      try {
        const { data, error } = await supabase
          .from('degraded_settings')
          .select('*')
          .limit(1)
          .single();

        if (error) {
          console.error('Error loading global settings:', error);
          return;
        }

        if (data) {
          setGlobalSettings({
            response_time: data.response_time,
            error_rate: data.error_rate,
            status_codes: data.status_codes || [],
            consecutive_failures: data.consecutive_failures
          });
        }
      } catch (error) {
        console.error('Error loading global settings:', error);
      }
    };

    loadGlobalSettings();
  }, []);

  // Update parent component when settings change
  useEffect(() => {
    if (useCustomSettings) {
      onSettingsChange(settings);
    } else {
      onSettingsChange(null);
    }
  }, [useCustomSettings, settings, onSettingsChange]);

  // Handle input changes
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Convert to number for numeric fields
    if (name === 'response_time' || name === 'error_rate' || name === 'consecutive_failures') {
      setSettings(prev => ({
        ...prev,
        [name]: parseInt(value) || 0
      }));
    }
  }, []);

  // Handle status code input
  const handleStatusCodeAdd = useCallback(() => {
    const code = parseInt(statusCodeInput);
    if (code && !isNaN(code) && code > 0) {
      if (!settings.status_codes?.includes(code)) {
        setSettings(prev => ({
          ...prev,
          status_codes: [...(prev.status_codes || []), code].sort((a, b) => a - b)
        }));
      }
      setStatusCodeInput('');
    }
  }, [statusCodeInput, settings.status_codes]);

  // Handle status code removal
  const handleStatusCodeRemove = useCallback((code: number) => {
    setSettings(prev => ({
      ...prev,
      status_codes: (prev.status_codes || []).filter(c => c !== code)
    }));
  }, []);

  // Memoize the status codes list to prevent unnecessary re-renders
  const statusCodesList = useMemo(() => (
    <>
      {(settings.status_codes || []).map(code => (
        <Badge
          key={code}
          variant="secondary"
          className="flex items-center gap-1 px-3 py-1"
        >
          {code}
          <button
            type="button"
            className="ml-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
            onClick={() => handleStatusCodeRemove(code)}
          >
            &times;
          </button>
        </Badge>
      ))}
      {(!settings.status_codes || settings.status_codes.length === 0) && (
        <p className="text-sm text-slate-500 italic">No status codes added</p>
      )}
    </>
  ), [settings.status_codes, handleStatusCodeRemove]);

  // Toggle custom settings
  const handleToggleCustomSettings = useCallback((checked: boolean) => {
    setUseCustomSettings(checked);
  }, []);

  // Reset to standard (global) settings
  const handleUseStandardSettings = useCallback(async () => {
    if (!monitorId) return;

    // If there are no custom settings saved yet, just reset the form
    if (!initialSettings) {
      setUseCustomSettings(false);
      setSettings(globalSettings || DEFAULT_DEGRADED_SETTINGS);
      return;
    }

    try {
      setIsLoading(true);

      // Delete the custom settings from the database
      const { error } = await supabase
        .from('monitor_degraded_settings')
        .delete()
        .eq('monitor_id', monitorId);

      if (error) throw error;

      // Reset the UI
      setUseCustomSettings(false);
      setSettings(globalSettings || DEFAULT_DEGRADED_SETTINGS);
      onSettingsChange(null);

      // Success - no toast needed
    } catch (error) {
      console.error('Error resetting to standard settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to reset to standard settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }, [monitorId, initialSettings, globalSettings, onSettingsChange]);

  // If not superadmin, don't show this component
  if (!isSuperadmin) {
    return null;
  }

  return (
    <div className="w-full mt-8 border rounded-md p-4 transition-all duration-300">
        <div className="flex items-center mb-4">
          <AlertTriangle className="h-5 w-5 mr-2 text-amber-500" />
          <h3 className="text-lg font-medium">Degraded Service Settings</h3>
          <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
            Superadmin Only
          </Badge>
          {initialSettings && (
            <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Custom Settings Active
            </Badge>
          )}
        </div>

        {/* Display current settings if they exist */}
        {initialSettings && (
          <div className="relative">
            <CurrentSettings settings={initialSettings} globalSettings={globalSettings} />
            <Button
              variant="outline"
              size="sm"
              className="absolute top-2 right-2 bg-white dark:bg-slate-800 text-blue-600 border-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-400 dark:hover:bg-blue-900/20"
              onClick={handleUseStandardSettings}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Resetting...
                </>
              ) : (
                <>Use Standard Settings</>
              )}
            </Button>
          </div>
        )}

        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="use-custom-settings"
              checked={useCustomSettings}
              onCheckedChange={handleToggleCustomSettings}
            />
            <Label htmlFor="use-custom-settings">
              Use custom degraded settings for this monitor
            </Label>
          </div>

          {useCustomSettings && (
            <div className="border rounded-md p-4 mt-4 space-y-6">
              <div className="text-sm text-slate-500 dark:text-slate-400 space-y-2">
                <p>
                  <strong>What are degraded settings?</strong> These determine when a monitor is considered "degraded" rather than completely down.
                </p>
                <p>
                  These custom settings will override the global degraded thresholds for this specific monitor. A monitor is considered degraded when it meets any of these conditions:
                </p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Response time exceeds the threshold</li>
                  <li>Error rate exceeds the threshold</li>
                  <li>Returns one of the specified HTTP status codes</li>
                  <li>Has the specified number of consecutive partial failures</li>
                </ul>
              </div>

            <Tabs defaultValue="thresholds">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
                <TabsTrigger value="status-codes">Status Codes</TabsTrigger>
              </TabsList>

              <TabsContent value="thresholds" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="response_time">
                      Response Time Threshold (ms)
                    </Label>
                    <FieldHelp
                      title="Response Time Threshold"
                      description="The maximum acceptable response time in milliseconds. If a monitor's response time exceeds this value but the service is still responding, it will be marked as degraded rather than down."
                      example="If set to 1000ms (1 second), a website that loads in 1.2 seconds will be marked as degraded, while one that loads in 0.8 seconds will be considered fully operational."
                    />
                  </div>
                  <Input
                    id="response_time"
                    name="response_time"
                    type="number"
                    min="0"
                    value={settings.response_time}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                    Services with response times above this threshold will be considered degraded.
                  </p>
                  {globalSettings && (
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                      Global setting: {globalSettings.response_time}ms
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="error_rate">
                      Error Rate Threshold (%)
                    </Label>
                    <FieldHelp
                      title="Error Rate Threshold"
                      description="The maximum acceptable percentage of errors. If a service has more errors than this threshold but is still partially functioning, it will be marked as degraded."
                      example="If set to 10%, a service that returns errors for 15% of requests will be marked as degraded, while one with 5% errors will be considered operational but with issues."
                    />
                  </div>
                  <Input
                    id="error_rate"
                    name="error_rate"
                    type="number"
                    min="0"
                    max="100"
                    value={settings.error_rate}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                    Services with error rates above this percentage will be considered degraded.
                  </p>
                  {globalSettings && (
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                      Global setting: {globalSettings.error_rate}%
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="consecutive_failures">
                      Consecutive Failures Threshold
                    </Label>
                    <FieldHelp
                      title="Consecutive Failures Threshold"
                      description="The number of consecutive partial failures that must occur before a service is considered degraded. This helps distinguish between temporary glitches and persistent issues."
                      example="If set to 3, a service must have 3 consecutive checks with partial failures (like slow response time) before being marked as degraded. This prevents temporary spikes from triggering false alarms."
                    />
                  </div>
                  <Input
                    id="consecutive_failures"
                    name="consecutive_failures"
                    type="number"
                    min="1"
                    value={settings.consecutive_failures}
                    onChange={handleInputChange}
                  />
                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                    Number of consecutive partial failures before a service is considered degraded.
                  </p>
                  {globalSettings && (
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                      Global setting: {globalSettings.consecutive_failures}
                    </p>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="status-codes" className="space-y-4 pt-4">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="status_codes">
                      HTTP Status Codes for Degraded Status
                    </Label>
                    <FieldHelp
                      title="HTTP Status Codes"
                      description="Specific HTTP status codes that should be treated as degraded rather than down. These are typically codes that indicate temporary issues or partial availability."
                      example="If you add status code 429 (Too Many Requests), a service that returns this code will be marked as degraded rather than down. This is useful for rate-limited APIs that are still functioning but temporarily restricting access."
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Input
                      id="status_code_input"
                      placeholder="Add status code (e.g. 429)"
                      value={statusCodeInput}
                      onChange={(e) => setStatusCodeInput(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleStatusCodeAdd();
                        }
                      }}
                    />
                    <Button
                      type="button"
                      onClick={handleStatusCodeAdd}
                    >
                      Add
                    </Button>
                  </div>

                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-1">
                    HTTP status codes that indicate a degraded service rather than a complete outage.
                    Common examples: 429 (Too Many Requests), 503 (Service Unavailable).
                  </p>

                  <div className="flex flex-wrap gap-2 mt-2">
                    {statusCodesList}
                  </div>

                  {globalSettings && (
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-2">
                      Global status codes: {globalSettings.status_codes?.join(', ') || 'None'}
                    </p>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </div>
    </div>
  );
};

export default MonitorDegradedSettings;
