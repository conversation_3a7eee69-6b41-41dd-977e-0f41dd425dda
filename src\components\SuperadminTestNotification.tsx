import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, Check, RefreshCw, Send } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import { useQuery } from '@tanstack/react-query';

interface SuperadminTestNotificationProps {
  isSuperadmin: boolean;
}

const SuperadminTestNotification: React.FC<SuperadminTestNotificationProps> = ({
  isSuperadmin
}) => {
  const [selectedMonitor, setSelectedMonitor] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('down');
  const [isSending, setIsSending] = useState(false);

  // Fetch all monitors
  const { data: monitors, isLoading } = useQuery({
    queryKey: ['monitors-for-test'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('monitors')
        .select('id, name, type, target')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    enabled: isSuperadmin
  });

  // Send test notification
  const sendTestNotification = async () => {
    if (!selectedMonitor) {
      toast({
        title: 'Error',
        description: 'Please select a monitor',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);
    try {
      // Use the new function that forces email sending
      const { data, error } = await supabase
        .rpc('superadmin_send_test_email', {
          p_monitor_id: selectedMonitor,
          p_status: selectedStatus,
          p_force_email: true
        });

      if (error) throw error;

      toast({
        title: 'Success',
        description: data,
      });
    } catch (error: any) {
      console.error('Error sending test notification:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to send test notification',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  if (!isSuperadmin) {
    return null;
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Send className="h-5 w-5 mr-2 text-blue-500" />
          Test Notification
          <Badge variant="outline" className="ml-2 text-amber-500 border-amber-500">
            Superadmin Only
          </Badge>
        </CardTitle>
        <CardDescription>
          Send a test notification for any monitor to verify email delivery.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin text-slate-500" />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="monitor-select">Select Monitor</Label>
              <Select
                value={selectedMonitor}
                onValueChange={setSelectedMonitor}
              >
                <SelectTrigger id="monitor-select">
                  <SelectValue placeholder="Select a monitor" />
                </SelectTrigger>
                <SelectContent>
                  {monitors?.map((monitor) => (
                    <SelectItem key={monitor.id} value={monitor.id}>
                      {monitor.name} ({monitor.type}: {monitor.target})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status-select">Status</Label>
              <Select
                value={selectedStatus}
                onValueChange={setSelectedStatus}
              >
                <SelectTrigger id="status-select">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="down" className="flex items-center">
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                      Down
                    </div>
                  </SelectItem>
                  <SelectItem value="up">
                    <div className="flex items-center">
                      <Check className="h-4 w-4 mr-2 text-green-500" />
                      Up
                    </div>
                  </SelectItem>
                  <SelectItem value="degraded">
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                      Degraded
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button
              onClick={sendTestNotification}
              disabled={isSending || !selectedMonitor}
              className="w-full mt-4"
            >
              {isSending ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Test Notification
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SuperadminTestNotification;
