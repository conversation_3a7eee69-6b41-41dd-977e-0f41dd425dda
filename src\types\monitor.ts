// Monitor types

export type MonitorType = 'http' | 'ping' | 'port';

export interface DegradedThresholds {
  response_time?: number;
  error_rate?: number;
  status_codes?: number[];
  consecutive_failures?: number;
}

export interface Monitor {
  id: string;
  name: string;
  target: string;
  type: MonitorType;
  interval: number;
  timeout: number;
  active: boolean;
  user_id: string;
  company_id?: string; // Kept for backward compatibility
  created_at: string;
  // Soft delete fields
  deleted?: boolean;
  deleted_at?: string;
  deleted_by?: string;
  // Virtual fields
  companies?: MonitorCompany[];
  // Degraded settings
  degraded_settings?: DegradedThresholds;
}

export interface MonitorCompany {
  id: string;
  monitor_id: string;
  company_id: string;
  created_at: string;
  // Joined fields
  company_name?: string;
}

export interface MonitorHistory {
  id: string;
  monitor_id: string;
  status: boolean;
  response_time: number | null;
  error_message: string | null;
  timestamp: string;
}

export interface CreateMonitorData {
  name: string;
  target: string;
  type: MonitorType;
  interval: number;
  timeout: number;
  company_ids: string[]; // Array of company IDs
  degraded_settings?: DegradedThresholds; // Optional custom degraded settings
}

export interface UpdateMonitorData {
  name?: string;
  target?: string;
  type?: MonitorType;
  interval?: number;
  timeout?: number;
  active?: boolean;
  company_ids?: string[]; // Array of company IDs
  degraded_settings?: DegradedThresholds | null; // null means remove custom settings
}

export interface MonitorWithStatus extends Monitor {
  status?: 'up' | 'down' | 'degraded' | 'paused';
  uptime?: string;
  lastChecked?: string;
  responseTime?: string;
}
