# PowerShell script to deploy the monitor-checker function using Docker

Write-Host "Deploying monitor-checker Edge Function using Docker..." -ForegroundColor Cyan

# Get the absolute path to the function directory
$functionPath = Resolve-Path "supabase/functions/monitor-checker"

# Run the Supabase CLI through Docker
try {
    Write-Host "Running deployment command through Docker..." -ForegroundColor Yellow
    
    # Pull the Supabase CLI image
    docker pull supabase/cli
    
    # Run the deployment command
    docker run --rm -v "${functionPath}:/function" supabase/cli functions deploy monitor-checker --project-ref axcfqilzeombkbzebeym
    
    # Check if deployment was successful
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Deployment completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "An error occurred during deployment: $_" -ForegroundColor Red
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
