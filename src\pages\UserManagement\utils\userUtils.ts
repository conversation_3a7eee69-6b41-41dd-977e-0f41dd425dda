/**
 * Utility functions for user management
 */

/**
 * Get user initials for avatar fallback
 */
export const getUserInitials = (name: string, email: string): string => {
  if (name) {
    const nameParts = name.split(' ');
    if (nameParts.length > 1) {
      return `${nameParts[0][0]}${nameParts[1][0]}`.toUpperCase();
    }
    return name[0].toUpperCase();
  }

  return email ? email[0].toUpperCase() : 'U';
};

/**
 * Check if user is the current logged-in user
 */
export const isCurrentUser = (currentUserId: string | undefined, userId: string): boolean => {
  return currentUserId === userId;
};
