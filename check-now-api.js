// API endpoint for the "Check Now" functionality
const express = require('express');
const cors = require('cors');
const { createClient } = require('@supabase/supabase-js');
const axios = require('axios');
require('dotenv').config();

// Create Express app
const app = express();
const port = process.env.CHECK_NOW_PORT || 3001;

// Enable CORS
app.use(cors());
app.use(express.json());

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_KEY
);

// Function to check a monitor
async function checkMonitor(monitor) {
  try {
    console.log(`Checking monitor: ${monitor.name} (${monitor.target})`);
    
    // Perform the check
    const startTime = Date.now();
    let status = false;
    let responseTime = null;
    let errorMessage = null;
    
    try {
      switch (monitor.type) {
        case 'http':
          // HTTP check
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), monitor.timeout * 1000);
          
          try {
            const response = await axios.get(monitor.target, {
              signal: controller.signal,
              timeout: monitor.timeout * 1000,
              validateStatus: null // Don't throw on non-2xx responses
            });
            
            clearTimeout(timeoutId);
            status = response.status >= 200 && response.status < 300;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              errorMessage = `HTTP status: ${response.status}`;
            }
          } catch (error) {
            clearTimeout(timeoutId);
            throw error;
          }
          break;
          
        case 'ping':
          // Simple ping check (using HTTP HEAD request as a proxy)
          try {
            const response = await axios.head(monitor.target, {
              timeout: monitor.timeout * 1000,
              validateStatus: null
            });
            
            status = response.status >= 200 && response.status < 300;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              errorMessage = `Ping failed with status: ${response.status}`;
            }
          } catch (error) {
            throw new Error(`Ping failed: ${error.message}`);
          }
          break;
          
        case 'port':
          // Port check
          try {
            // Parse the target (format: host|port1,port2,port3)
            const [host, portsStr] = monitor.target.split('|');
            const ports = portsStr.split(',').map(p => parseInt(p.trim()));
            
            // Check each port
            let anyPortOpen = false;
            let portResults = [];
            
            for (const port of ports) {
              try {
                // For HTTP/HTTPS ports, we can use a direct request
                if (port === 80 || port === 443) {
                  const protocol = port === 443 ? 'https' : 'http';
                  const url = `${protocol}://${host}`;
                  
                  try {
                    const response = await axios.head(url, {
                      timeout: 5000,
                      validateStatus: null
                    });
                    
                    // If we get any response, the port is open
                    anyPortOpen = true;
                    portResults.push({ port, status: true });
                  } catch (error) {
                    // Some errors actually indicate the port is open
                    if (error.code === 'ECONNREFUSED') {
                      portResults.push({ port, status: false, error: 'Connection refused' });
                    } else {
                      // For other errors, the port might still be open
                      anyPortOpen = true;
                      portResults.push({ port, status: true, error: error.message });
                    }
                  }
                } else {
                  // For other ports, we'll just check if the host is reachable
                  // This is a simplification - in a real implementation, you'd use a TCP socket
                  try {
                    const response = await axios.head(`https://${host}`, {
                      timeout: 5000,
                      validateStatus: null
                    });
                    
                    // If the host is reachable, we'll assume the port is open
                    // This is not accurate but the best we can do without TCP sockets
                    portResults.push({ port, status: true });
                    anyPortOpen = true;
                  } catch (error) {
                    portResults.push({ port, status: false, error: error.message });
                  }
                }
              } catch (error) {
                portResults.push({ port, status: false, error: error.message });
              }
            }
            
            // Overall status is true if any port is open
            status = anyPortOpen;
            responseTime = Date.now() - startTime;
            
            if (!status) {
              const failedPorts = portResults
                .filter(r => !r.status)
                .map(r => r.port)
                .join(', ');
              errorMessage = `All ports failed (${failedPorts})`;
            }
          } catch (error) {
            throw new Error(`Port check failed: ${error.message}`);
          }
          break;
          
        default:
          throw new Error(`Unsupported monitor type: ${monitor.type}`);
      }
    } catch (error) {
      status = false;
      errorMessage = `Error: ${error.message}`;
      console.error(`Check failed: ${error.message}`);
    }
    
    // Save the check result
    const checkResult = {
      monitor_id: monitor.id,
      status,
      response_time: responseTime,
      error_message: errorMessage,
      timestamp: new Date().toISOString()
    };
    
    console.log(`Check result for ${monitor.name}: ${status ? 'UP' : 'DOWN'} (${responseTime}ms)`);
    if (errorMessage) {
      console.log(`Error: ${errorMessage}`);
    }
    
    // Insert the result into the database
    const { error: insertError } = await supabase
      .from('monitor_history')
      .insert(checkResult);
    
    if (insertError) {
      throw new Error(`Failed to save check result: ${insertError.message}`);
    }
    
    console.log('Check result saved to database');
    
    return {
      monitor_id: monitor.id,
      name: monitor.name,
      status,
      response_time: responseTime,
      error_message: errorMessage
    };
  } catch (error) {
    console.error(`Error checking monitor: ${error.message}`);
    return {
      monitor_id: monitor.id,
      name: monitor.name || 'Unknown',
      status: false,
      response_time: 0,
      error_message: `Error: ${error.message}`
    };
  }
}

// API endpoint for checking a specific monitor
app.post('/check-monitor', async (req, res) => {
  try {
    const { monitorId } = req.body;
    
    if (!monitorId) {
      return res.status(400).json({
        success: false,
        error: 'Monitor ID is required'
      });
    }
    
    // Get the monitor
    const { data: monitor, error: monitorError } = await supabase
      .from('monitors')
      .select('*')
      .eq('id', monitorId)
      .single();
    
    if (monitorError) {
      return res.status(500).json({
        success: false,
        error: `Failed to get monitor: ${monitorError.message}`
      });
    }
    
    if (!monitor) {
      return res.status(404).json({
        success: false,
        error: `Monitor with ID ${monitorId} not found`
      });
    }
    
    // Check the monitor
    const result = await checkMonitor(monitor);
    
    return res.json({
      success: true,
      checksRun: 1,
      results: [result]
    });
  } catch (error) {
    console.error(`Error checking monitor: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// API endpoint for checking all monitors
app.post('/check-all-monitors', async (req, res) => {
  try {
    // Get all active monitors
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('*')
      .eq('active', true);
    
    if (monitorsError) {
      return res.status(500).json({
        success: false,
        error: `Failed to get monitors: ${monitorsError.message}`
      });
    }
    
    if (!monitors || monitors.length === 0) {
      return res.json({
        success: true,
        checksRun: 0,
        results: []
      });
    }
    
    console.log(`Found ${monitors.length} active monitors`);
    
    // Check each monitor
    const results = [];
    for (const monitor of monitors) {
      try {
        const result = await checkMonitor(monitor);
        results.push(result);
      } catch (error) {
        console.error(`Error checking monitor ${monitor.name}: ${error.message}`);
        results.push({
          monitor_id: monitor.id,
          name: monitor.name,
          status: false,
          response_time: 0,
          error_message: `Error: ${error.message}`
        });
      }
    }
    
    return res.json({
      success: true,
      checksRun: results.length,
      results
    });
  } catch (error) {
    console.error(`Error checking monitors: ${error.message}`);
    return res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Start the server
app.listen(port, () => {
  console.log(`Check Now API running on port ${port}`);
});
