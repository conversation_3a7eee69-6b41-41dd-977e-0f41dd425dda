import React from 'react';
import { useCompanies } from '@/hooks/use-companies';
import { useCompany } from '@/contexts/CompanyContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Plus, Edit, Trash2, Users } from 'lucide-react';

/**
 * Example component showing how to use the new React Query-based company hooks
 * This replaces the old pattern of calling methods directly on the CompanyContext
 */
export const CompanyManagementExample: React.FC = () => {
  const { currentCompany, setCurrentCompany, isAdmin } = useCompany();
  const {
    useGetCompaniesQuery,
    useGetCompanyMembersQuery,
    useCreateCompanyMutation,
    useUpdateCompanyMutation,
    useDeleteCompanyMutation,
    useAddCompanyMemberMutation,
    useUpdateCompanyMemberMutation,
    useRemoveCompanyMemberMutation,
  } = useCompanies();

  // Get companies with loading and error states
  const {
    data: companies = [],
    isLoading: companiesLoading,
    error: companiesError,
    refetch: refetchCompanies
  } = useGetCompaniesQuery();

  // Get company members with loading and error states
  const {
    data: companyMembers = [],
    isLoading: membersLoading,
    error: membersError,
    refetch: refetchMembers
  } = useGetCompanyMembersQuery(currentCompany?.id || '');

  // Mutations with built-in loading states and error handling
  const createCompanyMutation = useCreateCompanyMutation();
  const updateCompanyMutation = useUpdateCompanyMutation();
  const deleteCompanyMutation = useDeleteCompanyMutation();
  const addMemberMutation = useAddCompanyMemberMutation();
  const updateMemberMutation = useUpdateCompanyMemberMutation();
  const removeMemberMutation = useRemoveCompanyMemberMutation();

  // Example handlers
  const handleCreateCompany = () => {
    createCompanyMutation.mutate({
      name: 'New Company',
      description: 'A new company created via React Query'
    });
  };

  const handleUpdateCompany = () => {
    if (!currentCompany) return;
    
    updateCompanyMutation.mutate({
      id: currentCompany.id,
      data: {
        name: currentCompany.name + ' (Updated)',
        description: 'Updated via React Query'
      }
    });
  };

  const handleDeleteCompany = () => {
    if (!currentCompany) return;
    
    if (confirm(`Are you sure you want to delete ${currentCompany.name}?`)) {
      deleteCompanyMutation.mutate(currentCompany.id);
    }
  };

  const handleAddMember = () => {
    if (!currentCompany) return;
    
    // This would typically come from a form
    const userId = prompt('Enter user ID to add:');
    if (userId) {
      addMemberMutation.mutate({
        company_id: currentCompany.id,
        user_id: userId,
        role_type: 'user'
      });
    }
  };

  if (companiesError) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-red-600">
            Error loading companies: {companiesError.message}
            <Button onClick={() => refetchCompanies()} className="ml-2">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Companies Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Companies
            {isAdmin && (
              <Button
                onClick={handleCreateCompany}
                disabled={createCompanyMutation.isPending}
                size="sm"
              >
                {createCompanyMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
                Create Company
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {companiesLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading companies...</span>
            </div>
          ) : (
            <div className="space-y-2">
              {companies.map((company) => (
                <div
                  key={company.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    currentCompany?.id === company.id
                      ? 'bg-blue-50 border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setCurrentCompany(company)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">{company.name}</h3>
                      {company.description && (
                        <p className="text-sm text-gray-600">{company.description}</p>
                      )}
                    </div>
                    {currentCompany?.id === company.id && (
                      <Badge variant="secondary">Current</Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Current Company Actions */}
      {currentCompany && isAdmin && (
        <Card>
          <CardHeader>
            <CardTitle>Company Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              <Button
                onClick={handleUpdateCompany}
                disabled={updateCompanyMutation.isPending}
                variant="outline"
                size="sm"
              >
                {updateCompanyMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Edit className="h-4 w-4" />
                )}
                Update Company
              </Button>
              <Button
                onClick={handleDeleteCompany}
                disabled={deleteCompanyMutation.isPending}
                variant="destructive"
                size="sm"
              >
                {deleteCompanyMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Trash2 className="h-4 w-4" />
                )}
                Delete Company
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Company Members Section */}
      {currentCompany && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Members of {currentCompany.name}
              </span>
              {isAdmin && (
                <Button
                  onClick={handleAddMember}
                  disabled={addMemberMutation.isPending}
                  size="sm"
                >
                  {addMemberMutation.isPending ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Plus className="h-4 w-4" />
                  )}
                  Add Member
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {membersError ? (
              <div className="text-red-600">
                Error loading members: {membersError.message}
                <Button onClick={() => refetchMembers()} className="ml-2" size="sm">
                  Retry
                </Button>
              </div>
            ) : membersLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading members...</span>
              </div>
            ) : (
              <div className="space-y-2">
                {companyMembers.map((member) => (
                  <div
                    key={member.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div>
                      <p className="font-medium">
                        {member.full_name || member.email || 'Unknown User'}
                      </p>
                      <p className="text-sm text-gray-600">{member.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{member.role_type}</Badge>
                      {isAdmin && (
                        <Button
                          onClick={() => {
                            if (confirm('Remove this member?')) {
                              removeMemberMutation.mutate({
                                id: member.id,
                                companyId: member.company_id
                              });
                            }
                          }}
                          disabled={removeMemberMutation.isPending}
                          variant="destructive"
                          size="sm"
                        >
                          {removeMemberMutation.isPending ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                {companyMembers.length === 0 && (
                  <p className="text-gray-500 text-center py-4">
                    No members found for this company
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};
