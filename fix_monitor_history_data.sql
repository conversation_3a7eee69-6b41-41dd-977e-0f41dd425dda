-- This script fixes the monitor_history table data and constraints
-- Run this in the Supabase SQL Editor

-- First, let's check the structure of the monitor_history table
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'monitor_history'
ORDER BY 
    ordinal_position;

-- Check the constraints on the monitor_history table
SELECT 
    con.conname AS constraint_name,
    con.contype AS constraint_type,
    pg_get_constraintdef(con.oid) AS constraint_definition
FROM 
    pg_constraint con
    JOIN pg_class rel ON rel.oid = con.conrelid
    JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
WHERE 
    nsp.nspname = 'public'
    AND rel.relname = 'monitor_history';

-- Check a sample of records to see the actual data
SELECT 
    id, 
    monitor_id, 
    status, 
    pg_typeof(status) AS status_type,
    response_time,
    error_message,
    timestamp
FROM 
    public.monitor_history
ORDER BY timestamp DESC
LIMIT 10;

-- Drop the existing constraint if it exists
ALTER TABLE public.monitor_history DROP CONSTRAINT IF EXISTS valid_status;

-- Check for invalid status values
SELECT 
    status, 
    COUNT(*) 
FROM 
    public.monitor_history 
GROUP BY 
    status;

-- Fix the data based on the data type
DO $$
DECLARE
    status_type text;
BEGIN
    -- Get the data type of the status column
    SELECT data_type INTO status_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'monitor_history'
    AND column_name = 'status';
    
    RAISE NOTICE 'Status column is type: %', status_type;
    
    -- Fix the data based on the type
    IF status_type = 'text' THEN
        -- Update any NULL values to 'false'
        UPDATE public.monitor_history
        SET status = 'false'
        WHERE status IS NULL;
        
        -- Update any non-boolean text values to 'true' or 'false'
        UPDATE public.monitor_history
        SET status = 
            CASE 
                WHEN status IN ('1', 'yes', 'up', 'online', 'success', 'ok') THEN 'true'
                ELSE 'false'
            END
        WHERE status NOT IN ('true', 'false');
        
        RAISE NOTICE 'Fixed text status values';
    ELSIF status_type = 'boolean' THEN
        -- Update any NULL values to false
        UPDATE public.monitor_history
        SET status = false
        WHERE status IS NULL;
        
        RAISE NOTICE 'Fixed boolean status values';
    ELSE
        RAISE NOTICE 'Unknown status type: %. Manual fix needed.', status_type;
    END IF;
END $$;

-- Check if we need to convert the column type
DO $$
DECLARE
    status_type text;
BEGIN
    -- Get the data type of the status column
    SELECT data_type INTO status_type
    FROM information_schema.columns
    WHERE table_schema = 'public' 
    AND table_name = 'monitor_history'
    AND column_name = 'status';
    
    -- If it's text, convert it to boolean
    IF status_type = 'text' THEN
        -- Convert text to boolean
        ALTER TABLE public.monitor_history ALTER COLUMN status TYPE boolean USING 
            CASE 
                WHEN status = 'true' THEN true
                ELSE false
            END;
            
        RAISE NOTICE 'Converted status column from text to boolean';
    ELSE
        RAISE NOTICE 'Status column is already type: %', status_type;
    END IF;
END $$;

-- Add the appropriate constraint
ALTER TABLE public.monitor_history ADD CONSTRAINT valid_status CHECK (status IS NOT NULL);

-- Check if we have any monitor history records
SELECT COUNT(*) AS total_records FROM public.monitor_history;

-- Check the distribution of status values
SELECT status, COUNT(*) FROM public.monitor_history GROUP BY status;

-- Create a function to get monitor status counts
CREATE OR REPLACE FUNCTION get_monitor_status_counts()
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    up_count INTEGER;
    down_count INTEGER;
    degraded_count INTEGER;
    paused_count INTEGER;
BEGIN
    -- Count active monitors that are up (latest status is true)
    SELECT COUNT(*) INTO up_count
    FROM public.monitors m
    JOIN (
        SELECT DISTINCT ON (monitor_id) monitor_id, status
        FROM public.monitor_history
        ORDER BY monitor_id, timestamp DESC
    ) h ON m.id = h.monitor_id
    WHERE m.active = true AND h.status = true;
    
    -- Count active monitors that are down (latest status is false)
    SELECT COUNT(*) INTO down_count
    FROM public.monitors m
    JOIN (
        SELECT DISTINCT ON (monitor_id) monitor_id, status
        FROM public.monitor_history
        ORDER BY monitor_id, timestamp DESC
    ) h ON m.id = h.monitor_id
    WHERE m.active = true AND h.status = false;
    
    -- Count degraded monitors (this depends on your degraded logic)
    -- For now, we'll set it to 0 since we need to implement the degraded logic
    degraded_count := 0;
    
    -- Count paused monitors (active = false)
    SELECT COUNT(*) INTO paused_count
    FROM public.monitors
    WHERE active = false;
    
    -- Build the result JSON
    result := jsonb_build_object(
        'up', up_count,
        'down', down_count,
        'degraded', degraded_count,
        'paused', paused_count
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Test the function
SELECT get_monitor_status_counts();

-- Create a view for easy access to monitor statuses
CREATE OR REPLACE VIEW monitor_status_summary AS
WITH latest_checks AS (
    SELECT DISTINCT ON (monitor_id) 
        monitor_id,
        status,
        timestamp
    FROM 
        public.monitor_history
    ORDER BY 
        monitor_id, 
        timestamp DESC
)
SELECT 
    m.id,
    m.name,
    m.active,
    CASE
        WHEN m.active = false THEN 'paused'
        WHEN lc.status IS NULL THEN 'unknown'
        WHEN lc.status = true THEN 'up'
        ELSE 'down'
    END AS status,
    lc.timestamp AS last_checked
FROM 
    public.monitors m
LEFT JOIN 
    latest_checks lc ON m.id = lc.monitor_id;

-- Test the view
SELECT * FROM monitor_status_summary;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
