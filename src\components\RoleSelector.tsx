import React, { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, Shield, ShieldAlert, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { UserRole } from '@/types/company';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { supabase } from '@/integrations/supabase/client';

interface RoleSelectorProps {
  userId: string;
  companyId: string;
  currentRole: UserRole;
  disabled?: boolean;
  className?: string;
  isGlobalSuperadmin?: boolean;
}

// Default roles
const defaultRoles: { value: UserRole; label: string; icon: React.ReactNode }[] = [
  {
    value: 'user',
    label: 'User',
    icon: <User className="mr-2 h-4 w-4" />,
  },
  {
    value: 'admin',
    label: 'Admin',
    icon: <Shield className="mr-2 h-4 w-4" />,
  },
  {
    value: 'superadmin',
    label: 'Superadmin',
    icon: <ShieldAlert className="mr-2 h-4 w-4" />,
  },
];

export function RoleSelector({
  userId,
  companyId,
  currentRole,
  disabled = false,
  className,
  isGlobalSuperadmin = false,
}: RoleSelectorProps) {
  const [open, setOpen] = useState(false);
  const [role, setRole] = useState<UserRole>(currentRole);
  const [roles, setRoles] = useState(defaultRoles);
  const { useUpdateUserRoleMutation, useGlobalSuperadminQuery } = useCompanyRoles();
  const updateRole = useUpdateUserRoleMutation();
  const { data: isCurrentUserGlobalSuperadmin } = useGlobalSuperadminQuery();

  // Load roles from the database
  useEffect(() => {
    const loadRoles = async () => {
      try {
        const { data, error } = await supabase.rpc('get_available_roles');

        if (error) {
          console.error('Error loading roles:', error);
          return;
        }

        // Transform the data into role options
        const roleOptions = data
          .filter((role: any) => {
            // Only show superadmin if the current user is a global superadmin
            if (role.role_type === 'superadmin') {
              return isCurrentUserGlobalSuperadmin;
            }
            return true;
          })
          .map((role: any) => ({
            value: role.role_type as UserRole,
            label: role.display_name,
            icon: role.role_type === 'admin' ? <Shield className="mr-2 h-4 w-4" /> :
                  role.role_type === 'superadmin' ? <ShieldAlert className="mr-2 h-4 w-4" /> :
                  <User className="mr-2 h-4 w-4" />
          }));

        setRoles(roleOptions);
      } catch (err) {
        console.error('Error loading roles:', err);
      }
    };

    loadRoles();
  }, [isCurrentUserGlobalSuperadmin]);

  // Update the role state when the currentRole prop changes
  useEffect(() => {
    setRole(currentRole);
  }, [currentRole]);

  const handleSelectRole = (value: UserRole) => {
    if (value === role) {
      setOpen(false);
      return;
    }

    // Update the role in the database
    updateRole.mutate(
      {
        userId,
        companyId,
        role_type: value,
        isGlobal: value === 'superadmin' && isCurrentUserGlobalSuperadmin
      },
      {
        onSuccess: () => {
          setRole(value);
          setOpen(false);
        },
      }
    );
  };

  const currentRoleData = roles.find((r) => r.value === role);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-[200px] justify-between", className)}
          disabled={disabled || updateRole.isPending}
        >
          <div className="flex items-center">
            {currentRoleData?.icon}
            <span>{currentRoleData?.label}</span>
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search role..." />
          <CommandEmpty>No role found.</CommandEmpty>
          <CommandGroup>
            {roles.map((item) => (
              <CommandItem
                key={item.value}
                value={item.value}
                onSelect={() => handleSelectRole(item.value as UserRole)}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    role === item.value ? "opacity-100" : "opacity-0"
                  )}
                />
                <div className="flex items-center">
                  {item.icon}
                  {item.label}
                </div>
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
