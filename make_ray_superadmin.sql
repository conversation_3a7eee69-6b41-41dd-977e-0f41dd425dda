-- Script <NAME_EMAIL> a global superadmin
-- Run this in the Supabase SQL Editor

-- First, find the user <NAME_EMAIL>
DO $$
DECLARE
    target_user_id UUID;
BEGIN
    -- Get the user ID from auth.users
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = '<EMAIL>';
    
    -- Check if the user exists
    IF target_user_id IS NULL THEN
        RAISE EXCEPTION 'User <NAME_EMAIL> not found';
    END IF;
    
    -- Insert the user as a global superadmin
    -- This uses the ON CONFLICT clause to handle the case where the user is already a superadmin
    INSERT INTO public.user_roles (user_id, role_type)
    VALUES (target_user_id, 'superadmin')
    ON CONFLICT (user_id, role_type) 
    DO UPDATE SET updated_at = NOW();
    
    RAISE NOTICE 'User <EMAIL> (ID: %) has been made a global superadmin', target_user_id;
END $$;

-- Verify the user is now a superadmin
SELECT 
    u.id,
    u.email,
    ur.role_type AS global_role,
    ur.created_at,
    ur.updated_at
FROM 
    auth.users u
JOIN 
    public.user_roles ur ON u.id = ur.user_id
WHERE 
    u.email = '<EMAIL>'
    AND ur.role_type = 'superadmin';
