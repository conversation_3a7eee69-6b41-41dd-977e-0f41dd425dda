-- Create a function to get all admin emails for a company
CREATE OR REPLACE FUNCTION get_company_admin_emails(company_id UUID)
RETURNS TABLE (email TEXT) 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT u.email
  FROM auth.users u
  JOIN company_members cm ON u.id = cm.user_id
  WHERE cm.company_id = get_company_admin_emails.company_id
  AND cm.role_type = 'admin';
END;
$$;

-- Create a function to trigger email notifications when monitor status changes
CREATE OR REPLACE FUNCTION notify_monitor_status_change()
RETURNS TRIGGER
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
DECLARE
  previous_status TEXT;
  monitor_record RECORD;
  company_record RECORD;
BEGIN
  -- Get the previous status for this monitor
  SELECT status INTO previous_status
  FROM monitor_history
  WHERE monitor_id = NEW.monitor_id
  AND id != NEW.id
  ORDER BY timestamp DESC
  LIMIT 1;
  
  -- If this is the first check or status has changed
  IF previous_status IS NULL OR previous_status != NEW.status THEN
    -- Get monitor details
    SELECT * INTO monitor_record
    FROM monitors
    WHERE id = NEW.monitor_id;
    
    -- For each company associated with this monitor
    FOR company_record IN 
      SELECT company_id
      FROM monitor_companies
      WHERE monitor_id = NEW.monitor_id
    LOOP
      -- Insert a notification record
      INSERT INTO notifications (
        monitor_id,
        company_id,
        message,
        type,
        read,
        created_at
      ) VALUES (
        NEW.monitor_id,
        company_record.company_id,
        CASE 
          WHEN NEW.status = 'up' THEN 'Monitor ' || monitor_record.name || ' is now operational'
          WHEN NEW.status = 'down' THEN 'Monitor ' || monitor_record.name || ' is down'
          ELSE 'Monitor ' || monitor_record.name || ' is experiencing degraded performance'
        END,
        CASE 
          WHEN NEW.status = 'up' THEN 'up'
          WHEN NEW.status = 'down' THEN 'down'
          ELSE 'other'
        END,
        false,
        NOW()
      );
      
      -- Call the edge function to send email (this will be handled by a separate process)
      PERFORM pg_notify(
        'monitor_status_change',
        json_build_object(
          'monitor_id', NEW.monitor_id,
          'status', NEW.status,
          'company_id', company_record.company_id
        )::text
      );
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create a trigger to call the notification function when a new monitor history record is inserted
DROP TRIGGER IF EXISTS monitor_status_change_trigger ON monitor_history;
CREATE TRIGGER monitor_status_change_trigger
AFTER INSERT ON monitor_history
FOR EACH ROW
EXECUTE FUNCTION notify_monitor_status_change();
