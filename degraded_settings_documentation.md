# Degraded Settings Documentation

This document explains the degraded settings system in the Vurbis Uptime Monitor application.

## Overview

The degraded settings system allows you to define thresholds that determine when a service is considered "degraded" rather than fully operational or completely down. This provides a more nuanced view of service health.

## Global vs. Monitor-Specific Settings

The system supports two levels of settings:

1. **Global Settings**: Default thresholds that apply to all monitors unless overridden.
2. **Monitor-Specific Settings**: Custom thresholds for individual monitors that override the global defaults.

## Database Structure

### Tables

1. **degraded_settings**: Stores global default thresholds.
2. **monitor_degraded_settings**: Stores monitor-specific threshold overrides.

### Functions

1. **get_monitor_degraded_settings(monitor_id)**: Returns the effective settings for a monitor, combining global defaults with any monitor-specific overrides.
2. **upsert_monitor_degraded_settings(...)**: Creates or updates monitor-specific settings.

## Threshold Fields

### Response Time (response_time)

- **Type**: Integer (milliseconds)
- **Purpose**: Defines the maximum acceptable response time before a service is considered degraded.
- **Example**: If set to 1000, any response taking longer than 1000ms (1 second) will be considered degraded.
- **Default**: 1000ms

### Error Rate (error_rate)

- **Type**: Integer (percentage, 0-100)
- **Purpose**: Defines the maximum acceptable error rate before a service is considered degraded.
- **Example**: If set to 10, a service with more than 10% of requests resulting in errors will be considered degraded.
- **Default**: 10%

### Status Codes (status_codes)

- **Type**: Array of Integers
- **Purpose**: Defines HTTP status codes that indicate a degraded state rather than a complete failure.
- **Common Values**:
  - 429: Too Many Requests (rate limiting)
  - 503: Service Unavailable (temporary overload)
  - 507: Insufficient Storage
  - 509: Bandwidth Limit Exceeded
- **Default**: [429, 503]

### Consecutive Failures (consecutive_failures)

- **Type**: Integer
- **Purpose**: Defines how many consecutive partial failures must occur before a service is considered degraded.
- **Example**: If set to 2, a service must fail the degraded criteria twice in a row to be marked as degraded.
- **Default**: 2

## How Degraded Status is Determined

A monitor is considered degraded if ANY of the following conditions are met:

1. Response time exceeds the threshold
2. Error rate exceeds the threshold
3. The HTTP status code is in the list of degraded status codes
4. The number of consecutive partial failures exceeds the threshold

## User Interface

- **Global Settings**: Accessible to superadmins via the "Global Degraded Service Settings" page.
- **Monitor-Specific Settings**: Available in the monitor edit form, visible only to superadmins.

## Best Practices

1. **Response Time**: Set based on your service's performance characteristics. Critical services might need lower thresholds.
2. **Error Rate**: 5-10% is common for most services. Mission-critical services might use lower values.
3. **Status Codes**: Include codes that indicate temporary issues, not permanent failures.
4. **Consecutive Failures**: Use 2-3 to avoid false positives from temporary network glitches.

## Troubleshooting

If a monitor is incorrectly showing as degraded:

1. Check which threshold is being triggered (response time, error rate, etc.)
2. Adjust the relevant threshold if needed
3. Consider using monitor-specific settings for services with unique performance characteristics
