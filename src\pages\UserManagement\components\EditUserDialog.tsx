import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface EditUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editUserEmail: string;
  setEditUserEmail: (email: string) => void;
  editUserFullName: string;
  setEditUserFullName: (name: string) => void;
  handleEditUser: () => Promise<void>;
  isPending: boolean;
}

const EditUserDialog: React.FC<EditUserDialogProps> = ({
  open,
  onOpenChange,
  editUserEmail,
  setEditUserEmail,
  editUserFullName,
  setEditUserFullName,
  handleEditUser,
  isPending,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user information
          </DialogDescription>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="editEmail">Email</Label>
            <Input
              id="editEmail"
              type="email"
              value={editUserEmail}
              onChange={(e) => setEditUserEmail(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="editFullName">Full Name</Label>
            <Input
              id="editFullName"
              value={editUserFullName}
              onChange={(e) => setEditUserFullName(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <Button
            onClick={handleEditUser}
            disabled={isPending}
          >
            {isPending && (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            )}
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditUserDialog;
