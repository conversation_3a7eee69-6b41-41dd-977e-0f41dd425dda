# Welcome to your Lovable project

## Project info

**URL**: https://lovable.dev/projects/a3f844fd-1c3f-41f3-91a7-6c2854df542d

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/a3f844fd-1c3f-41f3-91a7-6c2854df542d) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Node.js
- React
- Vite
- TypeScript
- Supabase
- dotenv
- node-cron

## Supabase Configuration

This project uses Supabase for authentication, database, and storage. The Supabase client is configured in `src/integrations/supabase/client.ts`.

### Environment Variables

The Supabase client uses the following environment variables:

- `VITE_SUPABASE_URL`: The URL of your Supabase project
- `VITE_SUPABASE_ANON_KEY`: The anonymous key for your Supabase project
- `SUPABASE_SERVICE_KEY`: The service role key for your Supabase project (used by the monitor service)

For local development, create a `.env.local` file based on the `.env.local.example` template.

### Security Best Practices

This project follows these security best practices:

1. **No Hardcoded Credentials**: All sensitive information is stored in environment variables, never hardcoded in the source code.
2. **Development Fallbacks**: For development, fallback values are provided if environment variables aren't set, making it easier to get started.
3. **Production Requirements**: For production, all environment variables must be properly set - fallbacks are not used.
4. **Credential Scanning**: Run `npm run check-credentials` to scan the codebase for potentially hardcoded credentials.
5. **Pre-commit Hook**: The credential check runs automatically before each commit to prevent accidental credential exposure.
6. **Documentation**: Comprehensive documentation on environment variables and security practices is available in the `docs` directory.

To set up your environment securely:

```sh
# Create your local environment file
npm run setup

# Check for any hardcoded credentials
npm run check-credentials
```

## Security Features

### Rate Limiting

The system implements rate limiting to prevent abuse:
- 100 requests per IP per 15 minutes
- Whitelisted IPs are exempt from rate limiting
- Custom error messages for rate-limited requests

### Request Logging

All requests are logged for security and auditing:
- Stored in Supabase `request_logs` table
- Backup logging to local `requests.log` file
- Automatic cleanup of logs older than 30 days
- Includes: timestamp, method, URL, IP, response time, status code

### IP Whitelisting

Sensitive operations require IP whitelisting:
```sh
# Add IP to whitelist
node manage_whitelist.js add <ip>

# Remove IP from whitelist
node manage_whitelist.js remove <ip>
```

### Key Rotation Service

This project includes an automated key rotation service for enhanced security. The service:

- Automatically rotates Supabase service keys every 30 days
- Maintains a backup of the previous key during rotation
- Validates new keys before committing the change
- Logs all rotation events

### Running the Key Rotation Service

```sh
# Install dependencies
npm install

# Start the key rotation service
node key_rotation_service.js

# To trigger immediate key rotation
node key_rotation_service.js --rotate-now
```

### Key Rotation Logs

Rotation events are logged to `key_rotation.log` in JSON format. Each log entry contains:
- Timestamp
- Success/failure status
- Error message (if applicable)

### Environment Variables

The service requires the following environment variables in `.env`:
- `SUPABASE_PROJECT_REF`: Your Supabase project reference
- `SUPABASE_SERVICE_KEY`: Your current Supabase service key
- `SUPABASE_URL`: Your Supabase project URL

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/a3f844fd-1c3f-41f3-91a7-6c2854df542d) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes it is!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
