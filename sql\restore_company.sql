-- Create a function to restore a soft-deleted company
CREATE OR R<PERSON>LACE FUNCTION restore_company(
    company_id UUID,
    user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    company_data JSONB;
    is_superadmin BOOLEAN;
BEGIN
    -- Check if the company exists and is deleted
    SELECT to_jsonb(c) INTO company_data
    FROM companies c
    WHERE id = company_id AND deleted = TRUE;
    
    -- If company doesn't exist or is not deleted
    IF company_data IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if the user is a global superadmin
    SELECT is_global_superadmin(user_id) INTO is_superadmin;
    
    -- Only allow superadmins to restore companies
    IF NOT is_superadmin THEN
        RAISE EXCEPTION 'User does not have permission to restore this company';
    END IF;
    
    -- Restore the company
    UPDATE companies
    SET 
        deleted = FALSE,
        deleted_at = NULL,
        deleted_by = NULL
    WHERE
        id = company_id
        AND deleted = TRUE;
    
    -- Log the action
    PERFORM log_action(
        user_id,
        'restore',
        'companies',
        company_id,
        company_data,
        NULL
    );
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
