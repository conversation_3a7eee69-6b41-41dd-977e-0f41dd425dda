-- Drop existing RLS policies on the users table
DROP POLICY IF EXISTS "Users can view their own user data" ON public.users;
DROP POLICY IF EXISTS "Users can update their own data" ON public.users;
DROP POLICY IF EXISTS "Superadmins can view all users" ON public.users;
DROP POLICY IF EXISTS "Superadmins can update all users" ON public.users;
DROP POLICY IF EXISTS "Superadmins can insert new users" ON public.users;
DROP POLICY IF EXISTS "Superadmins can delete users" ON public.users;

-- Create RLS policies for the users table
-- 1. Users can view their own user data
CREATE POLICY "Users can view their own user data"
ON public.users
FOR SELECT
USING (auth.uid() = id);

-- 2. Users can update their own data
CREATE POLICY "Users can update their own data"
ON public.users
FOR UPDATE
USING (auth.uid() = id);

-- 3. Superadmins can view all users
CREATE POLICY "Superadmins can view all users"
ON public.users
FOR SELECT
USING (is_global_superadmin_bypass());

-- 4. <PERSON><PERSON><PERSON> can update all users
CREATE POLICY "Superadmins can update all users"
ON public.users
FOR UPDATE
USING (is_global_superadmin_bypass());

-- 5. <PERSON><PERSON><PERSON> can insert new users
CREATE POLICY "Superadmins can insert new users"
ON public.users
FOR INSERT
WITH CHECK (is_global_superadmin_bypass());

-- 6. Superadmins can delete users
CREATE POLICY "Superadmins can delete users"
ON public.users
FOR DELETE
USING (is_global_superadmin_bypass());

-- Make sure RLS is enabled on the users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
