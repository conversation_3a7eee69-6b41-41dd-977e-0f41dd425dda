# Comprehensive Degraded Status Handling Fix

## Overview

This document outlines the comprehensive fix for degraded status handling in the VUM (Vurbis Uptime Monitor) system. The fix addresses several issues with how degraded statuses are detected and ensures proper fallback from custom monitor settings to global thresholds.

## Issues Identified

### 1. Missing Database Functions
- `get_monitor_degraded_settings_rpc` - Referenced in UI code but didn't exist
- `get_monitors_with_degraded_settings` - Used for checking which monitors have custom settings

### 2. Incomplete Degraded Logic
- Monitor checker only checked response time thresholds
- Ignored other degraded criteria: error rates, status codes, consecutive failures
- No comprehensive status evaluation

### 3. Inconsistent Function Usage
- Some code called `get_monitor_degraded_settings`
- Other parts called `get_monitor_degraded_settings_rpc`
- No unified approach to degraded status detection

## Solutions Implemented

### 1. Database Functions Created

#### `get_monitor_degraded_settings_rpc(p_monitor_id UUID)`
- Alias for `get_monitor_degraded_settings` for UI compatibility
- Ensures consistent function naming across the application

#### `get_monitors_with_degraded_settings(p_monitor_ids UUID[])`
- Returns monitor IDs that have custom degraded settings
- Used by UI to show which monitors have custom configurations

#### `is_monitor_degraded(p_monitor_id, p_response_time, p_status_code, p_consecutive_failures, p_error_rate)`
- Comprehensive degraded status checking
- Evaluates all degraded criteria:
  - Response time thresholds
  - HTTP status codes (429, 503, etc.)
  - Consecutive failure counts
  - Error rate percentages
- Returns detailed information about why a monitor is degraded

#### `get_monitor_consecutive_failures(p_monitor_id UUID)`
- Calculates consecutive failures from monitor history
- Looks at recent check results to determine failure streaks

#### `get_monitor_error_rate(p_monitor_id, p_time_window_minutes)`
- Calculates error rate percentage over a time window
- Default 60-minute window for error rate calculation

#### `evaluate_monitor_status(p_monitor_id, p_current_status, p_response_time, p_status_code)`
- Comprehensive monitor status evaluation
- Combines all degraded criteria into final status determination
- Returns 'up', 'down', or 'degraded' with detailed reasoning

### 2. Monitor Checker Updates

#### Enhanced Status Evaluation
- Replaced simple response time checking with comprehensive evaluation
- Uses `evaluate_monitor_status` function for complete analysis
- Provides detailed logging of degraded reasons

#### Fallback Handling
- Graceful error handling if database functions fail
- Falls back to simple up/down status determination
- Maintains system stability even if degraded logic fails

### 3. TypeScript Utility Updates

#### Enhanced `isMonitorDegraded` Function
- Now returns comprehensive degraded information
- Includes reasons for degraded status
- Provides fallback logic for error cases

#### New `evaluateMonitorStatus` Function
- TypeScript wrapper for database function
- Provides type-safe interface for status evaluation
- Handles errors gracefully with fallbacks

## Custom vs Global Settings Logic

### How It Works

1. **Check for Custom Settings**
   - `get_monitor_degraded_settings` first looks for monitor-specific settings
   - If found, uses those values for the monitor

2. **Fallback to Global Settings**
   - If no custom settings exist, uses global degraded settings
   - Global settings are stored in `degraded_settings` table

3. **Field-Level Fallback**
   - Each degraded threshold field can be customized independently
   - If a custom setting has NULL for a field, uses global value for that field
   - Allows partial customization (e.g., only custom response time threshold)

4. **Default Values**
   - If no global settings exist, uses hardcoded defaults:
     - Response time: 1000ms
     - Error rate: 10%
     - Status codes: [429, 503]
     - Consecutive failures: 2

### Example Scenarios

#### Scenario 1: No Custom Settings
- Monitor uses global settings entirely
- All thresholds come from `degraded_settings` table

#### Scenario 2: Partial Custom Settings
- Monitor has custom response time (2000ms) but NULL for other fields
- Uses custom response time (2000ms) and global values for error rate, status codes, consecutive failures

#### Scenario 3: Full Custom Settings
- Monitor has all custom values specified
- Uses only custom settings, ignores global settings

## Testing

### Test Script: `test_degraded_status_handling.js`

The test script verifies:
1. All database functions exist and work correctly
2. Global degraded settings are properly configured
3. Monitor-specific settings retrieval works
4. Degraded status detection works for various scenarios
5. Comprehensive status evaluation functions correctly
6. Custom settings detection works

### Running Tests

```bash
node test_degraded_status_handling.js
```

## Deployment Steps

### 1. Deploy Database Functions
```sql
-- Run these SQL files in order:
-- 1. fix_monitor_degraded_settings.sql (updated with new functions)
-- 2. create_comprehensive_degraded_functions.sql
```

### 2. Deploy Updated Monitor Checker
```bash
# Deploy the updated monitor checker function
supabase functions deploy monitor-checker
```

### 3. Verify Deployment
```bash
# Run the test script to verify everything works
node test_degraded_status_handling.js
```

## Benefits

### 1. Comprehensive Detection
- All degraded criteria are now properly evaluated
- More accurate degraded status detection
- Better monitoring of service quality

### 2. Flexible Configuration
- Monitors can have custom settings or use global defaults
- Field-level customization allows fine-tuned control
- Easy to manage both global and monitor-specific thresholds

### 3. Robust Error Handling
- Graceful fallbacks if database functions fail
- System continues to work even with degraded logic errors
- Detailed logging for troubleshooting

### 4. Consistent API
- Unified function names across UI and backend
- Type-safe TypeScript interfaces
- Clear separation of concerns

## Monitoring and Maintenance

### 1. Regular Testing
- Run test script periodically to verify functionality
- Monitor logs for degraded status detection accuracy

### 2. Threshold Tuning
- Review degraded thresholds based on actual service performance
- Adjust global settings as needed
- Configure custom settings for critical monitors

### 3. Performance Monitoring
- Monitor database function performance
- Optimize queries if needed for large monitor counts
- Consider caching for frequently accessed settings

This comprehensive fix ensures that all degraded statuses are handled correctly with proper fallback from custom to global settings, providing a robust and flexible monitoring system.
