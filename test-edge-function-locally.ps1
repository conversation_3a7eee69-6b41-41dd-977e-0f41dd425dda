# PowerShell script to test the monitor-checker Edge Function locally

Write-Host "Starting local test of monitor-checker Edge Function..." -ForegroundColor Cyan

# Navigate to the function directory
Push-Location -Path "supabase/functions/monitor-checker"

try {
    # Start the function locally
    Write-Host "Starting local server..." -ForegroundColor Yellow
    Start-Process -FilePath "supabase" -ArgumentList "functions serve" -NoNewWindow
    
    Write-Host "Local server started. The function is available at: http://localhost:54321/functions/v1/monitor-checker" -ForegroundColor Green
    Write-Host "You can test it by sending a POST request to this URL." -ForegroundColor Green
    Write-Host "Press Ctrl+C to stop the server when done." -ForegroundColor Yellow
    
    # Keep the script running until user interrupts
    while ($true) {
        Start-Sleep -Seconds 1
    }
} catch {
    Write-Host "An error occurred: $_" -ForegroundColor Red
} finally {
    # Return to the original directory
    Pop-Location
}

Write-Host "Local test ended." -ForegroundColor Cyan
