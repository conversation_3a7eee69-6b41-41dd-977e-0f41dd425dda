-- This script creates a function to get all superadmin emails
-- Run this in the Supabase SQL Editor

-- Create a function to get all superadmin emails
CREATE OR REPLACE FUNCTION get_all_superadmin_emails()
RETURNS TABLE (email TEXT) 
SECURITY DEFINER
SET search_path = public
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT u.email
  FROM auth.users u
  JOIN public.user_roles ur ON u.id = ur.user_id
  WHERE ur.role_type = 'superadmin';
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_all_superadmin_emails() TO authenticated;

-- Test the function
SELECT * FROM get_all_superadmin_emails();
