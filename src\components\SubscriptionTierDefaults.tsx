import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Check, X } from 'lucide-react';
import { SubscriptionTier } from '@/types/subscription';

interface SubscriptionTierDefaultsProps {
  tiers: SubscriptionTier[];
  showTitle?: boolean;
}

export function SubscriptionTierDefaults({ tiers, showTitle = true }: SubscriptionTierDefaultsProps) {
  // Sort tiers by max_monitors (ascending)
  const sortedTiers = [...tiers].sort((a, b) => a.max_monitors - b.max_monitors);

  // Define features and their default values for each tier
  const features = [
    {
      name: 'Monitors',
      getValue: (tier: SubscriptionTier) => `${tier.max_monitors}`,
      description: 'Maximum number of monitors allowed'
    },
    {
      name: 'History Retention',
      getValue: (tier: SubscriptionTier) => `${tier.history_retention_days} days`,
      description: 'How long monitoring data is stored'
    },
    {
      name: 'Email Notifications',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free' ? <X className="h-4 w-4 text-red-500" /> : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Receive email alerts when monitors go down'
    },
    {
      name: 'SMS Notifications',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free' || tierName === 'premium'
          ? <X className="h-4 w-4 text-red-500" />
          : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Receive SMS alerts when monitors go down'
    },
    {
      name: 'Webhook Integrations',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free'
          ? <X className="h-4 w-4 text-red-500" />
          : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Integrate with external services via webhooks'
    },
    {
      name: 'API Access',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free'
          ? <X className="h-4 w-4 text-red-500" />
          : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Access to the API for automation'
    },
    {
      name: 'Team Members',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        if (tierName === 'free') return '1';
        if (tierName === 'premium') return '3';
        if (tierName === 'professional') return '10';
        return 'Unlimited';
      },
      description: 'Number of team members allowed'
    },
    {
      name: 'Check Interval',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        if (tierName === 'free') return '60 min';
        if (tierName === 'premium') return '15 min';
        if (tierName === 'professional') return '5 min';
        return '1 min';
      },
      description: 'How frequently monitors are checked'
    },
    {
      name: 'Custom Degraded Settings',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free' || tierName === 'premium'
          ? <X className="h-4 w-4 text-red-500" />
          : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Configure custom degraded thresholds'
    },
    {
      name: 'Priority Support',
      getValue: (tier: SubscriptionTier) => {
        const tierName = tier.name.toLowerCase();
        return tierName === 'free' || tierName === 'premium'
          ? <X className="h-4 w-4 text-red-500" />
          : <Check className="h-4 w-4 text-green-500" />;
      },
      description: 'Priority customer support'
    }
  ];

  return (
    <Card>
      {showTitle && (
        <CardHeader>
          <CardTitle>Subscription Tier Features</CardTitle>
          <CardDescription>
            Default features and limits for each subscription tier
          </CardDescription>
        </CardHeader>
      )}
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Feature</TableHead>
                {sortedTiers.map((tier) => (
                  <TableHead key={tier.id} className="text-center">
                    <div className="flex flex-col items-center">
                      <Badge variant="outline" className="mb-1">
                        {tier.name}
                      </Badge>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {features.map((feature) => (
                <TableRow key={feature.name}>
                  <TableCell className="font-medium">
                    <div>
                      {feature.name}
                      <p className="text-xs text-muted-foreground mt-1">{feature.description}</p>
                    </div>
                  </TableCell>
                  {sortedTiers.map((tier) => (
                    <TableCell key={`${tier.id}-${feature.name}`} className="text-center">
                      {feature.getValue(tier)}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
