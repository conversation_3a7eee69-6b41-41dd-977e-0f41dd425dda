import React, { useState, forwardRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Mail, RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface SendTestEmailButtonProps {
  monitorId: string;
  monitorName: string;
  isSuperadmin: boolean;
}

const SendTestEmailButton = forwardRef<HTMLDivElement, SendTestEmailButtonProps>((
  {
    monitorId,
    monitorName,
    isSuperadmin
  },
  ref
) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [testEmail, setTestEmail] = useState('');

  if (!isSuperadmin) {
    return null;
  }

  const sendTestEmail = async () => {
    if (!testEmail) {
      toast({
        title: 'Error',
        description: 'Please enter an email address.',
        variant: 'destructive',
      });
      return;
    }

    setIsSending(true);
    try {
      // Call the RPC function to send a test email
      const { data, error } = await supabase.rpc('send_test_monitor_email', {
        p_monitor_id: monitorId,
        p_email: testEmail,
        p_status: 'down' // Default to 'down' for test emails
      });

      if (error) throw error;

      toast({
        title: 'Test Email Requested',
        description: `A test email will be sent to ${testEmail} shortly.`,
      });
      setIsOpen(false);
    } catch (error) {
      console.error('Error requesting test email:', error);
      toast({
        title: 'Error',
        description: 'Failed to request test email. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div ref={ref} className="flex items-center w-full">
          <Mail className="h-4 w-4 mr-2" />
          Test Email
        </div>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Send Test Email</DialogTitle>
          <DialogDescription>
            Send a test notification email for monitor "{monitorName}".
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="test-email" className="text-right">
              Email
            </Label>
            <Input
              id="test-email"
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="Enter email address"
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={sendTestEmail} disabled={isSending}>
            {isSending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Sending...
              </>
            ) : (
              'Send Test Email'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
});

export default SendTestEmailButton;
