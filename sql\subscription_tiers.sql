-- This script creates the subscription tier system
-- Run this in the Supabase SQL Editor

-- Create subscription_tiers table
CREATE TABLE IF NOT EXISTS public.subscription_tiers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    max_monitors INTEGER NOT NULL,
    history_retention_days INTEGER NOT NULL,
    -- Add other tier-specific limits here
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add subscription_tier_id to companies table
ALTER TABLE public.companies
ADD COLUMN IF NOT EXISTS subscription_tier_id UUID REFERENCES public.subscription_tiers(id);

-- Add custom subscription override columns to companies table
ALTER TABLE public.companies
ADD COLUMN IF NOT EXISTS custom_max_monitors INTEGER,
ADD COLUMN IF NOT EXISTS custom_history_retention_days INTEGER;

-- Create function to get company's max monitors
CREATE OR REPLACE FUNCTION get_company_max_monitors(company_id UUID)
R<PERSON>URNS INTEGER AS $$
DECLARE
    custom_limit INTEGER;
    tier_limit INTEGER;
BEGIN
    -- Get company-specific limit if it exists
    SELECT custom_max_monitors INTO custom_limit
    FROM public.companies
    WHERE id = company_id;

    -- If company has a custom limit, return it
    IF custom_limit IS NOT NULL THEN
        RETURN custom_limit;
    END IF;

    -- Otherwise, get tier limit
    SELECT st.max_monitors INTO tier_limit
    FROM public.companies c
    JOIN public.subscription_tiers st ON c.subscription_tier_id = st.id
    WHERE c.id = company_id;

    -- Return tier limit or default (1) if not set
    RETURN COALESCE(tier_limit, 1);
END;
$$ LANGUAGE plpgsql;

-- Create function to get company's history retention days
CREATE OR REPLACE FUNCTION get_company_history_retention_days(company_id UUID)
RETURNS INTEGER AS $$
DECLARE
    custom_days INTEGER;
    tier_days INTEGER;
BEGIN
    -- Get company-specific retention days if it exists
    SELECT custom_history_retention_days INTO custom_days
    FROM public.companies
    WHERE id = company_id;

    -- If company has a custom value, return it
    IF custom_days IS NOT NULL THEN
        RETURN custom_days;
    END IF;

    -- Get company-specific retention days from existing column
    SELECT history_retention_days INTO custom_days
    FROM public.companies
    WHERE id = company_id;

    -- If company has a specific value in the old column, return it
    IF custom_days IS NOT NULL THEN
        RETURN custom_days;
    END IF;

    -- Otherwise, get tier days
    SELECT st.history_retention_days INTO tier_days
    FROM public.companies c
    JOIN public.subscription_tiers st ON c.subscription_tier_id = st.id
    WHERE c.id = company_id;

    -- Return tier days or default (7) if not set
    RETURN COALESCE(tier_days, 7);
END;
$$ LANGUAGE plpgsql;

-- Insert default subscription tiers
INSERT INTO public.subscription_tiers (name, description, max_monitors, history_retention_days)
VALUES
    ('Free', 'Basic monitoring for individuals', 1, 1),
    ('Premium', 'Enhanced monitoring for small teams', 5, 7),
    ('Professional', 'Advanced monitoring for businesses', 20, 14),
    ('Enterprise', 'Comprehensive monitoring for large organizations', 50, 31)
ON CONFLICT (name) DO UPDATE SET
    max_monitors = EXCLUDED.max_monitors,
    history_retention_days = EXCLUDED.history_retention_days,
    updated_at = now();

-- Create a trigger to enforce monitor limits
CREATE OR REPLACE FUNCTION enforce_monitor_limit()
RETURNS TRIGGER AS $$
DECLARE
    company_limit INTEGER;
    current_count INTEGER;
BEGIN
    -- Only check on insert or when company_id changes
    IF (TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND OLD.company_id IS DISTINCT FROM NEW.company_id)) THEN
        -- Skip check if no company is associated
        IF NEW.company_id IS NULL THEN
            RETURN NEW;
        END IF;

        -- Get company's monitor limit
        company_limit := get_company_max_monitors(NEW.company_id);

        -- Count current monitors for this company
        SELECT COUNT(*) INTO current_count
        FROM public.monitors
        WHERE company_id = NEW.company_id
        AND deleted = false;

        -- If this is an update, don't count the current monitor
        IF TG_OP = 'UPDATE' AND OLD.company_id = NEW.company_id THEN
            current_count := current_count - 1;
        END IF;

        -- Check if adding this monitor would exceed the limit
        IF current_count >= company_limit THEN
            RAISE EXCEPTION 'Monitor limit reached for this company. Upgrade your subscription to add more monitors.';
        END IF;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger on the monitors table
DROP TRIGGER IF EXISTS check_monitor_limit ON public.monitors;
CREATE TRIGGER check_monitor_limit
BEFORE INSERT OR UPDATE ON public.monitors
FOR EACH ROW EXECUTE FUNCTION enforce_monitor_limit();

-- Create a function to check if a company can add more monitors
CREATE OR REPLACE FUNCTION can_add_monitor(company_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    company_limit INTEGER;
    current_count INTEGER;
BEGIN
    -- Get company's monitor limit
    company_limit := get_company_max_monitors(company_id);

    -- Count current monitors for this company
    SELECT COUNT(*) INTO current_count
    FROM public.monitors
    WHERE company_id = company_id
    AND deleted = false;

    -- Return true if under limit, false if at or over limit
    RETURN current_count < company_limit;
END;
$$ LANGUAGE plpgsql;

-- Create a function to get company subscription info
CREATE OR REPLACE FUNCTION get_company_subscription_info(company_id UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'tier_name', COALESCE(st.name, 'Unknown'),
        'tier_description', st.description,
        'max_monitors', get_company_max_monitors(c.id),
        'current_monitors', (
            SELECT COUNT(*)
            FROM public.monitors m
            WHERE m.company_id = c.id AND m.deleted = false
        ),
        'history_retention_days', get_company_history_retention_days(c.id),
        'is_custom', (c.custom_max_monitors IS NOT NULL OR c.custom_history_retention_days IS NOT NULL)
    ) INTO result
    FROM public.companies c
    LEFT JOIN public.subscription_tiers st ON c.subscription_tier_id = st.id
    WHERE c.id = company_id;

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Enable RLS on subscription_tiers table
ALTER TABLE public.subscription_tiers ENABLE ROW LEVEL SECURITY;

-- Create policy for subscription_tiers table
DROP POLICY IF EXISTS "Superadmins can manage subscription tiers" ON public.subscription_tiers;
CREATE POLICY "Superadmins can manage subscription tiers"
ON public.subscription_tiers
FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM public.user_roles
        WHERE user_id = auth.uid()
        AND role_type = 'superadmin'
    )
);

DROP POLICY IF EXISTS "All users can view subscription tiers" ON public.subscription_tiers;
CREATE POLICY "All users can view subscription tiers"
ON public.subscription_tiers
FOR SELECT
TO authenticated
USING (true);
