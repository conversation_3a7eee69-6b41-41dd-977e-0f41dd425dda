import { supabase } from '@/integrations/supabase/client';
import {
  Company,
  CompanyMember,
  CreateCompanyData,
  UpdateCompanyData,
  AddCompanyMemberData,
  UpdateCompanyMemberData
} from '@/types/company';

/**
 * Get all companies for the current user
 */
export async function getUserCompanies(userId: string): Promise<Company[]> {
  // First get the company IDs for this user
  const { data: memberData, error: memberError } = await supabase
    .from('company_members')
    .select('company_id')
    .eq('user_id', userId);

  if (memberError) {
    console.error('Error fetching company memberships:', memberError);
    throw memberError;
  }

  if (!memberData || memberData.length === 0) {
    return [];
  }

  // Extract company IDs
  const companyIds = memberData.map(item => item.company_id);

  // Fetch the companies (excluding soft-deleted ones)
  const { data: companiesData, error: companiesError } = await supabase
    .from('companies')
    .select('*')
    .in('id', companyIds)
    .eq('deleted', false);

  if (companiesError) {
    console.error('Error fetching companies:', companiesError);
    throw companiesError;
  }

  return companiesData || [];
}

/**
 * Get company members for a specific company
 */
export async function getCompanyMembers(companyId: string): Promise<CompanyMember[]> {
  if (companyId === 'all') {
    return [];
  }

  const { data, error } = await supabase
    .rpc('get_company_members', { p_company_id: companyId });

  if (error) {
    console.error('Error fetching company members:', error);
    throw error;
  }

  if (!data || data.length === 0) {
    return [];
  }

  // Transform the data into CompanyMember objects
  return data.map((item: any) => ({
    id: item.id,
    company_id: item.company_id,
    user_id: item.user_id,
    role_type: item.role_type,
    created_at: item.created_at,
    updated_at: item.updated_at,
    email: item.email,
    full_name: item.full_name,
    avatar_url: item.avatar_url
  }));
}

/**
 * Create a new company
 */
export async function createCompany(data: CreateCompanyData, userId: string): Promise<Company> {
  // Insert the company
  const { data: companyData, error: companyError } = await supabase
    .from('companies')
    .insert([data])
    .select()
    .single();

  if (companyError) {
    console.error('Error inserting company:', companyError);
    throw companyError;
  }

  // Add the current user as an admin
  const { error: memberError } = await supabase
    .from('company_members')
    .insert([{
      company_id: companyData.id,
      user_id: userId,
      role_type: 'admin'
    }]);

  if (memberError) {
    console.error('Error adding user as admin:', memberError);
    throw memberError;
  }

  return {
    id: companyData.id,
    name: companyData.name,
    description: companyData.description,
    logo_url: companyData.logo_url,
    created_at: companyData.created_at,
    updated_at: companyData.updated_at
  };
}

/**
 * Update a company
 */
export async function updateCompany(id: string, data: UpdateCompanyData): Promise<Company> {
  const { data: companyData, error } = await supabase
    .from('companies')
    .update(data)
    .eq('id', id)
    .eq('deleted', false)
    .select()
    .single();

  if (error) {
    console.error('Error updating company:', error);
    throw error;
  }

  return {
    id: companyData.id,
    name: companyData.name,
    description: companyData.description,
    logo_url: companyData.logo_url,
    created_at: companyData.created_at,
    updated_at: companyData.updated_at
  };
}

/**
 * Delete a company (soft delete for admins, hard delete for superadmins)
 */
export async function deleteCompany(id: string, userId: string, isSuperadmin: boolean): Promise<void> {
  if (isSuperadmin) {
    // Hard delete for superadmins
    const { error } = await supabase
      .from('companies')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error hard deleting company:', error);
      throw error;
    }
  } else {
    // Try soft delete for regular admins
    try {
      const { error } = await supabase
        .rpc('soft_delete_company', {
          company_id: id,
          user_id: userId
        });

      if (error) {
        // If the RPC fails, fall back to updating the deleted flag directly
        console.error('Error with soft_delete_company RPC, trying direct update:', error);
        
        const { error: updateError } = await supabase
          .from('companies')
          .update({
            deleted: true,
            deleted_at: new Date().toISOString(),
            deleted_by: userId
          })
          .eq('id', id);

        if (updateError) {
          // If that fails too, just do a hard delete
          console.error('Error with direct update, falling back to hard delete:', updateError);
          const { error: hardDeleteError } = await supabase
            .from('companies')
            .delete()
            .eq('id', id);

          if (hardDeleteError) {
            console.error('Error with hard delete fallback:', hardDeleteError);
            throw hardDeleteError;
          }
        }
      }
    } catch (softDeleteErr) {
      console.error('Error with soft delete:', softDeleteErr);
      throw softDeleteErr;
    }
  }
}

/**
 * Add a member to a company
 */
export async function addCompanyMember(data: AddCompanyMemberData): Promise<CompanyMember> {
  // Check if the user exists
  const { data: userData, error: userError } = await supabase
    .from('users')
    .select('id, email, full_name, avatar_url')
    .eq('id', data.user_id)
    .single();

  if (userError) {
    console.error('Error fetching user data:', userError);
    throw new Error('User not found');
  }

  // Add the member
  const { data: memberData, error: memberError } = await supabase
    .from('company_members')
    .insert([data])
    .select()
    .single();

  if (memberError) {
    console.error('Error adding company member:', memberError);
    throw memberError;
  }

  // Return the member object with user information
  return {
    id: memberData.id,
    company_id: memberData.company_id,
    user_id: memberData.user_id,
    role_type: memberData.role_type,
    created_at: memberData.created_at,
    updated_at: memberData.updated_at,
    email: userData.email,
    full_name: userData.full_name,
    avatar_url: userData.avatar_url
  };
}

/**
 * Update a company member
 */
export async function updateCompanyMember(id: string, data: UpdateCompanyMemberData): Promise<CompanyMember> {
  const { data: memberData, error } = await supabase
    .from('company_members')
    .update(data)
    .eq('id', id)
    .select('*')
    .single();

  if (error) {
    console.error('Error updating company member:', error);
    throw error;
  }

  return {
    id: memberData.id,
    company_id: memberData.company_id,
    user_id: memberData.user_id,
    role_type: memberData.role_type,
    created_at: memberData.created_at,
    updated_at: memberData.updated_at
  };
}

/**
 * Remove a member from a company
 */
export async function removeCompanyMember(id: string): Promise<void> {
  const { error } = await supabase
    .from('company_members')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error removing company member:', error);
    throw error;
  }
}
