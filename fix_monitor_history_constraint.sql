-- This script checks and fixes the monitor_history table constraints
-- Run this in the Supabase SQL Editor

-- Check the structure of the monitor_history table
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'monitor_history'
ORDER BY 
    ordinal_position;

-- Check the constraints on the monitor_history table
SELECT 
    con.conname AS constraint_name,
    con.contype AS constraint_type,
    pg_get_constraintdef(con.oid) AS constraint_definition
FROM 
    pg_constraint con
    JOIN pg_class rel ON rel.oid = con.conrelid
    JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
WHERE 
    nsp.nspname = 'public'
    AND rel.relname = 'monitor_history';

-- Fix the valid_status constraint if needed
ALTER TABLE public.monitor_history DROP CONSTRAINT IF EXISTS valid_status;
ALTER TABLE public.monitor_history ADD CONSTRAINT valid_status CHECK (status IS NOT NULL);

-- Alternatively, if the status field should be a boolean:
-- ALTER TABLE public.monitor_history DROP CONSTRAINT IF EXISTS valid_status;
-- ALTER TABLE public.monitor_history ADD CONSTRAINT valid_status CHECK (status IN (true, false));

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
