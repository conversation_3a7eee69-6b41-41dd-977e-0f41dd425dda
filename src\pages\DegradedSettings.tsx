import React, { useState, useEffect, use<PERSON>allback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import { useAuth } from '@/contexts/AuthContext';
import DocumentTitle from '@/components/DocumentTitle';
import VumLogo from '@/components/VumLogo';
import { ArrowLeft, Save, RefreshCw, AlertTriangle } from 'lucide-react';
import FixedHeader from '@/components/FixedHeader';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/components/AppLayout';

interface DegradedThresholds {
  response_time: number;
  error_rate: number;
  status_codes: number[];
  consecutive_failures: number;
}

const DegradedSettings = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { isGlobalSuperadmin } = useCompanyRoles();

  // Use refs to reduce re-renders
  const initialLoadDone = useRef(false);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isResetting, setIsResetting] = useState(false);
  const [settings, setSettings] = useState<DegradedThresholds>({
    response_time: 1000,
    error_rate: 10,
    status_codes: [429, 503],
    consecutive_failures: 2
  });
  const [statusCodeInput, setStatusCodeInput] = useState('');

  // Load settings from the database - memoized to prevent unnecessary re-renders
  const loadSettings = useCallback(async () => {
    // Only show loading indicator on initial load
    if (!initialLoadDone.current) {
      setIsLoading(true);
    }

    try {
      // Get settings from the new degraded_settings table
      const { data, error } = await supabase
        .from('degraded_settings')
        .select('*')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is 'no rows returned'
        throw error;
      }

      if (data) {
        setSettings({
          response_time: data.response_time,
          error_rate: data.error_rate,
          status_codes: data.status_codes || [],
          consecutive_failures: data.consecutive_failures
        });
      }

      // Mark initial load as complete
      initialLoadDone.current = true;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error loading settings:', error);
      }
      toast({
        title: 'Error',
        description: 'Failed to load settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      // Always set loading to false, even if there was an error
      setIsLoading(false);
    }
  }, [toast]);

  // Memoize the checkAccess function to prevent unnecessary re-renders
  const checkAccess = useCallback(async () => {
    try {
      setIsLoading(true);
      const isSuperadmin = await isGlobalSuperadmin();

      if (!isSuperadmin) {
        toast({
          title: 'Access Denied',
          description: 'You do not have permission to access this page.',
          variant: 'destructive',
        });
        navigate('/dashboard');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error checking access:', error);
      toast({
        title: 'Error',
        description: 'Failed to check access permissions.',
        variant: 'destructive',
      });
      navigate('/dashboard');
      return false;
    } finally {
      // Don't set isLoading to false here, as we'll do that after loading settings
    }
  }, [isGlobalSuperadmin, navigate, toast]);

  // Check if user is a superadmin and load settings
  useEffect(() => {
    let isMounted = true;

    const initPage = async () => {
      if (user) {
        try {
          const hasAccess = await checkAccess();
          if (hasAccess && isMounted) {
            await loadSettings();
          }
        } catch (error) {
          console.error('Error initializing page:', error);
        } finally {
          if (isMounted) {
            setIsLoading(false);
          }
        }
      } else {
        setIsLoading(false);
      }
    };

    initPage();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;

      // Clear any pending debounce timers
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
        debounceTimerRef.current = null;
      }
    };
  }, [user, checkAccess, loadSettings]);

  // Save settings to the database - memoized to prevent unnecessary re-renders
  const saveSettings = useCallback(async () => {
    try {
      setIsSaving(true);

      // Get the first record ID
      const { data: existingData, error: fetchError } = await supabase
        .from('degraded_settings')
        .select('id')
        .limit(1)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is 'no rows returned'
        throw fetchError;
      }

      let result;
      if (existingData?.id) {
        // Update existing record
        const { error } = await supabase
          .from('degraded_settings')
          .update({
            response_time: settings.response_time,
            error_rate: settings.error_rate,
            status_codes: settings.status_codes,
            consecutive_failures: settings.consecutive_failures,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingData.id);

        if (error) throw error;
        result = { success: true, id: existingData.id };
      } else {
        // Insert new record
        const { data, error } = await supabase
          .from('degraded_settings')
          .insert({
            response_time: settings.response_time,
            error_rate: settings.error_rate,
            status_codes: settings.status_codes,
            consecutive_failures: settings.consecutive_failures
          })
          .select()
          .single();

        if (error) throw error;
        result = { success: true, id: data.id };
      }

      toast({
        title: 'Settings Saved',
        description: 'Global degraded service thresholds have been updated successfully.',
      });
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error saving settings:', error);
      }
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  }, [settings, toast]);

  // Handle form submission - memoized to prevent unnecessary re-renders
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    saveSettings();
  }, [saveSettings]);

  // Handle reset settings - memoized to prevent unnecessary re-renders
  const handleResetSettings = useCallback(async () => {
    try {
      setIsResetting(true);
      await loadSettings();
    } catch (error) {
      console.error('Error resetting settings:', error);
    } finally {
      setIsResetting(false);
    }
  }, [loadSettings]);

  // Debounced input change handler to reduce state updates
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // Clear any existing debounce timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set a new debounce timer (300ms delay)
    debounceTimerRef.current = setTimeout(() => {
      // Convert to number for numeric fields
      if (name === 'response_time' || name === 'error_rate' || name === 'consecutive_failures') {
        setSettings(prev => ({
          ...prev,
          [name]: parseInt(value) || 0
        }));
      }

      // Clear the timer reference
      debounceTimerRef.current = null;
    }, 300);
  }, []);

  // Handle status code input - memoized to prevent unnecessary re-renders
  const handleStatusCodeAdd = useCallback(() => {
    const code = parseInt(statusCodeInput);
    if (code && !isNaN(code) && code > 0) {
      if (!settings.status_codes.includes(code)) {
        setSettings(prev => ({
          ...prev,
          status_codes: [...prev.status_codes, code].sort((a, b) => a - b)
        }));
      }
      setStatusCodeInput('');
    }
  }, [statusCodeInput, settings.status_codes]);

  // Handle status code removal - memoized to prevent unnecessary re-renders
  const handleStatusCodeRemove = useCallback((code: number) => {
    setSettings(prev => ({
      ...prev,
      status_codes: prev.status_codes.filter(c => c !== code)
    }));
  }, []);

  // Memoize the status codes list to prevent unnecessary re-renders
  const statusCodesList = useMemo(() => (
    <>
      {settings.status_codes.map(code => (
        <Badge
          key={code}
          variant="secondary"
          className="flex items-center gap-1 px-3 py-1"
        >
          {code}
          <button
            type="button"
            className="ml-1 text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300"
            onClick={() => handleStatusCodeRemove(code)}
          >
            &times;
          </button>
        </Badge>
      ))}
      {settings.status_codes.length === 0 && (
        <p className="text-sm text-slate-500 italic">No status codes added</p>
      )}
    </>
  ), [settings.status_codes, handleStatusCodeRemove]);

  // Memoize the onChange handler for status code input with debouncing
  const handleStatusCodeInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    // For simple text inputs like this, we can update immediately without debouncing
    // since it doesn't trigger expensive re-renders
    setStatusCodeInput(e.target.value);
  }, []);

  // Memoize the onKeyDown handler for status code input
  const handleStatusCodeInputKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleStatusCodeAdd();
    }
  }, [handleStatusCodeAdd]);

  // Memoize the loading component to prevent re-renders
  const loadingComponent = useMemo(() => (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900 flex items-center justify-center">
      <DocumentTitle title="Loading..." />
      <div className="text-center">
        <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
        <h2 className="text-xl font-semibold">Loading settings...</h2>
      </div>
    </div>
  ), []);

  // Only show loading screen on initial load
  if (isLoading && !initialLoadDone.current) {
    return loadingComponent;
  }

  return (
    <AppLayout>
      <DocumentTitle title="Global Degraded Service Settings" />

      <div className="container mx-auto py-8 px-4 pt-16">
        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Configure Global Degraded Service Thresholds</CardTitle>
            <CardDescription>
              These global settings determine when a service is considered degraded rather than operational or down.
              Individual monitors can override these settings with their own specific thresholds.
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-6">
              <Tabs defaultValue="thresholds">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="thresholds">Thresholds</TabsTrigger>
                  <TabsTrigger value="status-codes">Status Codes</TabsTrigger>
                </TabsList>

                <TabsContent value="thresholds" className="space-y-4 pt-4">
                  <div className="space-y-2">
                      <Label htmlFor="response_time">
                        Response Time Threshold (ms)
                      </Label>
                      <Input
                        id="response_time"
                        name="response_time"
                        type="number"
                        min="0"
                        value={settings.response_time}
                        onChange={handleInputChange}
                      />
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Services with response times above this threshold will be considered degraded.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="error_rate">
                        Error Rate Threshold (%)
                      </Label>
                      <Input
                        id="error_rate"
                        name="error_rate"
                        type="number"
                        min="0"
                        max="100"
                        value={settings.error_rate}
                        onChange={handleInputChange}
                      />
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Services with error rates above this percentage will be considered degraded.
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="consecutive_failures">
                        Consecutive Failures Threshold
                      </Label>
                      <Input
                        id="consecutive_failures"
                        name="consecutive_failures"
                        type="number"
                        min="1"
                        value={settings.consecutive_failures}
                        onChange={handleInputChange}
                      />
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        Number of consecutive partial failures before a service is considered degraded.
                      </p>
                    </div>
                  </TabsContent>

                  <TabsContent value="status-codes" className="space-y-4 pt-4">
                    <div className="space-y-2">
                      <Label htmlFor="status_codes">
                        HTTP Status Codes for Degraded Status
                      </Label>
                      <div className="flex space-x-2">
                        <Input
                          id="status_code_input"
                          placeholder="Add status code (e.g. 429)"
                          value={statusCodeInput}
                          onChange={handleStatusCodeInputChange}
                          onKeyDown={handleStatusCodeInputKeyDown}
                        />
                        <Button
                          type="button"
                          onClick={handleStatusCodeAdd}
                        >
                          Add
                        </Button>
                      </div>
                      <p className="text-sm text-slate-500 dark:text-slate-400">
                        HTTP status codes that indicate a degraded service rather than a complete outage.
                      </p>

                      <div className="flex flex-wrap gap-2 mt-2">
                        {statusCodesList}
                      </div>
                    </div>

                    <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-md">
                      <h3 className="font-medium flex items-center text-amber-800 dark:text-amber-300">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Common HTTP Status Codes for Degraded Services
                      </h3>
                      <ul className="mt-2 text-sm text-amber-700 dark:text-amber-400 space-y-1">
                        <li><strong>429</strong> - Too Many Requests (rate limiting)</li>
                        <li><strong>503</strong> - Service Unavailable (temporary overload)</li>
                        <li><strong>507</strong> - Insufficient Storage</li>
                        <li><strong>509</strong> - Bandwidth Limit Exceeded</li>
                      </ul>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>

              <CardFooter className="flex justify-between border-t pt-6">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleResetSettings}
                  disabled={isLoading || isSaving || isResetting}
                >
                  {isResetting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Resetting...
                    </>
                  ) : (
                    <>Reset</>
                  )}
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading || isSaving || isResetting}
                >
                  {isSaving ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Settings
                    </>
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
      </div>
    </AppLayout>
  );
};

export default DegradedSettings;
