-- Add soft delete columns to companies table
ALTER TABLE public.companies 
ADD COLUMN IF NOT EXISTS deleted BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS deleted_by UUID REFERENCES auth.users(id);

-- Create audit_logs table to track all user actions
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id),
    action_type TEXT NOT NULL, -- 'create', 'update', 'delete', 'soft_delete', etc.
    table_name TEXT NOT NULL,
    record_id UUID NOT NULL,
    old_data JSONB,
    new_data JSONB,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create index on table_name and record_id for faster lookups
CREATE INDEX IF NOT EXISTS audit_logs_table_record_idx ON public.audit_logs (table_name, record_id);
-- Create index on user_id for faster user-specific lookups
CREATE INDEX IF NOT EXISTS audit_logs_user_id_idx ON public.audit_logs (user_id);
-- Create index on action_type for filtering by action
CREATE INDEX IF NOT EXISTS audit_logs_action_type_idx ON public.audit_logs (action_type);
-- Create index on created_at for time-based queries
CREATE INDEX IF NOT EXISTS audit_logs_created_at_idx ON public.audit_logs (created_at);

-- Create a function to log actions to the audit_logs table
CREATE OR REPLACE FUNCTION log_action(
    p_user_id UUID,
    p_action_type TEXT,
    p_table_name TEXT,
    p_record_id UUID,
    p_old_data JSONB DEFAULT NULL,
    p_new_data JSONB DEFAULT NULL,
    p_ip_address TEXT DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_log_id UUID;
BEGIN
    INSERT INTO public.audit_logs (
        user_id,
        action_type,
        table_name,
        record_id,
        old_data,
        new_data,
        ip_address,
        user_agent
    ) VALUES (
        p_user_id,
        p_action_type,
        p_table_name,
        p_record_id,
        p_old_data,
        p_new_data,
        p_ip_address,
        p_user_agent
    ) RETURNING id INTO v_log_id;
    
    RETURN v_log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to soft delete a company
CREATE OR REPLACE FUNCTION soft_delete_company(
    company_id UUID,
    user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
    company_data JSONB;
    is_admin BOOLEAN;
    is_superadmin BOOLEAN;
BEGIN
    -- Get the company data before deletion for audit log
    SELECT to_jsonb(c) INTO company_data
    FROM companies c
    WHERE id = company_id AND deleted = FALSE;
    
    -- If company doesn't exist or is already deleted
    IF company_data IS NULL THEN
        RETURN FALSE;
    END IF;
    
    -- Check if the user is a global superadmin
    SELECT is_global_superadmin(user_id) INTO is_superadmin;
    
    -- If not a global superadmin, check if they're an admin in this company
    IF NOT is_superadmin THEN
        SELECT 
            CASE 
                WHEN EXISTS (
                    SELECT 1 FROM company_members
                    WHERE company_id = soft_delete_company.company_id
                    AND user_id = soft_delete_company.user_id
                    AND role_type = 'admin'
                ) THEN TRUE
                ELSE FALSE
            END INTO is_admin;
            
        -- Only allow admins and superadmins to soft delete companies
        IF NOT is_admin THEN
            RAISE EXCEPTION 'User does not have permission to delete this company';
        END IF;
    END IF;
    
    -- Soft delete the company
    UPDATE companies
    SET 
        deleted = TRUE,
        deleted_at = NOW(),
        deleted_by = user_id
    WHERE
        id = company_id
        AND deleted = FALSE;
    
    -- Log the action
    PERFORM log_action(
        user_id,
        'soft_delete',
        'companies',
        company_id,
        company_data,
        NULL
    );
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies for companies to exclude deleted records
DROP POLICY IF EXISTS "Users can view companies they belong to" ON public.companies;
CREATE POLICY "Users can view companies they belong to" 
ON public.companies 
FOR SELECT 
USING (
    deleted = FALSE AND
    EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = companies.id
        AND user_id = auth.uid()
    )
);

-- Create a policy for superadmins to see all companies including deleted ones
CREATE POLICY "Superadmins can view all companies including deleted" 
ON public.companies 
FOR SELECT 
USING (
    is_global_superadmin(auth.uid()) OR
    EXISTS (
        SELECT 1 FROM user_roles
        WHERE user_id = auth.uid()
        AND role_name = 'superadmin'
    )
);

-- Set up RLS for audit_logs
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Only superadmins can view audit logs
CREATE POLICY "Superadmins can view audit logs" 
ON public.audit_logs 
FOR SELECT 
USING (
    is_global_superadmin(auth.uid()) OR
    EXISTS (
        SELECT 1 FROM user_roles
        WHERE user_id = auth.uid()
        AND role_name = 'superadmin'
    )
);

-- Allow the system to insert audit logs
CREATE POLICY "System can insert audit logs" 
ON public.audit_logs 
FOR INSERT 
WITH CHECK (true);
