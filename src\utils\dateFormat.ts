import { format } from 'date-fns';
import { supabase } from '@/integrations/supabase/client';

// Default format patterns
const DEFAULT_DATE_FORMAT = 'yyyy-MM-dd';
const DEFAULT_TIME_FORMAT = 'HH:mm:ss';
const DEFAULT_DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss';

// Cache for user preferences to avoid repeated database calls
let cachedPreferences: { dateFormat: DateDisplayFormat; timeFormat: DateFormatType } | null = null;
let lastCacheTime = 0;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Date format types
export type DateFormatType = '24h' | '12h';
export type DateDisplayFormat = 'iso' | 'us' | 'eu';

// Get user preferences from database or cache
async function getUserPreferences(): Promise<{ dateFormat: DateDisplayFormat; timeFormat: DateFormatType }> {
  // Check if we have a valid cache
  const now = Date.now();
  if (cachedPreferences && (now - lastCacheTime < CACHE_DURATION)) {
    return cachedPreferences;
  }

  try {
    // Check if user is authenticated
    const { data: { session } } = await supabase.auth.getSession();

    // Only try to get preferences from database if user is authenticated
    if (session?.user) {
      // Try to get preferences from database
      const { data, error } = await supabase.rpc('get_or_create_user_preferences');

      if (!error && data) {
        // Update cache
        cachedPreferences = {
          dateFormat: data.date_format as DateDisplayFormat,
          timeFormat: data.time_format as DateFormatType
        };
        lastCacheTime = now;

        return cachedPreferences;
      }
    }

    // If not authenticated or there was an error, fall back to localStorage
    const dateFormat = getDateFormatFromLocalStorage();
    const timeFormat = getTimeFormatFromLocalStorage();

    // Update cache with localStorage values
    cachedPreferences = { dateFormat, timeFormat };
    lastCacheTime = now;

    return cachedPreferences;
  } catch (error) {
    console.error('Error getting user preferences:', error);

    // Fall back to localStorage
    const dateFormat = getDateFormatFromLocalStorage();
    const timeFormat = getTimeFormatFromLocalStorage();

    return { dateFormat, timeFormat };
  }
}

// Get date format from localStorage or system settings
function getDateFormatFromLocalStorage(): DateDisplayFormat {
  // Check if there's a saved preference in localStorage
  const savedFormat = localStorage.getItem('dateDisplayFormat') as DateDisplayFormat;
  if (savedFormat && (savedFormat === 'iso' || savedFormat === 'us' || savedFormat === 'eu')) {
    return savedFormat;
  }

  // Try to detect from browser/OS settings
  try {
    const locale = navigator.language;

    // US format (MM/DD/YYYY)
    if (locale === 'en-US') {
      return 'us';
    }

    // European format (DD/MM/YYYY) - most European countries
    if (locale.startsWith('en-GB') || locale.startsWith('fr') ||
        locale.startsWith('de') || locale.startsWith('es') ||
        locale.startsWith('it') || locale.startsWith('nl')) {
      return 'eu';
    }

    // Default to ISO format (YYYY-MM-DD)
    return 'iso';
  } catch (e) {
    // Default to ISO format if detection fails
    return 'iso';
  }
}

// Get user's preferred date display format from database, localStorage, or system settings
export function getUserDateDisplayFormat(): DateDisplayFormat {
  // First check if we have a cached value
  if (cachedPreferences) {
    return cachedPreferences.dateFormat;
  }

  // Otherwise fall back to localStorage
  return getDateFormatFromLocalStorage();
}

// Get the date format pattern based on the display format
export function getDateFormatPattern(displayFormat: DateDisplayFormat = 'iso'): string {
  switch (displayFormat) {
    case 'us':
      return 'MM/dd/yyyy';
    case 'eu':
      return 'dd/MM/yyyy';
    case 'iso':
    default:
      return 'yyyy-MM-dd';
  }
}

// Get time format from localStorage or system settings
function getTimeFormatFromLocalStorage(): DateFormatType {
  // Check if there's a saved preference in localStorage
  const savedFormat = localStorage.getItem('timeFormat') as DateFormatType;
  if (savedFormat && (savedFormat === '12h' || savedFormat === '24h')) {
    return savedFormat;
  }

  // Try to detect from browser/OS settings
  try {
    // Check if the user's locale uses 12-hour time
    const uses12Hour = new Intl.DateTimeFormat(navigator.language, {
      hour: 'numeric'
    }).format(new Date(2020, 0, 1, 13)).includes('PM');

    return uses12Hour ? '12h' : '24h';
  } catch (e) {
    // Default to 24-hour format if detection fails
    return '24h';
  }
}

// Get user's preferred time format from database, localStorage, or system settings
export function getUserTimeFormat(): DateFormatType {
  // First check if we have a cached value
  if (cachedPreferences) {
    return cachedPreferences.timeFormat;
  }

  // Otherwise fall back to localStorage
  return getTimeFormatFromLocalStorage();
}

// Format a date according to user preferences
export function formatDate(date: Date | string | number): string {
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;

  const displayFormat = getUserDateDisplayFormat();
  const dateFormat = getDateFormatPattern(displayFormat);
  return format(dateObj, dateFormat);
}

// Format a time according to user preferences
export function formatTime(date: Date | string | number): string {
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;

  const timeFormat = getUserTimeFormat();
  return format(dateObj, timeFormat === '12h' ? 'h:mm a' : 'HH:mm');
}

// Format a date and time according to user preferences
export function formatDateTime(date: Date | string | number): string {
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;

  const displayFormat = getUserDateDisplayFormat();
  const dateFormat = getDateFormatPattern(displayFormat);
  const timeFormat = getUserTimeFormat();

  const timePattern = timeFormat === '12h' ? 'h:mm a' : 'HH:mm';
  return format(dateObj, `${dateFormat} ${timePattern}`);
}

// Format a date for chart labels (compact format)
export function formatChartDate(date: Date | string | number, showTime: boolean = false): string {
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;

  if (showTime) {
    const timeFormat = getUserTimeFormat();
    const timePattern = timeFormat === '12h' ? 'h:mm a' : 'HH:mm';
    return format(dateObj, timePattern);
  }

  // For dates, use a compact format based on user preference
  const displayFormat = getUserDateDisplayFormat();
  switch (displayFormat) {
    case 'us':
      return format(dateObj, 'MM/dd');
    case 'eu':
      return format(dateObj, 'dd/MM');
    case 'iso':
    default:
      return format(dateObj, 'MM-dd');
  }
}

// Format a date and time for tooltips (detailed format)
export function formatTooltipDateTime(date: Date | string | number): string {
  const dateObj = typeof date === 'string' || typeof date === 'number'
    ? new Date(date)
    : date;

  const displayFormat = getUserDateDisplayFormat();
  const dateFormat = getDateFormatPattern(displayFormat);
  const timeFormat = getUserTimeFormat();
  const timePattern = timeFormat === '12h' ? 'h:mm:ss a' : 'HH:mm:ss';

  return format(dateObj, `${dateFormat} ${timePattern}`);
}

// Initialize preferences cache
export function initPreferencesCache(): void {
  // Load preferences in the background
  getUserPreferences().then(prefs => {
    cachedPreferences = prefs;
    lastCacheTime = Date.now();
  }).catch(error => {
    console.error('Error initializing preferences cache:', error);

    // Set default preferences if there's an error
    cachedPreferences = {
      dateFormat: getDateFormatFromLocalStorage(),
      timeFormat: getTimeFormatFromLocalStorage()
    };
    lastCacheTime = Date.now();
  });
}

// Call initPreferencesCache when this module is imported
// Wrap in setTimeout to ensure it runs after the module is fully loaded
setTimeout(initPreferencesCache, 0);
