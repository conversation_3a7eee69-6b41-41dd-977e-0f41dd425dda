-- This script migrates existing monitors to the new company-based system
-- Run this in the Supabase SQL Editor after running the company_tables_modified.sql script

-- Check if the current user already has a company
DO $$
DECLARE
    user_company_count INTEGER;
    current_user_id UUID;
    new_company_id UUID;
BEGIN
    -- Get the current user ID
    SELECT auth.uid() INTO current_user_id;
    
    -- Check if the user already has a company
    SELECT COUNT(*) INTO user_company_count
    FROM public.company_members
    WHERE user_id = current_user_id;
    
    -- If the user doesn't have a company, create one
    IF user_company_count = 0 THEN
        -- Create a default company for the current user
        INSERT INTO public.companies (name, description)
        VALUES ('My Company', 'Default company for your monitors')
        RETURNING id INTO new_company_id;
        
        -- Add the current user as an admin to the company
        INSERT INTO public.company_members (company_id, user_id, role)
        VALUES (new_company_id, current_user_id, 'admin');
        
        -- Update existing monitors to be associated with the company
        UPDATE public.monitors
        SET company_id = new_company_id
        WHERE user_id = current_user_id AND company_id IS NULL;
        
        RAISE NOTICE 'Created new company with ID: % for user %', new_company_id, current_user_id;
    ELSE
        RAISE NOTICE 'User % already has % companies', current_user_id, user_company_count;
    END IF;
END $$;
