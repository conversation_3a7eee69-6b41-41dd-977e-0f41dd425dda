-- This script adds a placeholder avatar URL to your user metadata
-- Run this in the Supabase SQL Editor

-- Update the user's metadata with a placeholder avatar URL
UPDATE auth.users
SET raw_user_meta_data = raw_user_meta_data || 
  jsonb_build_object('avatar_url', 'https://ui-avatars.com/api/?name=<PERSON>+Zwankhuizennnn&background=0D8ABC&color=fff')
WHERE id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';

-- Verify the update
SELECT id, email, raw_user_meta_data
FROM auth.users
WHERE id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';
