-- This script checks and fixes monitor status counts
-- Run this in the Supabase SQL Editor

-- First, check if we have any monitor history records
SELECT COUNT(*) AS total_records FROM public.monitor_history;

-- Check the distribution of status values
SELECT status, COUNT(*) FROM public.monitor_history GROUP BY status;

-- Check the most recent status for each monitor
WITH latest_checks AS (
    SELECT DISTINCT ON (monitor_id) 
        monitor_id,
        status,
        timestamp
    FROM 
        public.monitor_history
    ORDER BY 
        monitor_id, 
        timestamp DESC
)
SELECT 
    m.name AS monitor_name,
    lc.status,
    lc.timestamp AS last_checked,
    m.active
FROM 
    latest_checks lc
JOIN 
    public.monitors m ON lc.monitor_id = m.id
ORDER BY 
    lc.timestamp DESC;

-- Check if we have any monitors without history records
SELECT 
    m.id,
    m.name,
    m.active,
    (SELECT COUNT(*) FROM public.monitor_history WHERE monitor_id = m.id) AS history_count
FROM 
    public.monitors m
WHERE 
    (SELECT COUNT(*) FROM public.monitor_history WHERE monitor_id = m.id) = 0;

-- Create a function to get monitor status counts
CREATE OR REPLACE FUNCTION get_monitor_status_counts()
R<PERSON>URNS JSONB AS $$
DECLARE
    result JSONB;
    up_count INTEGER;
    down_count INTEGER;
    degraded_count INTEGER;
    paused_count INTEGER;
BEGIN
    -- Count active monitors that are up (latest status is true)
    SELECT COUNT(*) INTO up_count
    FROM public.monitors m
    JOIN (
        SELECT DISTINCT ON (monitor_id) monitor_id, status
        FROM public.monitor_history
        ORDER BY monitor_id, timestamp DESC
    ) h ON m.id = h.monitor_id
    WHERE m.active = true AND h.status = true;
    
    -- Count active monitors that are down (latest status is false)
    SELECT COUNT(*) INTO down_count
    FROM public.monitors m
    JOIN (
        SELECT DISTINCT ON (monitor_id) monitor_id, status
        FROM public.monitor_history
        ORDER BY monitor_id, timestamp DESC
    ) h ON m.id = h.monitor_id
    WHERE m.active = true AND h.status = false;
    
    -- Count degraded monitors (this depends on your degraded logic)
    -- For now, we'll set it to 0 since we need to implement the degraded logic
    degraded_count := 0;
    
    -- Count paused monitors (active = false)
    SELECT COUNT(*) INTO paused_count
    FROM public.monitors
    WHERE active = false;
    
    -- Build the result JSON
    result := jsonb_build_object(
        'up', up_count,
        'down', down_count,
        'degraded', degraded_count,
        'paused', paused_count
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Test the function
SELECT get_monitor_status_counts();

-- Create a view for easy access to monitor statuses
CREATE OR REPLACE VIEW monitor_status_summary AS
WITH latest_checks AS (
    SELECT DISTINCT ON (monitor_id) 
        monitor_id,
        status,
        timestamp
    FROM 
        public.monitor_history
    ORDER BY 
        monitor_id, 
        timestamp DESC
)
SELECT 
    m.id,
    m.name,
    m.active,
    CASE
        WHEN m.active = false THEN 'paused'
        WHEN lc.status IS NULL THEN 'unknown'
        WHEN lc.status = true THEN 'up'
        ELSE 'down'
    END AS status,
    lc.timestamp AS last_checked
FROM 
    public.monitors m
LEFT JOIN 
    latest_checks lc ON m.id = lc.monitor_id;

-- Test the view
SELECT * FROM monitor_status_summary;

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
