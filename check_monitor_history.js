// This script checks the monitor_history table structure
// Run with: node check_monitor_history.js

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL;
const SUPABASE_KEY = process.env.SUPABASE_KEY;

// Check if required environment variables are set
if (!SUPABASE_URL || !SUPABASE_KEY) {
  console.error('ERROR: Required environment variables SUPABASE_URL and/or SUPABASE_KEY are not set.');
  console.error('Please set these variables in your .env file.');
  process.exit(1);
}

// Initialize Supabase client
const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function checkMonitorHistoryTable() {
  console.log('Checking monitor_history table structure...');

  try {
    // Check table columns
    const { data: columns, error: columnsError } = await supabase.rpc('execute_sql', {
      query_text: `
        SELECT
          column_name,
          data_type,
          is_nullable,
          column_default
        FROM
          information_schema.columns
        WHERE
          table_schema = 'public'
          AND table_name = 'monitor_history'
        ORDER BY
          ordinal_position;
      `
    });

    if (columnsError) {
      console.error('Error checking columns:', columnsError.message);
    } else {
      console.log('Monitor History Table Columns:');
      console.table(columns);
    }

    // Check constraints
    const { data: constraints, error: constraintsError } = await supabase.rpc('execute_sql', {
      query_text: `
        SELECT
          con.conname AS constraint_name,
          con.contype AS constraint_type,
          pg_get_constraintdef(con.oid) AS constraint_definition
        FROM
          pg_constraint con
          JOIN pg_class rel ON rel.oid = con.conrelid
          JOIN pg_namespace nsp ON nsp.oid = rel.relnamespace
        WHERE
          nsp.nspname = 'public'
          AND rel.relname = 'monitor_history';
      `
    });

    if (constraintsError) {
      console.error('Error checking constraints:', constraintsError.message);
    } else {
      console.log('\nMonitor History Table Constraints:');
      console.table(constraints);
    }

    // Check a sample record
    const { data: sample, error: sampleError } = await supabase
      .from('monitor_history')
      .select('*')
      .limit(1);

    if (sampleError) {
      console.error('Error getting sample record:', sampleError.message);
    } else if (sample && sample.length > 0) {
      console.log('\nSample Record:');
      console.log(sample[0]);
      console.log('Status type:', typeof sample[0].status);
    } else {
      console.log('\nNo records found in monitor_history table');
    }

    // Try inserting a test record
    console.log('\nTrying to insert a test record...');

    // Get a monitor ID first
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('id')
      .limit(1);

    if (monitorsError) {
      console.error('Error getting monitor ID:', monitorsError.message);
      return;
    }

    if (!monitors || monitors.length === 0) {
      console.error('No monitors found');
      return;
    }

    const monitorId = monitors[0].id;

    // Try with boolean status
    const testRecord = {
      monitor_id: monitorId,
      status: true, // Boolean
      response_time: 100,
      error_message: null,
      timestamp: new Date().toISOString()
    };

    console.log('Test record:', testRecord);

    const { data: insertResult, error: insertError } = await supabase
      .from('monitor_history')
      .insert(testRecord)
      .select();

    if (insertError) {
      console.error('Error inserting test record with boolean status:', insertError.message);

      // Try with string status
      testRecord.status = 'true';
      console.log('Trying with string status:', testRecord);

      const { data: stringResult, error: stringError } = await supabase
        .from('monitor_history')
        .insert(testRecord)
        .select();

      if (stringError) {
        console.error('Error inserting test record with string status:', stringError.message);
      } else {
        console.log('Successfully inserted test record with string status:', stringResult);
      }
    } else {
      console.log('Successfully inserted test record with boolean status:', insertResult);
    }
  } catch (error) {
    console.error('Error checking monitor_history table:', error.message);
  }
}

// Run the check
checkMonitorHistoryTable();
