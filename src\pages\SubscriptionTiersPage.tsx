import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyRoles } from '@/hooks/use-company-roles';
import AppLayout from '@/components/AppLayout';
import UnifiedHeader from '@/components/UnifiedHeader';
import DocumentTitle from '@/components/DocumentTitle';
import { SubscriptionTierSettings } from '@/components/SubscriptionTierSettings';
import { SubscriptionFeatureSettings } from '@/components/SubscriptionFeatureSettings';
import { Separator } from '@/components/ui/separator';
import { Loader2 } from 'lucide-react';

const SubscriptionTiersPage = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin, isLoading: checkingSuperadmin } = useGlobalSuperadminQuery();

  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }

    // Only redirect if we've finished checking and user is not a superadmin
    if (!checkingSuperadmin && isSuperadmin === false) {
      navigate('/dashboard');
    }
  }, [user, isSuperadmin, checkingSuperadmin, navigate]);

  const header = (
    <UnifiedHeader
      title="Subscription Tiers"
      description="Manage subscription tier settings"
    />
  );

  if (!user || checkingSuperadmin || isSuperadmin === undefined) {
    return (
      <AppLayout header={header}>
        <div className="flex justify-center items-center h-full">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </AppLayout>
    );
  }

  // If we've checked and user is not a superadmin, they shouldn't see this page
  if (isSuperadmin === false) {
    return null; // Will be redirected by useEffect
  }

  return (
    <AppLayout header={header}>
      <DocumentTitle title="Subscription Tiers" />
      <div className="container mx-auto py-6 space-y-8">
        <SubscriptionTierSettings />

        <Separator className="my-8" />

        <SubscriptionFeatureSettings />

        <Separator className="my-8" />

        <div className="bg-muted/50 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-3">About Subscription Tiers</h3>
          <p className="text-muted-foreground mb-4">
            Subscription tiers define the limits and features available to companies in the system.
            Each tier has different limits for the number of monitors and data retention periods.
          </p>

          <h4 className="font-medium mt-4 mb-2">Default Tiers:</h4>
          <ul className="list-disc list-inside space-y-2 text-muted-foreground">
            <li><strong>Free:</strong> 1 monitor, 1 day of logs</li>
            <li><strong>Premium:</strong> 5 monitors, 7 days of logs</li>
            <li><strong>Professional:</strong> 20 monitors, 14 days of logs</li>
            <li><strong>Enterprise:</strong> 50 monitors, 31 days of logs</li>
          </ul>

          <p className="text-muted-foreground mt-4">
            You can modify these tiers or override them for specific companies as needed.
          </p>
        </div>
      </div>
    </AppLayout>
  );
};

export default SubscriptionTiersPage;
