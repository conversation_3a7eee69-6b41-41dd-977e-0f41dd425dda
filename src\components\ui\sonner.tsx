import { useTheme } from "next-themes"
import { Toaster as Sonner, toast } from "sonner"
import { Copy } from "lucide-react"
import { Button } from "@/components/ui/button"

type ToasterProps = React.ComponentProps<typeof Sonner>

// Custom toast component with copy button
const CustomToast = ({ toast }: { toast: any }) => {
  const handleCopy = () => {
    const contentToCopy = `${toast.title || ''} ${toast.description || ''}`;
    navigator.clipboard.writeText(contentToCopy)
      .then(() => {
        toast.success('Copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy text: ', err);
      });
  };

  // Only show copy button for error toasts
  const showCopyButton = toast.type === 'error' || toast.variant === 'destructive';

  return (
    <div className="flex flex-col w-full">
      <div>
        {toast.title && <div className="font-semibold">{toast.title}</div>}
        {toast.description && <div className="text-sm opacity-90">{toast.description}</div>}
      </div>
      {showCopyButton && (
        <div className="flex justify-end mt-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleCopy}
            className="h-7 px-2 text-xs"
          >
            <Copy className="h-3.5 w-3.5 mr-1" />
            Copy
          </Button>
        </div>
      )}
    </div>
  );
};

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
        },
      }}
      render={CustomToast}
      {...props}
    />
  )
}

export { Toaster }
