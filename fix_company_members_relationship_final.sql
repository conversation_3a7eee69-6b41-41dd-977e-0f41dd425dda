-- This script fixes the relationship between company_members and users tables
-- Run this in the Supabase SQL Editor

-- First, check what the users table/view is
SELECT 
    table_name,
    table_type
FROM 
    information_schema.tables 
WHERE 
    table_schema = 'public' 
    AND table_name = 'users';

-- Check the columns in the users table
SELECT 
    column_name, 
    data_type
FROM 
    information_schema.columns
WHERE 
    table_schema = 'public' 
    AND table_name = 'users';

-- If the users table exists but doesn't have the columns we need,
-- let's add them if they don't exist

DO $$
BEGIN
    -- Check if the email column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'email'
    ) THEN
        ALTER TABLE public.users ADD COLUMN email TEXT;
        RAISE NOTICE 'Added email column to users table';
    END IF;

    -- Check if the full_name column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'full_name'
    ) THEN
        ALTER TABLE public.users ADD COLUMN full_name TEXT;
        RAISE NOTICE 'Added full_name column to users table';
    END IF;

    -- Check if the avatar_url column exists
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users' 
        AND column_name = 'avatar_url'
    ) THEN
        ALTER TABLE public.users ADD COLUMN avatar_url TEXT;
        RAISE NOTICE 'Added avatar_url column to users table';
    END IF;
END $$;

-- Now let's update the users table with data from auth.users
UPDATE public.users u
SET 
    email = a.email,
    full_name = a.raw_user_meta_data->>'full_name',
    avatar_url = a.raw_user_meta_data->>'avatar_url'
FROM auth.users a
WHERE u.id = a.id;

-- Insert any users that exist in auth.users but not in public.users
INSERT INTO public.users (id, email, full_name, avatar_url)
SELECT 
    a.id,
    a.email,
    a.raw_user_meta_data->>'full_name',
    a.raw_user_meta_data->>'avatar_url'
FROM 
    auth.users a
LEFT JOIN 
    public.users u ON a.id = u.id
WHERE 
    u.id IS NULL;

-- Check the updated users table
SELECT * FROM public.users LIMIT 10;
