-- This script updates the user's metadata with an avatar URL
-- Run this in the Supabase SQL Editor

-- Replace 'YOUR_USER_ID' with your actual user ID
-- Replace 'YOUR_AVATAR_URL' with the actual URL of your avatar

UPDATE auth.users
SET raw_user_meta_data = raw_user_meta_data || 
  jsonb_build_object('avatar_url', 'https://axcfqilzeombkbzebeym.supabase.co/storage/v1/object/public/avatars/3a19a1f7-3f61-48a8-949c-caa3fba04924.png')
WHERE id = '3a19a1f7-3f61-48a8-949c-caa3fba04924';
