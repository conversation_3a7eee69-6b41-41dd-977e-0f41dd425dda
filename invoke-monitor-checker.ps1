# PowerShell script to manually invoke the monitor-checker Edge Function

param (
    [Parameter(Mandatory=$true)]
    [string]$MonitorId  # Required parameter for checking a specific monitor
)

Write-Host "Invoking monitor-checker Edge Function..." -ForegroundColor Cyan

# Supabase project reference
$projectRef = "axcfqilzeombkbzebeym"

# Prepare the request body
$body = @{
    monitorId = $MonitorId
}
Write-Host "Checking monitor: $MonitorId" -ForegroundColor Yellow

# Convert body to JSON
$bodyJson = $body | ConvertTo-Json

try {
    # Get Supabase API key from environment or prompt user
    $apiKey = $env:SUPABASE_API_KEY
    if (-not $apiKey) {
        $apiKey = Read-Host "Enter your Supabase API key (anon or service_role)"
    }

    # Invoke the function
    $response = Invoke-RestMethod `
        -Uri "https://$projectRef.functions.supabase.co/monitor-checker" `
        -Method Post `
        -Headers @{
            "Authorization" = "Bearer $apiKey"
            "Content-Type" = "application/json"
        } `
        -Body $bodyJson

    # Display the response
    Write-Host "Function executed successfully!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 4
} catch {
    Write-Host "Error invoking function: $_" -ForegroundColor Red
    if ($_.Exception.Response) {
        $responseBody = $_.Exception.Response.GetResponseStream()
        $reader = New-Object System.IO.StreamReader($responseBody)
        $responseContent = $reader.ReadToEnd()
        Write-Host "Response content: $responseContent" -ForegroundColor Red
    }
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
