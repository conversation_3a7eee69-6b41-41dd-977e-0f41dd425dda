import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { Loader2, RefreshCw, Plus, Trash2, AlertTriangle } from 'lucide-react';
import { useSubscription } from '@/hooks/use-subscription';
import { SubscriptionFeature } from '@/types/subscription';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

export function SubscriptionFeatureSettings() {
  const {
    useSubscriptionFeatures,
    useCreateSubscriptionFeature,
    useUpdateSubscriptionFeature,
    useDeleteSubscriptionFeature,
    isSuperadmin
  } = useSubscription();

  const { data: features, isLoading, refetch } = useSubscriptionFeatures();
  const createFeatureMutation = useCreateSubscriptionFeature();
  const updateFeatureMutation = useUpdateSubscriptionFeature();
  const deleteFeatureMutation = useDeleteSubscriptionFeature();

  const [editingFeature, setEditingFeature] = useState<SubscriptionFeature | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const [featureToDelete, setFeatureToDelete] = useState<SubscriptionFeature | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newFeature, setNewFeature] = useState<{
    name: string;
    description: string;
  }>({
    name: '',
    description: ''
  });

  // If not a superadmin, don't show this component
  if (!isSuperadmin) {
    return null;
  }

  const handleEdit = (feature: SubscriptionFeature) => {
    setEditingFeature({ ...feature });
  };

  const handleSave = async () => {
    if (!editingFeature) return;

    setIsSaving(true);
    try {
      await updateFeatureMutation.mutateAsync({
        id: editingFeature.id,
        updates: {
          name: editingFeature.name,
          description: editingFeature.description
        }
      });

      refetch();
      setEditingFeature(null);
    } catch (error) {
      console.error('Error updating feature:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setEditingFeature(null);
  };

  const handleInputChange = (field: keyof SubscriptionFeature, value: string) => {
    if (!editingFeature) return;

    setEditingFeature({
      ...editingFeature,
      [field]: value
    });
  };

  const handleNewFeatureInputChange = (field: string, value: string) => {
    setNewFeature({
      ...newFeature,
      [field]: value
    });
  };

  const handleCreateFeature = async () => {
    setIsSaving(true);
    try {
      await createFeatureMutation.mutateAsync({
        name: newFeature.name,
        description: newFeature.description
      });

      // Reset form and close dialog
      setNewFeature({
        name: '',
        description: ''
      });
      setShowCreateDialog(false);
      refetch();
    } catch (error) {
      console.error('Error creating feature:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteClick = (feature: SubscriptionFeature) => {
    setFeatureToDelete(feature);
    setShowDeleteAlert(true);
  };

  const handleConfirmDelete = async () => {
    if (!featureToDelete) return;

    try {
      await deleteFeatureMutation.mutateAsync(featureToDelete.id);
      setShowDeleteAlert(false);
      setFeatureToDelete(null);
      refetch();
    } catch (error) {
      console.error('Error deleting feature:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Subscription Features</h2>
        <div className="flex space-x-2">
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Feature
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Subscription Feature</DialogTitle>
                <DialogDescription>
                  Add a new subscription feature that can be enabled or disabled for each tier.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="new-feature-name">Name</Label>
                  <Input
                    id="new-feature-name"
                    value={newFeature.name}
                    onChange={(e) => handleNewFeatureInputChange('name', e.target.value)}
                    placeholder="e.g., SMS Notifications"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-feature-description">Description</Label>
                  <Input
                    id="new-feature-description"
                    value={newFeature.description}
                    onChange={(e) => handleNewFeatureInputChange('description', e.target.value)}
                    placeholder="e.g., Receive SMS alerts when monitors go down"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateFeature} disabled={isSaving || !newFeature.name || !newFeature.description}>
                  {isSaving ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Feature'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Button variant="outline" onClick={() => refetch()} disabled={isLoading}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid gap-4">
        {features?.map((feature) => (
          <Card key={feature.id}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{feature.name}</CardTitle>
            </CardHeader>
            <CardContent>
              {editingFeature && editingFeature.id === feature.id ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor={`name-${feature.id}`}>Name</Label>
                    <Input
                      id={`name-${feature.id}`}
                      value={editingFeature.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor={`description-${feature.id}`}>Description</Label>
                    <Input
                      id={`description-${feature.id}`}
                      value={editingFeature.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                    />
                  </div>
                </div>
              ) : (
                <div>
                  <p className="text-sm text-muted-foreground">{feature.description}</p>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-between">
              <div>
                {!editingFeature && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteClick(feature)}
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
              <div className="space-x-2">
                {editingFeature && editingFeature.id === feature.id ? (
                  <>
                    <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
                      Cancel
                    </Button>
                    <Button onClick={handleSave} disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Save Changes'
                      )}
                    </Button>
                  </>
                ) : (
                  <Button variant="outline" onClick={() => handleEdit(feature)}>
                    Edit
                  </Button>
                )}
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center text-red-600">
              <AlertTriangle className="h-5 w-5 mr-2" /> Delete Subscription Feature
            </AlertDialogTitle>
            <AlertDialogDescription>
              <p className="mb-4">
                Are you sure you want to delete the <strong>{featureToDelete?.name}</strong> feature?
              </p>
              <p className="mb-2">
                This action cannot be undone. Deleting this feature will remove it from all subscription tiers.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteAlert(false)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
