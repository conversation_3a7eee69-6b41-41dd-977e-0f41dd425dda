# Manual Deployment Instructions for Monitor Checker Function

If you're having issues with the Supabase CLI, you can manually deploy the Edge Function through the Supabase dashboard.

## Step 1: Create a ZIP File of the Function

1. Run the provided PowerShell script:
   ```
   .\create-function-zip.ps1
   ```

2. This will create a file called `monitor-checker.zip` in the `supabase/functions` directory.

## Step 2: Deploy Through the Supabase Dashboard

1. Go to your Supabase dashboard: https://app.supabase.com/project/[YOUR_PROJECT_ID]

2. Navigate to Edge Functions in the left sidebar

3. Click "Create a new function"

4. Enter the following details:
   - Name: `monitor-checker`
   - Choose the option to upload a file
   - Upload the `monitor-checker.zip` file you created

5. Click "Create function"

## Step 3: Test the Function

1. After deployment, you can test the function directly from the dashboard:
   - Click on the newly created function
   - Go to the "Invoke" tab
   - Click "Invoke" to run the function

2. You can also use the provided PowerShell script to invoke the function:
   ```
   .\invoke-monitor-checker.ps1
   ```

## Step 4: Set Up Scheduled Execution

1. Go to your Supabase dashboard

2. Navigate to Database > Functions > Scheduled Functions

3. Create a new scheduled function:
   - Name: `trigger_monitor_checker`
   - Schedule: `* * * * *` (every minute)
   - Function: `SELECT trigger_monitor_checker();`

## Troubleshooting

If you encounter issues with the manual deployment:

1. Check that the ZIP file contains the correct files:
   - `index.ts` should be at the root of the ZIP file

2. Check the function logs in the Supabase dashboard for any error messages

3. Make sure you have the necessary permissions to create and invoke functions in your Supabase project
