/**
 * Comprehensive test script for degraded status handling
 * This script tests all aspects of degraded status detection
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://axcfqilzeombkbzebeym.supabase.co';
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4Y2ZxaWx6ZW9tYmtiemViZXltIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzI3MjE5NzQsImV4cCI6MjA0ODI5Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDegradedStatusHandling() {
  console.log('🧪 Testing Degraded Status Handling...\n');

  try {
    // Test 1: Check if required database functions exist
    console.log('1️⃣ Testing database function existence...');
    
    const functions = [
      'get_monitor_degraded_settings',
      'get_monitor_degraded_settings_rpc',
      'get_monitors_with_degraded_settings',
      'is_monitor_degraded',
      'get_monitor_consecutive_failures',
      'get_monitor_error_rate',
      'evaluate_monitor_status'
    ];

    for (const funcName of functions) {
      try {
        const { data, error } = await supabase
          .rpc('pg_get_functiondef', { funcname: funcName });
        
        if (error) {
          console.log(`   ❌ Function ${funcName}: NOT FOUND`);
        } else {
          console.log(`   ✅ Function ${funcName}: EXISTS`);
        }
      } catch (err) {
        console.log(`   ❌ Function ${funcName}: ERROR - ${err.message}`);
      }
    }

    // Test 2: Check global degraded settings
    console.log('\n2️⃣ Testing global degraded settings...');
    
    const { data: globalSettings, error: globalError } = await supabase
      .from('degraded_settings')
      .select('*')
      .limit(1)
      .single();

    if (globalError) {
      console.log('   ❌ Global settings not found:', globalError.message);
    } else {
      console.log('   ✅ Global settings found:', {
        response_time: globalSettings.response_time,
        error_rate: globalSettings.error_rate,
        status_codes: globalSettings.status_codes,
        consecutive_failures: globalSettings.consecutive_failures
      });
    }

    // Test 3: Get a test monitor
    console.log('\n3️⃣ Finding test monitor...');
    
    const { data: monitors, error: monitorsError } = await supabase
      .from('monitors')
      .select('id, name, target')
      .limit(1);

    if (monitorsError || !monitors || monitors.length === 0) {
      console.log('   ❌ No monitors found for testing');
      return;
    }

    const testMonitor = monitors[0];
    console.log(`   ✅ Using test monitor: ${testMonitor.name} (${testMonitor.id})`);

    // Test 4: Test get_monitor_degraded_settings function
    console.log('\n4️⃣ Testing monitor degraded settings retrieval...');
    
    const { data: monitorSettings, error: settingsError } = await supabase
      .rpc('get_monitor_degraded_settings', { monitor_id: testMonitor.id });

    if (settingsError) {
      console.log('   ❌ Error getting monitor settings:', settingsError.message);
    } else {
      console.log('   ✅ Monitor settings retrieved:', monitorSettings);
    }

    // Test 5: Test get_monitor_degraded_settings_rpc function (UI compatibility)
    console.log('\n5️⃣ Testing RPC compatibility function...');
    
    const { data: rpcSettings, error: rpcError } = await supabase
      .rpc('get_monitor_degraded_settings_rpc', { p_monitor_id: testMonitor.id });

    if (rpcError) {
      console.log('   ❌ Error with RPC function:', rpcError.message);
    } else {
      console.log('   ✅ RPC function works:', rpcSettings);
    }

    // Test 6: Test degraded status checking
    console.log('\n6️⃣ Testing degraded status detection...');
    
    // Test with high response time (should be degraded)
    const { data: degradedTest1, error: degradedError1 } = await supabase
      .rpc('is_monitor_degraded', {
        p_monitor_id: testMonitor.id,
        p_response_time: 5000, // 5 seconds - should be degraded
        p_status_code: 200,
        p_consecutive_failures: 0,
        p_error_rate: 0
      });

    if (degradedError1) {
      console.log('   ❌ Error testing degraded status:', degradedError1.message);
    } else {
      console.log('   ✅ High response time test:', {
        is_degraded: degradedTest1.is_degraded,
        reasons: degradedTest1.reasons
      });
    }

    // Test with normal response time (should not be degraded)
    const { data: degradedTest2, error: degradedError2 } = await supabase
      .rpc('is_monitor_degraded', {
        p_monitor_id: testMonitor.id,
        p_response_time: 200, // 200ms - should be fine
        p_status_code: 200,
        p_consecutive_failures: 0,
        p_error_rate: 0
      });

    if (degradedError2) {
      console.log('   ❌ Error testing normal status:', degradedError2.message);
    } else {
      console.log('   ✅ Normal response time test:', {
        is_degraded: degradedTest2.is_degraded,
        reasons: degradedTest2.reasons
      });
    }

    // Test 7: Test comprehensive status evaluation
    console.log('\n7️⃣ Testing comprehensive status evaluation...');
    
    const { data: statusEval, error: evalError } = await supabase
      .rpc('evaluate_monitor_status', {
        p_monitor_id: testMonitor.id,
        p_current_status: true,
        p_response_time: 1500, // Slightly high response time
        p_status_code: 200
      });

    if (evalError) {
      console.log('   ❌ Error in status evaluation:', evalError.message);
    } else {
      console.log('   ✅ Status evaluation result:', {
        status: statusEval.status,
        response_time: statusEval.response_time,
        consecutive_failures: statusEval.consecutive_failures,
        error_rate: statusEval.error_rate
      });
    }

    // Test 8: Test monitors with custom settings detection
    console.log('\n8️⃣ Testing custom settings detection...');
    
    const { data: customSettings, error: customError } = await supabase
      .rpc('get_monitors_with_degraded_settings', {
        p_monitor_ids: [testMonitor.id]
      });

    if (customError) {
      console.log('   ❌ Error checking custom settings:', customError.message);
    } else {
      console.log('   ✅ Custom settings check:', {
        monitor_has_custom_settings: customSettings.length > 0,
        custom_monitors: customSettings
      });
    }

    console.log('\n🎉 Degraded status handling tests completed!');

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Run the tests
if (require.main === module) {
  testDegradedStatusHandling()
    .then(() => {
      console.log('\n✅ All tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Tests failed:', error);
      process.exit(1);
    });
}

module.exports = { testDegradedStatusHandling };
