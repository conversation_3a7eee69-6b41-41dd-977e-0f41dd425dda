-- This script checks the permissions and policies for the avatars bucket
-- Run this in the Supabase SQL Editor

-- Check if the bucket exists and is public
SELECT id, name, public, owner FROM storage.buckets WHERE id = 'avatars';

-- Check existing policies for the avatars bucket
SELECT 
  policyname,
  tablename,
  schemaname,
  cmd AS operation,
  roles
FROM pg_policies 
WHERE schemaname = 'storage' 
AND tablename = 'objects'
AND policyname LIKE '%avatar%';

-- Check if the current role has the necessary permissions
SELECT 
  grantee, 
  table_schema, 
  table_name, 
  privilege_type
FROM information_schema.role_table_grants
WHERE table_schema = 'storage'
AND table_name = 'objects';

-- Check if RLS is enabled on the storage.objects table
SELECT 
  schemaname, 
  tablename, 
  rowsecurity
FROM pg_tables
WHERE schemaname = 'storage'
AND tablename = 'objects';
