-- This script creates the company-related tables and relationships
-- Run this in the Supabase SQL Editor

-- Create companies table
CREATE TABLE IF NOT EXISTS public.companies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create company_members table to manage user-company relationships
CREATE TABLE IF NOT EXISTS public.company_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('admin', 'member', 'viewer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    UNIQUE(company_id, user_id)
);

-- Add company_id to monitors table
ALTER TABLE public.monitors 
ADD COLUMN IF NOT EXISTS company_id UUID REFERENCES public.companies(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_company_members_company_id ON public.company_members(company_id);
CREATE INDEX IF NOT EXISTS idx_company_members_user_id ON public.company_members(user_id);
CREATE INDEX IF NOT EXISTS idx_monitors_company_id ON public.monitors(company_id);

-- Enable Row Level Security
ALTER TABLE public.companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.company_members ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view companies they are members of" ON public.companies;
DROP POLICY IF EXISTS "Company admins can update their companies" ON public.companies;
DROP POLICY IF EXISTS "Company admins can delete their companies" ON public.companies;
DROP POLICY IF EXISTS "Authenticated users can create companies" ON public.companies;
DROP POLICY IF EXISTS "Users can view company members for their companies" ON public.company_members;
DROP POLICY IF EXISTS "Company admins can manage company members" ON public.company_members;
DROP POLICY IF EXISTS "Users can view monitors in their companies" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can create monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can update monitors" ON public.monitors;
DROP POLICY IF EXISTS "Company admins can delete monitors" ON public.monitors;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.companies;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.company_members;
DROP POLICY IF EXISTS "Allow all operations for authenticated users" ON public.monitors;

-- Create policies for companies table
CREATE POLICY "Users can view companies they are members of"
ON public.companies
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can update their companies"
ON public.companies
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Company admins can delete their companies"
ON public.companies
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = companies.id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Authenticated users can create companies"
ON public.companies
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Create policies for company_members table
CREATE POLICY "Users can view company members for their companies"
ON public.company_members
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members AS cm
        WHERE cm.company_id = company_members.company_id
        AND cm.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can manage company members"
ON public.company_members
FOR ALL
USING (
    EXISTS (
        SELECT 1 FROM public.company_members AS cm
        WHERE cm.company_id = company_members.company_id
        AND cm.user_id = auth.uid()
        AND cm.role = 'admin'
    )
);

-- Update policies for monitors table
DROP POLICY IF EXISTS "Users can view their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can insert their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can update their own monitors" ON public.monitors;
DROP POLICY IF EXISTS "Users can delete their own monitors" ON public.monitors;

CREATE POLICY "Users can view monitors in their companies"
ON public.monitors
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
    )
);

CREATE POLICY "Company admins can create monitors"
ON public.monitors
FOR INSERT
TO authenticated
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Company admins can update monitors"
ON public.monitors
FOR UPDATE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

CREATE POLICY "Company admins can delete monitors"
ON public.monitors
FOR DELETE
USING (
    EXISTS (
        SELECT 1 FROM public.company_members
        WHERE company_members.company_id = monitors.company_id
        AND company_members.user_id = auth.uid()
        AND company_members.role = 'admin'
    )
);

-- Grant necessary permissions
GRANT ALL ON public.companies TO authenticated;
GRANT ALL ON public.company_members TO authenticated;
