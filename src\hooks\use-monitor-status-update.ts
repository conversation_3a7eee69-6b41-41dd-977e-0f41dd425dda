import { create } from 'zustand';

// Define the store type
interface MonitorStatusStore {
  // Map of monitor IDs to their latest status
  latestStatus: Record<string, {
    status: 'up' | 'down' | 'degraded' | 'paused';
    response_time?: number;
    timestamp?: string;
    type?: string;
    portResults?: Array<{
      port: number;
      status: boolean;
      error?: string;
    }>;
  }>;

  // Update a monitor's status
  updateMonitorStatus: (
    monitorId: string,
    status: 'up' | 'down' | 'degraded' | 'paused',
    response_time?: number,
    type?: string,
    portResults?: Array<{
      port: number;
      status: boolean;
      error?: string;
    }>
  ) => void;

  // Get a monitor's status
  getMonitorStatus: (monitorId: string) => {
    status: 'up' | 'down' | 'degraded' | 'paused';
    response_time?: number;
    timestamp?: string;
    type?: string;
    portResults?: Array<{
      port: number;
      status: boolean;
      error?: string;
    }>;
  } | null;

  // Check if a monitor has a status update
  hasStatusUpdate: (monitorId: string) => boolean;

  // Clear a monitor's status update
  clearStatusUpdate: (monitorId: string) => void;
}

// Create the store
export const useMonitorStatusStore = create<MonitorStatusStore>((set, get) => ({
  latestStatus: {},

  updateMonitorStatus: (monitorId, status, response_time, type, portResults) => {
    set((state) => ({
      latestStatus: {
        ...state.latestStatus,
        [monitorId]: {
          status,
          response_time,
          timestamp: new Date().toISOString(),
          type,
          portResults,
        },
      },
    }));
  },

  getMonitorStatus: (monitorId) => {
    const state = get();
    return state.latestStatus[monitorId] || null;
  },

  hasStatusUpdate: (monitorId) => {
    const state = get();
    return !!state.latestStatus[monitorId];
  },

  clearStatusUpdate: (monitorId) => {
    set((state) => {
      const newLatestStatus = { ...state.latestStatus };
      delete newLatestStatus[monitorId];
      return { latestStatus: newLatestStatus };
    });
  },
}));

// Hook to use the monitor status store
export function useMonitorStatusUpdate() {
  const {
    updateMonitorStatus,
    getMonitorStatus,
    hasStatusUpdate,
    clearStatusUpdate,
  } = useMonitorStatusStore();

  return {
    updateMonitorStatus,
    getMonitorStatus,
    hasStatusUpdate,
    clearStatusUpdate,
  };
}
