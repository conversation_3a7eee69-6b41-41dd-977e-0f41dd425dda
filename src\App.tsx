
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider, useAuth } from "./contexts/AuthContext";
import { SupabaseProvider } from "./contexts/SupabaseProvider";
import { CompanyProvider } from "./contexts/CompanyContext";
import { SidebarProvider } from "./contexts/SidebarContext";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import AddMonitor from "./pages/AddMonitor";
import EditMonitor from "./pages/EditMonitor";
import MonitorDetail from "./pages/MonitorDetail";
import Profile from "./pages/Profile";
import NotFound from "./pages/NotFound";
import CreateCompany from "./pages/CreateCompany";
import CompanySettings from "./pages/CompanySettings";
import Companies from "./pages/Companies";
import SubscriptionPage from "./pages/SubscriptionPage";
import SubscriptionTiersPage from "./pages/SubscriptionTiersPage";

import SuperadminTest from "./pages/SuperadminTest";
import SuperadminRoute from "./components/SuperadminRoute";

import GlobalSettings from "./pages/GlobalSettings";
import UserManagement from "./pages/UserManagement/index";

const queryClient = new QueryClient();

// Protected route wrapper
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <SupabaseProvider>
      <AuthProvider>
        <CompanyProvider>
          <SidebarProvider>
            <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />
              <Route path="/register" element={<Register />} />
              <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>} />
              <Route path="/add-monitor" element={<ProtectedRoute><AddMonitor /></ProtectedRoute>} />
              <Route path="/edit-monitor/:id" element={<ProtectedRoute><EditMonitor /></ProtectedRoute>} />
              <Route path="/monitor/:id" element={<ProtectedRoute><MonitorDetail /></ProtectedRoute>} />
              <Route path="/profile" element={<ProtectedRoute><Profile /></ProtectedRoute>} />
              <Route path="/companies" element={<ProtectedRoute><Companies /></ProtectedRoute>} />
              <Route path="/create-company" element={<SuperadminRoute><CreateCompany /></SuperadminRoute>} />
              <Route path="/company/:id/settings" element={<ProtectedRoute><CompanySettings /></ProtectedRoute>} />
              <Route path="/subscription" element={<ProtectedRoute><SubscriptionPage /></ProtectedRoute>} />
              <Route path="/subscription-tiers" element={<SuperadminRoute><SubscriptionTiersPage /></SuperadminRoute>} />

              <Route path="/superadmin-test" element={<SuperadminRoute><SuperadminTest /></SuperadminRoute>} />
              {/* Degraded settings moved to Global Settings page */}
              <Route path="/global-settings" element={<SuperadminRoute><GlobalSettings /></SuperadminRoute>} />
              <Route path="/users" element={<SuperadminRoute><UserManagement /></SuperadminRoute>} />
              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
            </TooltipProvider>
          </SidebarProvider>
        </CompanyProvider>
      </AuthProvider>
    </SupabaseProvider>
  </QueryClientProvider>
);

export default App;
