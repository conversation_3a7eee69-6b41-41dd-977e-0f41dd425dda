import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import AppLayout from "@/components/AppLayout";
import UnifiedHeader from "@/components/UnifiedHeader";
import DocumentTitle from "@/components/DocumentTitle";
import { Button } from "@/components/ui/button";
import { AlertTriangle } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const header = (
    <UnifiedHeader
      title="Page Not Found"
      icon={AlertTriangle}
    />
  );

  return (
    <AppLayout header={header}>
      <DocumentTitle title="404 - Page Not Found" />
      <div className="flex items-center justify-center h-[calc(100vh-64px)]">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4">404</h1>
          <p className="text-xl text-slate-600 dark:text-slate-400 mb-6">Oops! Page not found</p>
          <Button asChild>
            <Link to="/">Return to Home</Link>
          </Button>
        </div>
      </div>
    </AppLayout>
  );
};

export default NotFound;
