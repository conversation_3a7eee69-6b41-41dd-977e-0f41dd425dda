-- Update degraded status logic to only trigger on response time and status code issues
-- Other metrics (consecutive failures, error rate) are tracked for future customer advisement

CREATE OR REPLACE FUNCTION is_monitor_degraded(
    p_monitor_id UUID,
    p_response_time INTEGER,
    p_status_code INTEGER DEFAULT NULL,
    p_consecutive_failures INTEGER DEFAULT 0,
    p_error_rate DECIMAL DEFAULT 0
) RETURNS JSONB AS $$
DECLARE
    settings JSONB;
    is_degraded BOOLEAN := FALSE;
    degraded_reasons TEXT[] := ARRAY[]::TEXT[];
BEGIN
    -- Get the effective degraded settings for this monitor
    SELECT get_monitor_degraded_settings(p_monitor_id) INTO settings;
    
    -- Check response time threshold (TRIGGERS DEGRADED STATUS)
    IF p_response_time IS NOT NULL AND p_response_time > (settings->>'response_time')::INTEGER THEN
        is_degraded := TRUE;
        degraded_reasons := array_append(degraded_reasons, 
            format('Response time %sms exceeds threshold %sms', 
                p_response_time, settings->>'response_time'));
    END IF;
    
    -- Check status code threshold (TRIGGERS DEGRADED STATUS)
    IF p_status_code IS NOT NULL AND settings->'status_codes' IS NOT NULL THEN
        IF p_status_code = ANY(ARRAY(SELECT jsonb_array_elements_text(settings->'status_codes'))::INTEGER[]) THEN
            is_degraded := TRUE;
            degraded_reasons := array_append(degraded_reasons, 
                format('Status code %s indicates degraded service', p_status_code));
        END IF;
    END IF;
    
    -- Track consecutive failures threshold (FOR FUTURE CUSTOMER ADVISEMENT - DOES NOT TRIGGER DEGRADED)
    -- Note: This is tracked but does not set is_degraded = TRUE
    
    -- Track error rate threshold (FOR FUTURE CUSTOMER ADVISEMENT - DOES NOT TRIGGER DEGRADED)  
    -- Note: This is tracked but does not set is_degraded = TRUE
    
    -- Return comprehensive result
    RETURN jsonb_build_object(
        'is_degraded', is_degraded,
        'reasons', degraded_reasons,
        'settings_used', settings,
        'checked_values', jsonb_build_object(
            'response_time', p_response_time,
            'status_code', p_status_code,
            'consecutive_failures', p_consecutive_failures,
            'error_rate', p_error_rate
        ),
        'advisory_metrics', jsonb_build_object(
            'consecutive_failures_threshold_exceeded', 
                CASE WHEN p_consecutive_failures >= (settings->>'consecutive_failures')::INTEGER 
                     THEN TRUE ELSE FALSE END,
            'error_rate_threshold_exceeded', 
                CASE WHEN p_error_rate >= (settings->>'error_rate')::DECIMAL 
                     THEN TRUE ELSE FALSE END,
            'consecutive_failures_info', 
                format('Consecutive failures: %s (threshold: %s)', 
                    p_consecutive_failures, settings->>'consecutive_failures'),
            'error_rate_info', 
                format('Error rate: %s%% (threshold: %s%%)', 
                    p_error_rate, settings->>'error_rate')
        )
    );
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the updated logic
COMMENT ON FUNCTION is_monitor_degraded(UUID, INTEGER, INTEGER, INTEGER, DECIMAL) IS 
'Updated function to check if a monitor is in degraded state. Only response time and status code issues trigger degraded status. Consecutive failures and error rates are tracked for future customer advisement but do not trigger degraded status.';
