-- This script fixes issues with the monitor_degraded_settings table
-- and adds documentation about what each field does
-- Run this in the Supabase SQL Editor

-- Check if the table exists
SELECT EXISTS (
  SELECT FROM information_schema.tables 
  WHERE table_schema = 'public' 
  AND table_name = 'monitor_degraded_settings'
) AS table_exists;

-- Check the current structure of the table
SELECT 
  column_name, 
  data_type, 
  is_nullable, 
  column_default
FROM 
  information_schema.columns
WHERE 
  table_schema = 'public' 
  AND table_name = 'monitor_degraded_settings'
ORDER BY 
  ordinal_position;

-- Add comments to the table and columns to document what they do
COMMENT ON TABLE public.monitor_degraded_settings IS 
'Stores monitor-specific degraded threshold settings that override global defaults.
If a field is NULL, the system will use the global setting from the degraded_settings table.';

COMMENT ON COLUMN public.monitor_degraded_settings.id IS 
'Unique identifier for the record';

COMMENT ON COLUMN public.monitor_degraded_settings.monitor_id IS 
'Reference to the monitor this setting applies to';

COMMENT ON COLUMN public.monitor_degraded_settings.response_time IS 
'Response time threshold in milliseconds. If a monitor''s response time exceeds this value, it will be considered degraded.
NULL means use the global setting.';

COMMENT ON COLUMN public.monitor_degraded_settings.error_rate IS 
'Error rate threshold as a percentage (0-100). If a monitor''s error rate exceeds this value, it will be considered degraded.
NULL means use the global setting.';

COMMENT ON COLUMN public.monitor_degraded_settings.status_codes IS 
'Array of HTTP status codes that indicate a degraded state rather than a complete failure.
Common examples include 429 (Too Many Requests) and 503 (Service Unavailable).
NULL means use the global setting.';

COMMENT ON COLUMN public.monitor_degraded_settings.consecutive_failures IS 
'Number of consecutive partial failures before a monitor is considered degraded.
NULL means use the global setting.';

COMMENT ON COLUMN public.monitor_degraded_settings.created_at IS 
'Timestamp when this record was created';

COMMENT ON COLUMN public.monitor_degraded_settings.updated_at IS 
'Timestamp when this record was last updated';

-- Check if there are any records in the table
SELECT COUNT(*) AS record_count FROM public.monitor_degraded_settings;

-- Let's check if the get_monitor_degraded_settings function exists
SELECT EXISTS (
  SELECT FROM pg_proc
  WHERE proname = 'get_monitor_degraded_settings'
) AS function_exists;

-- Let's check if the upsert_monitor_degraded_settings function exists
SELECT EXISTS (
  SELECT FROM pg_proc
  WHERE proname = 'upsert_monitor_degraded_settings'
) AS function_exists;

-- Create or replace the upsert_monitor_degraded_settings function
CREATE OR REPLACE FUNCTION upsert_monitor_degraded_settings(
    p_monitor_id UUID,
    p_response_time INTEGER DEFAULT NULL,
    p_error_rate INTEGER DEFAULT NULL,
    p_status_codes INTEGER[] DEFAULT NULL,
    p_consecutive_failures INTEGER DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
    result JSONB;
BEGIN
    -- Insert or update the monitor settings
    INSERT INTO public.monitor_degraded_settings (
        monitor_id,
        response_time,
        error_rate,
        status_codes,
        consecutive_failures,
        updated_at
    ) VALUES (
        p_monitor_id,
        p_response_time,
        p_error_rate,
        p_status_codes,
        p_consecutive_failures,
        now()
    )
    ON CONFLICT (monitor_id) DO UPDATE SET
        response_time = EXCLUDED.response_time,
        error_rate = EXCLUDED.error_rate,
        status_codes = EXCLUDED.status_codes,
        consecutive_failures = EXCLUDED.consecutive_failures,
        updated_at = now()
    RETURNING jsonb_build_object(
        'id', id,
        'monitor_id', monitor_id,
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures,
        'created_at', created_at,
        'updated_at', updated_at
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Create or replace the get_monitor_degraded_settings function
CREATE OR REPLACE FUNCTION get_monitor_degraded_settings(monitor_id UUID)
RETURNS JSONB AS $$
DECLARE
    monitor_settings JSONB;
    global_settings JSONB;
    result JSONB;
BEGIN
    -- Get monitor-specific settings
    SELECT jsonb_build_object(
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures
    )
    INTO monitor_settings
    FROM public.monitor_degraded_settings
    WHERE monitor_degraded_settings.monitor_id = get_monitor_degraded_settings.monitor_id;
    
    -- Get global settings
    SELECT jsonb_build_object(
        'response_time', response_time,
        'error_rate', error_rate,
        'status_codes', status_codes,
        'consecutive_failures', consecutive_failures
    )
    INTO global_settings
    FROM public.degraded_settings
    LIMIT 1;
    
    -- If no global settings exist, create default values
    IF global_settings IS NULL THEN
        global_settings := jsonb_build_object(
            'response_time', 1000,
            'error_rate', 10,
            'status_codes', ARRAY[429, 503]::INTEGER[],
            'consecutive_failures', 2
        );
    END IF;
    
    -- Combine settings, using monitor-specific when available, falling back to global
    result := global_settings;
    
    IF monitor_settings IS NOT NULL THEN
        -- Only override fields that are not null in monitor settings
        IF monitor_settings->>'response_time' IS NOT NULL THEN
            result := jsonb_set(result, '{response_time}', monitor_settings->'response_time');
        END IF;
        
        IF monitor_settings->>'error_rate' IS NOT NULL THEN
            result := jsonb_set(result, '{error_rate}', monitor_settings->'error_rate');
        END IF;
        
        IF monitor_settings->>'status_codes' IS NOT NULL THEN
            result := jsonb_set(result, '{status_codes}', monitor_settings->'status_codes');
        END IF;
        
        IF monitor_settings->>'consecutive_failures' IS NOT NULL THEN
            result := jsonb_set(result, '{consecutive_failures}', monitor_settings->'consecutive_failures');
        END IF;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Add a comment to the function
COMMENT ON FUNCTION get_monitor_degraded_settings(UUID) IS 
'Returns the effective degraded threshold settings for a monitor.
Combines global settings with any monitor-specific overrides.';

COMMENT ON FUNCTION upsert_monitor_degraded_settings(UUID, INTEGER, INTEGER, INTEGER[], INTEGER) IS
'Creates or updates monitor-specific degraded threshold settings.
Pass NULL for any parameter to use the global setting for that field.';

-- Create the get_monitor_degraded_settings_rpc function (alias for UI compatibility)
CREATE OR REPLACE FUNCTION get_monitor_degraded_settings_rpc(p_monitor_id UUID)
RETURNS JSONB AS $$
BEGIN
    RETURN get_monitor_degraded_settings(p_monitor_id);
END;
$$ LANGUAGE plpgsql;

-- Create function to get monitors with custom degraded settings
CREATE OR REPLACE FUNCTION get_monitors_with_degraded_settings(p_monitor_ids UUID[])
RETURNS TABLE(monitor_id UUID) AS $$
BEGIN
    RETURN QUERY
    SELECT mds.monitor_id
    FROM public.monitor_degraded_settings mds
    WHERE mds.monitor_id = ANY(p_monitor_ids);
END;
$$ LANGUAGE plpgsql;

-- Add comments to the new functions
COMMENT ON FUNCTION get_monitor_degraded_settings_rpc(UUID) IS
'Alias for get_monitor_degraded_settings function for UI compatibility.';

COMMENT ON FUNCTION get_monitors_with_degraded_settings(UUID[]) IS
'Returns monitor IDs that have custom degraded settings configured.';

-- Refresh the schema cache
NOTIFY pgrst, 'reload schema';
