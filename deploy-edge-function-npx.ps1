# PowerShell script to deploy the monitor-checker Edge Function to Supabase using npx

Write-Host "Deploying monitor-checker Edge Function to Supabase using npx..." -ForegroundColor Cyan

# Navigate to the function directory
Push-Location -Path "supabase/functions/monitor-checker"

try {
    # Deploy the function using npx
    Write-Host "Running deployment command..." -ForegroundColor Yellow
    npx supabase functions deploy monitor-checker --project-ref axcfqilzeombkbzebeym
    
    # Check if deployment was successful
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Deployment completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "Deployment failed with exit code $LASTEXITCODE" -ForegroundColor Red
    }
} catch {
    Write-Host "An error occurred during deployment: $_" -ForegroundColor Red
} finally {
    # Return to the original directory
    Pop-Location
}

Write-Host "Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
