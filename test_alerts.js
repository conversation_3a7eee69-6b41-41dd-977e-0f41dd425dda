import { createClient } from '@supabase/supabase-js';
import { checkAndSendAlerts } from './monitor_alerts.js';
import dotenv from 'dotenv';

dotenv.config();

const supabase = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_KEY
);

async function setupTestMonitors() {
    console.log('Setting up test monitors...');
    
    // Use test user ID
    const userId = '3a19a1f7-3f61-48a8-949c-caa3fba04924'; // Test user ID from existing monitor
    
    // Create test monitors
    const monitors = [
        {
            user_id: userId,
            name: 'Test Failed Monitor',
            target: 'https://test-failed.example.com',
            type: 'http',
            interval: 5,
            timeout: 30,
            active: true
        },
        {
            user_id: userId,
            name: 'Test Slow Monitor',
            target: 'https://test-slow.example.com',
            type: 'http',
            interval: 5,
            timeout: 30,
            active: true
        },
        {
            user_id: userId,
            name: 'Test Healthy Monitor',
            target: 'https://test-healthy.example.com',
            type: 'http',
            interval: 5,
            timeout: 30,
            active: true
        }
    ];

    // Insert test monitors
    const { data: createdMonitors, error: monitorError } = await supabase
        .from('monitors')
        .insert(monitors)
        .select();

    if (monitorError) {
        console.error('Error creating monitors:', monitorError);
        return;
    }

    console.log('Created test monitors:', createdMonitors);
    return createdMonitors;
}

async function simulateMonitorHistory(monitors) {
    console.log('Simulating monitor history...');
    
    const now = new Date();
    const history = [];

    // For the failed monitor, create 4 consecutive failures
    for (let i = 0; i < 4; i++) {
        history.push({
            monitor_id: monitors[0].id,
            timestamp: new Date(now - (i * 5 * 60000)).toISOString(), // 5 minutes apart
            status: false,
            response_time: 1000,
            error_message: 'Connection refused'
        });
    }

    // For the slow monitor, create entries with high response times
    for (let i = 0; i < 4; i++) {
        history.push({
            monitor_id: monitors[1].id,
            timestamp: new Date(now - (i * 5 * 60000)).toISOString(),
            status: true,
            response_time: Math.floor(6000 + Math.random() * 1000), // Between 6-7 seconds
            error_message: null
        });
    }

    // For the healthy monitor, create normal entries
    for (let i = 0; i < 4; i++) {
        history.push({
            monitor_id: monitors[2].id,
            timestamp: new Date(now - (i * 5 * 60000)).toISOString(),
            status: true,
            response_time: Math.floor(200 + Math.random() * 100), // Between 200-300ms
            error_message: null
        });
    }

    // Insert monitor history
    const { error: historyError } = await supabase
        .from('monitor_history')
        .insert(history);

    if (historyError) {
        console.error('Error creating monitor history:', historyError);
        return;
    }

    console.log('Created test monitor history entries:', history.length);
}

async function cleanupTestData(monitors) {
    console.log('Cleaning up test data...');
    
    // Delete monitor history
    const { error: historyError } = await supabase
        .from('monitor_history')
        .delete()
        .in('monitor_id', monitors.map(m => m.id));

    if (historyError) {
        console.error('Error deleting monitor history:', historyError);
    }

    // Delete monitors
    const { error: monitorError } = await supabase
        .from('monitors')
        .delete()
        .in('id', monitors.map(m => m.id));

    if (monitorError) {
        console.error('Error deleting monitors:', monitorError);
    }

    console.log('Test data cleaned up');
}

async function runAlertTest() {
    console.log('Starting alert system test...');
    
    try {
        // Setup test monitors
        const monitors = await setupTestMonitors();
        if (!monitors) return;

        // Simulate monitor history
        await simulateMonitorHistory(monitors);

        // Wait a moment for data to be processed
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Run the alert check
        console.log('Testing alert system...');
        await checkAndSendAlerts();

        // Wait for emails to be sent
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Cleanup test data
        await cleanupTestData(monitors);

        console.log('Alert system test completed!');
        
    } catch (error) {
        console.error('Error in alert test:', error);
    }
}

// Run the test
runAlertTest();
