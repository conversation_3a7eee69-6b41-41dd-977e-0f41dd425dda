-- This script migrates existing company members to the new role system
-- Run this in the Supabase SQL Editor

-- Make sure the uuid-ossp extension is available
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Add role enum type if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('user', 'admin', 'superadmin');
    END IF;
END$$;

-- Add role_type column to company_members table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_schema = 'public' 
                   AND table_name = 'company_members' 
                   AND column_name = 'role_type') THEN
        ALTER TABLE public.company_members ADD COLUMN role_type user_role DEFAULT 'user';
    END IF;
END$$;

-- Migrate existing roles to the new role_type column
UPDATE public.company_members
SET role_type = 
    CASE 
        WHEN role = 'admin' THEN 'admin'::user_role
        WHEN role = 'member' THEN 'user'::user_role
        WHEN role = 'viewer' THEN 'user'::user_role
        ELSE 'user'::user_role
    END;

-- Add deleted column to monitors table if it doesn't exist
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted BOOLEAN DEFAULT FALSE;
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP WITH TIME ZONE;
ALTER TABLE public.monitors ADD COLUMN IF NOT EXISTS deleted_by UUID;

-- Create a function to assign superadmin role to a user
CREATE OR REPLACE FUNCTION assign_superadmin_role(
  user_id UUID,
  company_id UUID
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
BEGIN
  -- Check if the user is already a member of the company
  IF EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = assign_superadmin_role.user_id
    AND company_id = assign_superadmin_role.company_id
  ) THEN
    -- Update the existing membership
    UPDATE public.company_members
    SET role_type = 'superadmin'
    WHERE user_id = assign_superadmin_role.user_id
    AND company_id = assign_superadmin_role.company_id
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  ELSE
    -- Create a new membership
    INSERT INTO public.company_members (user_id, company_id, role_type)
    VALUES (assign_superadmin_role.user_id, assign_superadmin_role.company_id, 'superadmin')
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to assign admin role to a user
CREATE OR REPLACE FUNCTION assign_admin_role(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL -- The user performing the action
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user has admin or superadmin role in the company
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = assign_admin_role.company_id
        AND user_id = admin_user_id
        AND role_type = 'superadmin'
      ) THEN TRUE
      ELSE FALSE
    END INTO is_superadmin;
    
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = assign_admin_role.company_id
        AND user_id = admin_user_id
        AND (role_type = 'admin' OR role_type = 'superadmin')
      ) THEN TRUE
      ELSE FALSE
    END INTO is_admin;
  
  -- Only allow admins and superadmins to assign admin roles
  IF NOT (is_admin OR is_superadmin) THEN
    RAISE EXCEPTION 'User does not have permission to assign admin roles';
  END IF;
  
  -- Check if the user is already a member of the company
  IF EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = assign_admin_role.user_id
    AND company_id = assign_admin_role.company_id
  ) THEN
    -- Update the existing membership
    UPDATE public.company_members
    SET role_type = 'admin'
    WHERE user_id = assign_admin_role.user_id
    AND company_id = assign_admin_role.company_id
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  ELSE
    -- Create a new membership
    INSERT INTO public.company_members (user_id, company_id, role_type)
    VALUES (assign_admin_role.user_id, assign_admin_role.company_id, 'admin')
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to assign user role to a user
CREATE OR REPLACE FUNCTION assign_user_role(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL -- The user performing the action
) RETURNS JSONB AS $$
DECLARE
  result JSONB;
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user has admin or superadmin role in the company
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = assign_user_role.company_id
        AND user_id = admin_user_id
        AND role_type = 'superadmin'
      ) THEN TRUE
      ELSE FALSE
    END INTO is_superadmin;
    
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = assign_user_role.company_id
        AND user_id = admin_user_id
        AND (role_type = 'admin' OR role_type = 'superadmin')
      ) THEN TRUE
      ELSE FALSE
    END INTO is_admin;
  
  -- Only allow admins and superadmins to assign user roles
  IF NOT (is_admin OR is_superadmin) THEN
    RAISE EXCEPTION 'User does not have permission to assign user roles';
  END IF;
  
  -- Check if the user is already a member of the company
  IF EXISTS (
    SELECT 1 FROM public.company_members
    WHERE user_id = assign_user_role.user_id
    AND company_id = assign_user_role.company_id
  ) THEN
    -- Update the existing membership
    UPDATE public.company_members
    SET role_type = 'user'
    WHERE user_id = assign_user_role.user_id
    AND company_id = assign_user_role.company_id
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  ELSE
    -- Create a new membership
    INSERT INTO public.company_members (user_id, company_id, role_type)
    VALUES (assign_user_role.user_id, assign_user_role.company_id, 'user')
    RETURNING jsonb_build_object(
      'id', id,
      'user_id', user_id,
      'company_id', company_id,
      'role_type', role_type
    ) INTO result;
  END IF;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to remove a user from a company
CREATE OR REPLACE FUNCTION remove_company_member(
  user_id UUID,
  company_id UUID,
  admin_user_id UUID DEFAULT NULL -- The user performing the action
) RETURNS BOOLEAN AS $$
DECLARE
  is_admin BOOLEAN;
  is_superadmin BOOLEAN;
BEGIN
  -- Check if the admin user has admin or superadmin role in the company
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = remove_company_member.company_id
        AND user_id = admin_user_id
        AND role_type = 'superadmin'
      ) THEN TRUE
      ELSE FALSE
    END INTO is_superadmin;
    
  SELECT 
    CASE 
      WHEN EXISTS (
        SELECT 1 FROM company_members
        WHERE company_id = remove_company_member.company_id
        AND user_id = admin_user_id
        AND (role_type = 'admin' OR role_type = 'superadmin')
      ) THEN TRUE
      ELSE FALSE
    END INTO is_admin;
  
  -- Only allow admins and superadmins to remove users
  IF NOT (is_admin OR is_superadmin) THEN
    RAISE EXCEPTION 'User does not have permission to remove company members';
  END IF;
  
  -- Don't allow removing the last admin or superadmin
  IF (
    SELECT role_type FROM company_members
    WHERE user_id = remove_company_member.user_id
    AND company_id = remove_company_member.company_id
  ) IN ('admin', 'superadmin') AND (
    SELECT COUNT(*) FROM company_members
    WHERE company_id = remove_company_member.company_id
    AND role_type IN ('admin', 'superadmin')
  ) <= 1 THEN
    RAISE EXCEPTION 'Cannot remove the last admin or superadmin from a company';
  END IF;
  
  -- Remove the user from the company
  DELETE FROM public.company_members
  WHERE user_id = remove_company_member.user_id
  AND company_id = remove_company_member.company_id;
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Verify the migration
SELECT 
    cm.id,
    cm.user_id,
    cm.company_id,
    cm.role,
    cm.role_type
FROM 
    public.company_members cm
ORDER BY 
    cm.company_id, cm.user_id;
