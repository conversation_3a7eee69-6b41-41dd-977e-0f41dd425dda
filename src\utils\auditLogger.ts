import { supabase } from '@/integrations/supabase/client';

/**
 * Logs a user action to the audit_logs table
 *
 * @param userId - The ID of the user performing the action
 * @param actionType - The type of action (create, update, delete, soft_delete, etc.)
 * @param tableName - The name of the table being affected
 * @param recordId - The ID of the record being affected
 * @param oldData - The data before the change (for updates and deletes)
 * @param newData - The data after the change (for creates and updates)
 * @param ipAddress - The IP address of the user (optional)
 * @param userAgent - The user agent of the user (optional)
 * @returns The ID of the created audit log entry, or null if there was an error
 */
export async function logAction(
  userId: string,
  actionType: 'create' | 'update' | 'delete' | 'soft_delete' | 'restore' | string,
  tableName: string,
  recordId: string,
  oldData?: any,
  newData?: any,
  ipAddress?: string,
  userAgent?: string
): Promise<string | null> {
  try {
    // Try to use the RPC function
    try {
      const { data, error } = await supabase.rpc('log_action', {
        p_user_id: userId,
        p_action_type: actionType,
        p_table_name: tableName,
        p_record_id: recordId,
        p_old_data: oldData ? JSON.stringify(oldData) : null,
        p_new_data: newData ? JSON.stringify(newData) : null,
        p_ip_address: ipAddress,
        p_user_agent: userAgent
      });

      if (error) {
        // If the RPC function fails, try direct insert
        console.warn('Error using log_action RPC, trying direct insert:', error);
        throw error;
      }

      return data;
    } catch (rpcError) {
      // Fallback: Try to insert directly into the audit_logs table
      try {
        const { data: insertData, error: insertError } = await supabase
          .from('audit_logs')
          .insert({
            user_id: userId,
            action_type: actionType,
            table_name: tableName,
            record_id: recordId,
            old_data: oldData,
            new_data: newData,
            ip_address: ipAddress,
            user_agent: userAgent
          })
          .select('id')
          .single();

        if (insertError) {
          console.error('Error inserting audit log:', insertError);
          return null;
        }

        return insertData.id;
      } catch (insertError) {
        // If direct insert fails too, just log to console and continue
        console.error('Failed to log action to audit_logs:', insertError);
        // Don't fail the main operation if logging fails
        return null;
      }
    }
  } catch (error) {
    console.error('Exception in logAction:', error);
    return null;
  }
}

/**
 * Gets the client's IP address from the request
 * This is a placeholder - in a real application, you would get this from the request
 *
 * @returns A dummy IP address for now
 */
export function getClientIp(): string {
  // In a real application, you would get this from the request
  // For now, return a placeholder
  return '127.0.0.1';
}

/**
 * Gets the client's user agent from the request
 *
 * @returns The user agent string from the browser
 */
export function getUserAgent(): string {
  if (typeof window !== 'undefined') {
    return window.navigator.userAgent;
  }
  return 'Unknown';
}
