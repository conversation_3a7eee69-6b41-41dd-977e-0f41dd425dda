-- This script creates the get_current_user_companies RPC function
-- Run this in the Supabase SQL Editor

-- Create the function to get companies for the current user
CREATE OR REPLACE FUNCTION get_current_user_companies()
RETURNS SETOF public.companies AS $$
BEGIN
  RETURN QUERY
  SELECT c.*
  FROM public.companies c
  JOIN public.company_members cm ON c.id = cm.company_id
  WHERE cm.user_id = auth.uid();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_current_user_companies() TO authenticated;
