
import React, { useState, useEffect } from 'react';
import { supabase } from "@/integrations/supabase/client";
import { useMonitors } from "@/hooks/use-monitors";
import { useMonitorHistory } from "@/hooks/use-monitor-history";
import { useCompanyRoles } from "@/hooks/use-company-roles";
import { Database } from "@/integrations/supabase/types";
import MonitorCard from "@/components/MonitorCard";
import { toast } from "@/components/ui/use-toast";
import { format } from "date-fns";
import { formatDateTime } from "@/utils/dateFormat";

type Monitor = Database['public']['Tables']['monitors']['Row'];

type MonitorWithStatus = Monitor & {
  status?: 'up' | 'down' | 'degraded' | 'paused';
  uptime?: string;
  lastChecked?: string;
  responseTime?: string;
  hasCustomDegradedSettings?: boolean;
};

interface MonitorListProps {
  monitors?: any[];
}

const MonitorList = ({ monitors = [] }: MonitorListProps) => {
  const { useToggleMonitorStatusMutation, useSoftDeleteMonitorMutation } = useMonitors();
  const { useGlobalSuperadminQuery } = useCompanyRoles();
  const { data: isSuperadmin = false } = useGlobalSuperadminQuery();

  const toggleMonitorStatus = useToggleMonitorStatusMutation();
  const deleteMonitor = useSoftDeleteMonitorMutation();
  const handleToggleStatus = (id: string, active: boolean) => {
    toggleMonitorStatus.mutate({ id, active }, {
      // No success toast needed
    });
  };

  const handleDelete = (id: string, name: string) => {
    if (confirm(`Are you sure you want to delete the monitor "${name}"?`)) {
      console.log(`Attempting to delete monitor: ${id} (${name})`);
      deleteMonitor.mutate(id, {
        onSuccess: () => {
          console.log(`Successfully deleted monitor: ${id} (${name})`);
          toast({
            title: 'Monitor deleted',
            description: 'The monitor has been deleted successfully.',
          });
        },
        onError: (error) => {
          console.error(`Error deleting monitor ${id} (${name}):`, error);
          // Toast is already shown in the softDeleteMonitor function
        }
      });
    }
  };

  // Format the date for last checked
  const formatLastChecked = (date: string | null) => {
    if (!date) return 'Never';

    const now = new Date();
    const checkDate = new Date(date);
    const diffMs = now.getTime() - checkDate.getTime();
    const diffMins = Math.floor(diffMs / 60000);

    if (diffMins < 1) return 'Just now';
    if (diffMins === 1) return '1 minute ago';
    if (diffMins < 60) return `${diffMins} minutes ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours === 1) return '1 hour ago';
    if (diffHours < 24) return `${diffHours} hours ago`;

    // For older checks, show the actual time instead of "X days ago"
    return formatDateTime(checkDate);
  };

  // Use monitor history hook to get the latest status for each monitor
  const { useGetLatestHistoryForMonitors } = useMonitorHistory();
  const { data: monitorHistories = {}, isLoading: historiesLoading } = useGetLatestHistoryForMonitors(
    monitors.map(m => m.id)
  );

  // Check for custom degraded settings
  const [monitorDegradedSettings, setMonitorDegradedSettings] = useState<Record<string, boolean>>({});

  // Fetch custom degraded settings for all monitors
  useEffect(() => {
    const fetchDegradedSettings = async () => {
      try {
        // Only fetch if user is superadmin or admin
        if (!isSuperadmin) {
          return;
        }

        const { data, error } = await supabase
          .rpc('get_monitors_with_degraded_settings', {
            p_monitor_ids: monitors.map(m => m.id)
          });

        if (error) throw error;

        const settingsMap: Record<string, boolean> = {};
        data?.forEach(item => {
          settingsMap[item.monitor_id] = true;
        });

        setMonitorDegradedSettings(settingsMap);
      } catch (err) {
        console.error('Error fetching degraded settings:', err);
      }
    };

    if (monitors.length > 0) {
      fetchDegradedSettings();
    }
  }, [monitors, isSuperadmin]);

  // Convert database monitors to UI format with status
  const enhancedMonitors: MonitorWithStatus[] = monitors.map(monitor => {
    const history = monitorHistories[monitor.id];

    // Determine status based on active flag and monitor status
    let status: 'up' | 'down' | 'degraded' | 'paused' = 'up';
    if (!monitor.active) {
      status = 'paused';
    } else {
      // Use the status directly from the monitors table
      if (typeof monitor.status === 'string') {
        // Make sure the status is one of the allowed values
        if (['up', 'down', 'degraded'].includes(monitor.status.toLowerCase())) {
          status = monitor.status.toLowerCase() as 'up' | 'down' | 'degraded';
        } else {
          // Default to 'up' for any other string value
          status = 'up';
        }
      } else if (typeof monitor.status === 'boolean') {
        // Handle legacy boolean status
        status = monitor.status ? 'up' : 'down';
      }
    }

    // Calculate uptime from history if available
    const uptime = history?.uptime_24h ? `${history.uptime_24h.toFixed(1)}%` : 'N/A';

    // Format response time
    const responseTime = history?.response_time
      ? `${history.response_time}ms`
      : 'N/A';

    // Format last checked time
    const lastChecked = history?.timestamp
      ? formatLastChecked(history.timestamp)
      : 'Never';

    // Check if this monitor has custom degraded settings
    const hasCustomDegradedSettings = monitorDegradedSettings[monitor.id] || false;

    return {
      ...monitor,
      status,
      uptime,
      lastChecked,
      responseTime,
      hasCustomDegradedSettings,
    };
  });

  // Sort monitors by status: down, degraded, up, paused
  const sortedMonitors = [...enhancedMonitors].sort((a, b) => {
    const statusOrder = {
      'down': 0,
      'degraded': 1,
      'up': 2,
      'paused': 3
    };

    const statusA = statusOrder[a.status || 'up'];
    const statusB = statusOrder[b.status || 'up'];

    return statusA - statusB;
  });

  // Debug logging
  console.log('MonitorList - monitors:', monitors);
  console.log('MonitorList - enhancedMonitors:', enhancedMonitors);
  console.log('MonitorList - sortedMonitors:', sortedMonitors);

  // Ensure all monitors have the required properties
  const validMonitors = sortedMonitors.filter(monitor => {
    if (!monitor || !monitor.id || !monitor.name) {
      console.warn('Invalid monitor found:', monitor);
      return false;
    }
    return true;
  });

  // Check if we have any monitors to display
  if (!validMonitors || validMonitors.length === 0) {
    return (
      <div className="text-center py-8 bg-slate-100 dark:bg-slate-800 rounded">
        <p className="text-slate-500">No monitors to display</p>
        <p className="text-xs mt-2">Received {monitors.length} monitors, but only {validMonitors.length} could be processed</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {validMonitors.map((monitor) => (
        <MonitorCard
          key={monitor.id}
          monitor={monitor}
          isSuperadmin={isSuperadmin}
          onToggleStatus={handleToggleStatus}
          onDelete={handleDelete}
        />
      ))}
    </div>
  );
};

export default MonitorList;
