import { supabase } from '@/integrations/supabase/client';
import { SubscriptionTier, CompanySubscriptionInfo, SubscriptionOverrides } from '@/types/subscription';

/**
 * Get all subscription tiers
 */
export async function getSubscriptionTiers(): Promise<SubscriptionTier[]> {
  const { data, error } = await supabase
    .from('subscription_tiers')
    .select('*')
    .order('max_monitors');

  if (error) {
    console.error('Error fetching subscription tiers:', error);
    throw error;
  }

  return data || [];
}

/**
 * Get a specific subscription tier by ID
 */
export async function getSubscriptionTierById(id: string): Promise<SubscriptionTier | null> {
  const { data, error } = await supabase
    .from('subscription_tiers')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    if (error.code === 'PGRST116') { // Record not found
      return null;
    }
    console.error('Error fetching subscription tier:', error);
    throw error;
  }

  return data;
}

/**
 * Get a specific subscription tier by name
 */
export async function getSubscriptionTierByName(name: string): Promise<SubscriptionTier | null> {
  const { data, error } = await supabase
    .from('subscription_tiers')
    .select('*')
    .eq('name', name)
    .single();

  if (error) {
    if (error.code === 'PGRST116') { // Record not found
      return null;
    }
    console.error('Error fetching subscription tier:', error);
    throw error;
  }

  return data;
}

/**
 * Create a new subscription tier (superadmin only)
 */
export async function createSubscriptionTier(
  tier: Omit<SubscriptionTier, 'id' | 'created_at' | 'updated_at'>
): Promise<SubscriptionTier> {
  const { data, error } = await supabase
    .from('subscription_tiers')
    .insert(tier)
    .select()
    .single();

  if (error) {
    console.error('Error creating subscription tier:', error);
    throw error;
  }

  return data;
}

/**
 * Update a subscription tier (superadmin only)
 */
export async function updateSubscriptionTier(
  id: string,
  updates: Partial<Omit<SubscriptionTier, 'id' | 'created_at' | 'updated_at'>>
): Promise<SubscriptionTier> {
  const { data, error } = await supabase
    .from('subscription_tiers')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating subscription tier:', error);
    throw error;
  }

  return data;
}

/**
 * Delete a subscription tier (superadmin only)
 */
export async function deleteSubscriptionTier(id: string): Promise<void> {
  // First check if any companies are using this tier
  const { data: companies, error: checkError } = await supabase
    .from('companies')
    .select('id, name')
    .eq('subscription_tier_id', id)
    .eq('deleted', false);

  if (checkError) {
    console.error('Error checking companies using tier:', checkError);
    throw checkError;
  }

  // If companies are using this tier, throw an error
  if (companies && companies.length > 0) {
    throw new Error(`Cannot delete tier: ${companies.length} companies are using it. Reassign these companies to another tier first.`);
  }

  // If no companies are using it, delete the tier
  const { error } = await supabase
    .from('subscription_tiers')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting subscription tier:', error);
    throw error;
  }
}

/**
 * Get company subscription information
 */
export async function getCompanySubscriptionInfo(companyId: string): Promise<CompanySubscriptionInfo> {
  const { data, error } = await supabase
    .rpc('get_company_subscription_info', { company_id: companyId });

  if (error) {
    console.error('Error fetching company subscription info:', error);
    throw error;
  }

  return data;
}

/**
 * Update company subscription tier
 */
export async function updateCompanySubscriptionTier(
  companyId: string,
  tierIdOrName: string
): Promise<void> {
  // Check if tierIdOrName is a UUID or a name
  const isUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tierIdOrName);

  let tierId = tierIdOrName;

  // If it's a name, get the ID
  if (!isUuid) {
    const tier = await getSubscriptionTierByName(tierIdOrName);
    if (!tier) {
      throw new Error(`Subscription tier "${tierIdOrName}" not found`);
    }
    tierId = tier.id;
  }

  const { error } = await supabase
    .from('companies')
    .update({ subscription_tier_id: tierId })
    .eq('id', companyId);

  if (error) {
    console.error('Error updating company subscription tier:', error);
    throw error;
  }
}

/**
 * Update company subscription overrides
 */
export async function updateCompanySubscriptionOverrides(
  companyId: string,
  overrides: SubscriptionOverrides
): Promise<void> {
  const { error } = await supabase
    .from('companies')
    .update(overrides)
    .eq('id', companyId);

  if (error) {
    console.error('Error updating company subscription overrides:', error);
    throw error;
  }
}

/**
 * Check if a company can add more monitors
 */
export async function canAddMonitor(companyId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .rpc('can_add_monitor', { input_company_id: companyId });

    if (error) {
      console.error('Error checking if company can add monitor:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in canAddMonitor:', error);
    // Return true as a fallback to prevent blocking monitor creation if the function fails
    return true;
  }
}

/**
 * Get the maximum number of monitors a company can have
 */
export async function getCompanyMaxMonitors(companyId: string): Promise<number> {
  const { data, error } = await supabase
    .rpc('get_company_max_monitors', { company_id: companyId });

  if (error) {
    console.error('Error getting company max monitors:', error);
    throw error;
  }

  return data;
}

/**
 * Get the history retention days for a company
 */
export async function getCompanyHistoryRetentionDays(companyId: string): Promise<number> {
  const { data, error } = await supabase
    .rpc('get_company_history_retention_days', { company_id: companyId });

  if (error) {
    console.error('Error getting company history retention days:', error);
    throw error;
  }

  return data;
}

/**
 * Check if a company can add more monitors and get detailed limit information
 */
export async function checkCompanyMonitorLimit(companyId: string): Promise<{
  canAdd: boolean;
  currentCount: number;
  maxMonitors: number;
  monitorsAvailable: number;
} | null> {
  try {
    // First check if the function exists with the expected parameter name
    const { data, error } = await supabase
      .rpc('check_company_monitor_limit', { company_id: companyId });

    if (error) {
      // If there's an error, try with the alternative parameter name
      console.log('Trying alternative parameter name for check_company_monitor_limit');
      const { data: altData, error: altError } = await supabase
        .rpc('check_company_monitor_limit', { input_company_id: companyId });

      if (altError) {
        console.error('Error checking company monitor limit with both parameter names:', altError);
        throw altError;
      }

      return {
        canAdd: altData.can_add,
        currentCount: altData.current_count,
        maxMonitors: altData.max_monitors,
        monitorsAvailable: altData.monitors_available
      };
    }

    return {
      canAdd: data.can_add,
      currentCount: data.current_count,
      maxMonitors: data.max_monitors,
      monitorsAvailable: data.monitors_available
    };
  } catch (error) {
    console.error('Error in checkCompanyMonitorLimit:', error);
    // Return a default value that won't block monitor creation
    return {
      canAdd: true,
      currentCount: 0,
      maxMonitors: 100,
      monitorsAvailable: 100
    };
  }
}

/**
 * Get all subscription features
 */
export async function getSubscriptionFeatures(): Promise<SubscriptionFeature[]> {
  const { data, error } = await supabase
    .from('subscription_features')
    .select('*')
    .order('name');

  if (error) {
    console.error('Error fetching subscription features:', error);
    throw error;
  }

  return data || [];
}

/**
 * Create a new subscription feature (superadmin only)
 */
export async function createSubscriptionFeature(
  feature: Omit<SubscriptionFeature, 'id' | 'created_at' | 'updated_at'>
): Promise<SubscriptionFeature> {
  const { data, error } = await supabase
    .from('subscription_features')
    .insert(feature)
    .select()
    .single();

  if (error) {
    console.error('Error creating subscription feature:', error);
    throw error;
  }

  return data;
}

/**
 * Update a subscription feature (superadmin only)
 */
export async function updateSubscriptionFeature(
  id: string,
  updates: Partial<Omit<SubscriptionFeature, 'id' | 'created_at' | 'updated_at'>>
): Promise<SubscriptionFeature> {
  const { data, error } = await supabase
    .from('subscription_features')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error('Error updating subscription feature:', error);
    throw error;
  }

  return data;
}

/**
 * Delete a subscription feature (superadmin only)
 */
export async function deleteSubscriptionFeature(id: string): Promise<void> {
  const { error } = await supabase
    .from('subscription_features')
    .delete()
    .eq('id', id);

  if (error) {
    console.error('Error deleting subscription feature:', error);
    throw error;
  }
}

/**
 * Get features for a specific tier
 */
export async function getTierFeatures(tierId: string): Promise<any[]> {
  const { data, error } = await supabase
    .rpc('get_tier_features', { p_tier_id: tierId });

  if (error) {
    console.error('Error fetching tier features:', error);
    throw error;
  }

  return data || [];
}

/**
 * Update a tier feature (superadmin only)
 */
export async function updateTierFeature(
  tierId: string,
  featureId: string,
  value: string,
  isEnabled: boolean
): Promise<TierFeature> {
  // Check if the tier-feature relationship exists
  const { data: existing, error: checkError } = await supabase
    .from('tier_features')
    .select('*')
    .eq('tier_id', tierId)
    .eq('feature_id', featureId)
    .maybeSingle();

  if (checkError) {
    console.error('Error checking tier feature:', checkError);
    throw checkError;
  }

  if (existing) {
    // Update existing relationship
    const { data, error } = await supabase
      .from('tier_features')
      .update({ value, is_enabled: isEnabled })
      .eq('tier_id', tierId)
      .eq('feature_id', featureId)
      .select()
      .single();

    if (error) {
      console.error('Error updating tier feature:', error);
      throw error;
    }

    return data;
  } else {
    // Create new relationship
    const { data, error } = await supabase
      .from('tier_features')
      .insert({
        tier_id: tierId,
        feature_id: featureId,
        value,
        is_enabled: isEnabled
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating tier feature:', error);
      throw error;
    }

    return data;
  }
}
