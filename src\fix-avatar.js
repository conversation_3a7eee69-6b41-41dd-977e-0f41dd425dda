// This script will directly update the user's avatar URL in Supabase
// Run this in the browser console

async function fixUserAvatar() {
  console.log('Starting avatar fix...');
  
  // Get the Supabase client
  const supabase = window.supabase;
  if (!supabase) {
    console.error('Supabase client not found. Make sure you run this in the browser console while on the app.');
    return;
  }
  
  // Check if the user is logged in
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    console.error('User not logged in. Please log in first.');
    return;
  }
  
  console.log('Current user:', user);
  console.log('Current user metadata:', user.user_metadata);
  
  // Check if the avatars bucket exists
  console.log('Checking if avatars bucket exists...');
  const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();
  
  if (bucketsError) {
    console.error('Error listing buckets:', bucketsError);
    return;
  }
  
  const avatarBucket = buckets.find(b => b.name === 'avatars');
  if (!avatarBucket) {
    console.error('Avatars bucket does not exist! Please run the supabase_storage_setup.sql script.');
    return;
  }
  
  console.log('Avatars bucket exists:', avatarBucket);
  
  // List files in the avatars bucket
  console.log('Listing files in avatars bucket...');
  const { data: files, error: filesError } = await supabase.storage
    .from('avatars')
    .list();
    
  if (filesError) {
    console.error('Error listing files:', filesError);
    return;
  }
  
  console.log('Files in avatars bucket:', files);
  
  // Look for files that match the user's ID
  const userFiles = files.filter(file => file.name.includes(user.id));
  console.log('Files matching user ID:', userFiles);
  
  if (userFiles.length === 0) {
    console.log('No avatar files found for this user. Let\'s upload a test image.');
    
    // Create a simple test image (1x1 pixel transparent PNG)
    const base64Image = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    const byteCharacters = atob(base64Image);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: 'image/png' });
    const file = new File([blob], 'test-avatar.png', { type: 'image/png' });
    
    // Upload the test image
    console.log('Uploading test image...');
    const fileName = `${user.id}.png`;
    
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('avatars')
      .upload(fileName, file, { upsert: true });
    
    if (uploadError) {
      console.error('Error uploading test image:', uploadError);
      return;
    }
    
    console.log('Test image uploaded successfully:', uploadData);
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(fileName);
    
    const avatarUrl = urlData.publicUrl;
    console.log('Generated avatar URL:', avatarUrl);
    
    // Update the user's metadata with the avatar URL
    console.log('Updating user metadata with avatar URL...');
    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: {
        avatar_url: avatarUrl,
      },
    });
    
    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      return;
    }
    
    console.log('User metadata updated successfully:', userData);
    console.log('New user metadata:', userData.user.user_metadata);
    
    // Verify the update
    const { data: { user: updatedUser } } = await supabase.auth.getUser();
    console.log('Updated user:', updatedUser);
    console.log('Updated user metadata:', updatedUser.user_metadata);
    
    // Create a preview of the avatar
    const img = new Image();
    img.src = avatarUrl;
    img.onload = () => {
      console.log('Avatar image loaded successfully!');
      console.log('Avatar image preview:', img);
      
      // Create a div to display the image on the page
      const div = document.createElement('div');
      div.style.position = 'fixed';
      div.style.top = '10px';
      div.style.right = '10px';
      div.style.zIndex = '9999';
      div.style.background = 'white';
      div.style.padding = '10px';
      div.style.border = '1px solid black';
      div.style.borderRadius = '5px';
      div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
      
      const imgElement = document.createElement('img');
      imgElement.src = avatarUrl;
      imgElement.style.width = '100px';
      imgElement.style.height = '100px';
      imgElement.style.borderRadius = '50%';
      imgElement.style.objectFit = 'cover';
      
      const message = document.createElement('p');
      message.textContent = 'Avatar fixed! Please refresh the page.';
      message.style.marginTop = '10px';
      message.style.fontSize = '14px';
      
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Close';
      closeButton.style.marginTop = '10px';
      closeButton.style.padding = '5px';
      closeButton.style.cursor = 'pointer';
      closeButton.onclick = () => document.body.removeChild(div);
      
      div.appendChild(imgElement);
      div.appendChild(message);
      div.appendChild(closeButton);
      
      document.body.appendChild(div);
    };
    
    img.onerror = () => {
      console.error('Error loading avatar image. The URL might not be publicly accessible.');
    };
    
    return {
      success: true,
      message: 'Avatar fixed! Please refresh the page.',
      avatarUrl,
    };
  } else {
    // Use the most recent file
    const latestFile = userFiles.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())[0];
    console.log('Using latest file:', latestFile);
    
    // Get the public URL
    const { data: urlData } = supabase.storage
      .from('avatars')
      .getPublicUrl(latestFile.name);
    
    const avatarUrl = urlData.publicUrl;
    console.log('Generated avatar URL:', avatarUrl);
    
    // Update the user's metadata with the avatar URL
    console.log('Updating user metadata with avatar URL...');
    const { data: userData, error: updateError } = await supabase.auth.updateUser({
      data: {
        avatar_url: avatarUrl,
      },
    });
    
    if (updateError) {
      console.error('Error updating user metadata:', updateError);
      return;
    }
    
    console.log('User metadata updated successfully:', userData);
    console.log('New user metadata:', userData.user.user_metadata);
    
    // Verify the update
    const { data: { user: updatedUser } } = await supabase.auth.getUser();
    console.log('Updated user:', updatedUser);
    console.log('Updated user metadata:', updatedUser.user_metadata);
    
    // Create a preview of the avatar
    const img = new Image();
    img.src = avatarUrl;
    img.onload = () => {
      console.log('Avatar image loaded successfully!');
      console.log('Avatar image preview:', img);
      
      // Create a div to display the image on the page
      const div = document.createElement('div');
      div.style.position = 'fixed';
      div.style.top = '10px';
      div.style.right = '10px';
      div.style.zIndex = '9999';
      div.style.background = 'white';
      div.style.padding = '10px';
      div.style.border = '1px solid black';
      div.style.borderRadius = '5px';
      div.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.5)';
      
      const imgElement = document.createElement('img');
      imgElement.src = avatarUrl;
      imgElement.style.width = '100px';
      imgElement.style.height = '100px';
      imgElement.style.borderRadius = '50%';
      imgElement.style.objectFit = 'cover';
      
      const message = document.createElement('p');
      message.textContent = 'Avatar fixed! Please refresh the page.';
      message.style.marginTop = '10px';
      message.style.fontSize = '14px';
      
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Close';
      closeButton.style.marginTop = '10px';
      closeButton.style.padding = '5px';
      closeButton.style.cursor = 'pointer';
      closeButton.onclick = () => document.body.removeChild(div);
      
      div.appendChild(imgElement);
      div.appendChild(message);
      div.appendChild(closeButton);
      
      document.body.appendChild(div);
    };
    
    img.onerror = () => {
      console.error('Error loading avatar image. The URL might not be publicly accessible.');
    };
    
    return {
      success: true,
      message: 'Avatar fixed! Please refresh the page.',
      avatarUrl,
    };
  }
}

// Run the fix
fixUserAvatar().then(result => {
  console.log('Fix completed:', result);
});
