import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { formatChartDate } from '@/utils/dateFormat';
import { toast } from '@/components/ui/use-toast';

interface MonitorHistory {
  id: string;
  monitor_id: string;
  status: boolean | string;
  response_time: number | null;
  error_message: string | null;
  timestamp: string;
  status_code?: number;
}

interface UptimeStats {
  uptime24h: number;
  uptime7d: number;
  avgResponseTime: number;
}

interface ChartDataPoint {
  time: string;
  value: number;
  date: Date;
}

export function useMonitorDetail(monitorId: string, retentionDays: number) {
  const [history, setHistory] = useState<MonitorHistory[]>([]);
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [uptimeStats, setUptimeStats] = useState<UptimeStats>({
    uptime24h: 0,
    uptime7d: 0,
    avgResponseTime: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  const fetchHistory = async () => {
    if (!monitorId) return;

    setIsLoading(true);
    try {
      // Calculate date range based on retention period
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - retentionDays);

      const { data, error } = await supabase
        .from('monitor_history')
        .select('*')
        .eq('monitor_id', monitorId)
        .gte('timestamp', startDate.toISOString())
        .lte('timestamp', endDate.toISOString())
        .order('timestamp', { ascending: false })
        .limit(1000);

      if (error) throw error;

      setHistory(data || []);

      // Prepare chart data - use all available data points
      const chartData = data ? data
        .reverse() // Reverse to show oldest first
        .map((item: MonitorHistory) => {
          const date = new Date(item.timestamp);
          // Format the time differently based on the retention period
          const showTime = retentionDays <= 1;
          return {
            time: formatChartDate(date, showTime),
            value: item.response_time || 0,
            date: date // Store the full date for tooltip
          };
        }) : [];

      setChartData(chartData);

      // Calculate uptime stats
      calculateUptimeStats(data || []);
    } catch (err) {
      console.error('Error fetching monitor history:', err);
      toast({
        title: 'Error',
        description: 'Failed to fetch monitor history',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate uptime statistics
  const calculateUptimeStats = (historyData: MonitorHistory[]) => {
    if (!historyData.length) {
      setUptimeStats({
        uptime24h: 0,
        uptime7d: 0,
        avgResponseTime: 0
      });
      return;
    }

    const now = new Date();
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    const history24h = historyData.filter(h => new Date(h.timestamp) >= oneDayAgo);
    const history7d = historyData.filter(h => new Date(h.timestamp) >= sevenDaysAgo);

    const uptime24h = history24h.length ?
      (history24h.filter(h => {
        return typeof h.status === 'string' ? h.status === 'up' : h.status;
      }).length / history24h.length) * 100 : 0;

    const uptime7d = history7d.length ?
      (history7d.filter(h => {
        return typeof h.status === 'string' ? h.status === 'up' : h.status;
      }).length / history7d.length) * 100 : 0;

    const responseTimes = historyData
      .filter(h => h.response_time !== null)
      .map(h => h.response_time as number);

    const avgResponseTime = responseTimes.length ?
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

    setUptimeStats({
      uptime24h,
      uptime7d,
      avgResponseTime
    });
  };

  // Load history data when component mounts or ID changes
  useEffect(() => {
    if (monitorId && retentionDays) {
      fetchHistory();
    }
  }, [monitorId, retentionDays]);

  return {
    history,
    chartData,
    uptimeStats,
    isLoading,
    fetchHistory
  };
}
