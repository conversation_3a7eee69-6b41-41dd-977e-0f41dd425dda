-- This script migrates existing monitors to the new company-based system
-- Run this in the Supabase SQL Editor after running the company_tables.sql script

-- Create a default company for each user
INSERT INTO public.companies (name, description)
SELECT 
    CONCAT(u.email, '''s Company'),
    CONCAT('Default company for ', u.email)
FROM 
    auth.users u
WHERE 
    NOT EXISTS (
        SELECT 1 FROM public.company_members cm
        JOIN public.companies c ON cm.company_id = c.id
        WHERE cm.user_id = u.id
    );

-- Add each user as an admin to their default company
WITH user_companies AS (
    INSERT INTO public.companies (name, description)
    SELECT 
        CONCAT(u.email, '''s Company'),
        CONCAT('Default company for ', u.email)
    FROM 
        auth.users u
    WHERE 
        NOT EXISTS (
            SELECT 1 FROM public.company_members cm
            WHERE cm.user_id = u.id
        )
    RETURNING id, (
        SELECT id FROM auth.users WHERE email = SUBSTRING(name FROM 1 FOR POSITION('''s Company' IN name) - 1)
    ) AS user_id
)
INSERT INTO public.company_members (company_id, user_id, role)
SELECT id, user_id, 'admin'
FROM user_companies;

-- Migrate existing monitors to be associated with the user's default company
UPDATE public.monitors m
SET company_id = (
    SELECT c.id
    FROM public.companies c
    JOIN public.company_members cm ON c.id = cm.company_id
    WHERE cm.user_id = m.user_id
    LIMIT 1
)
WHERE m.company_id IS NULL;

-- Verify the migration
SELECT 
    c.name AS company_name,
    u.email AS user_email,
    cm.role,
    COUNT(m.id) AS monitor_count
FROM 
    public.companies c
JOIN 
    public.company_members cm ON c.id = cm.company_id
JOIN 
    auth.users u ON cm.user_id = u.id
LEFT JOIN 
    public.monitors m ON c.id = m.company_id
GROUP BY 
    c.name, u.email, cm.role
ORDER BY 
    c.name;
